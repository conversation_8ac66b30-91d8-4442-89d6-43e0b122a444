## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
#Wed Mar 19 09:27:14 WET 2025
ARTIFACTORY_PWD=AKCp5dKZ1VWBG3itCt1AJY37pGc7m67r8LdYrmjUekJUrXuJ46TWYjDirhwZQywNRY1Gn95KN
ARTIFACTORY_USER=<EMAIL>
DTXLogLevel=debug
DTXMaxDexMethods=65535
DTXMultidexMoveMethodCount=65385
android.nonFinalResIds=false
android.nonTransitiveRClass=false
android.useAndroidX=true
org.gradle.daemon=true
org.gradle.jvmargs=-Xmx4048M -Dkotlin.daemon.jvm.options\="-Xmx4048M" --add-exports\=java.base/sun.nio.ch\=ALL-UNNAMED --add-opens\=java.base/java.lang\=ALL-UNNAMED --add-opens\=java.base/java.lang.reflect\=ALL-UNNAMED --add-opens\=java.base/java.io\=ALL-UNNAMED --add-exports\=jdk.unsupported/sun.misc\=ALL-UNNAMED
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configuration-cache=true
