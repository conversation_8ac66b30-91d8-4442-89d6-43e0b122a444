{"project_info": {"project_number": "647240640772", "firebase_url": "https://avito-f0026.firebaseio.com", "project_id": "avito-f0026", "storage_bucket": "avito-f0026.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:647240640772:android:867805efd5b4673e", "android_client_info": {"package_name": "se.scmv.morocco"}}, "oauth_client": [{"client_id": "647240640772-rin3tpm940stiruk4a9f21p4ieu7vmj5.apps.googleusercontent.com", "client_type": 3}, {"client_id": "647240640772-sf0sf8kj6ber8nfq16ha2ff3agj8quqe.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD05ZT5QWowdUc2_KXvYoI6JSjXYXf_dto"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:647240640772:android:867805efd5b4673e", "android_client_info": {"package_name": "se.scmv.morocco.debug"}}, "oauth_client": [{"client_id": "647240640772-rin3tpm940stiruk4a9f21p4ieu7vmj5.apps.googleusercontent.com", "client_type": 3}, {"client_id": "647240640772-sf0sf8kj6ber8nfq16ha2ff3agj8quqe.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD05ZT5QWowdUc2_KXvYoI6JSjXYXf_dto"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}], "configuration_version": "1"}