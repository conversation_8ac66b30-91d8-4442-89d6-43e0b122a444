package se.scmv.morocco.core

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import se.scmv.morocco.Avito
import se.scmv.morocco.R
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.ad.vas.master.IntentParams
import se.scmv.morocco.avitov2.adview.presentation.AdViewActivity
import se.scmv.morocco.avitov2.vas.presentation.master.NewVasActivity
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.messaging.utils.MessagingUtils
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.isNotNull

class AvitoPushNotificationsService : FirebaseMessagingService() {
        override fun onNewToken(token: String) {
                val oldToken = Utils.getStringPreference(
                        Avito.context,
                        Constants.FIREBASE_MESSAGING_TOKEN
                )
                if (token == oldToken) {
                        return
                }
                Utils.savePreference(Avito.context, Constants.FIREBASE_MESSAGING_TOKEN, token)
                if (AccountToken.isLoggedIn(Avito.context) && !AccountToken.isSessionExpired(Avito.context))
                        MessagingUtils.registerFirebaseToken(token, oldToken)
                super.onNewToken(token)
        }

        override fun onMessageReceived(remoteMessage: RemoteMessage) {
                super.onMessageReceived(remoteMessage)
                sendNotification(remoteMessage)
        }

        private fun isAvitoPushNotification(remoteMessage: RemoteMessage): Boolean {
                return !remoteMessage.data["body"].isNullOrEmpty() && !remoteMessage.data["title"].isNullOrEmpty() && (!remoteMessage.data["redirect_to_my_ad_id"].isNullOrEmpty() || !remoteMessage.data["redirect_to_list_id"].isNullOrEmpty() || remoteMessage.data["redirect_to_my_account"].isNotNull() || remoteMessage.data["redirect_to_ad_listing_vas"].isNotNull())
        }

        private fun sendNotification(remoteMessage: RemoteMessage) {

                if (!isAvitoPushNotification(remoteMessage))
                        return

                val adId = remoteMessage.data["redirect_to_my_ad_id"]
                val listIdAdView = remoteMessage.data["redirect_to_list_id"]
                val vasAdId = remoteMessage.data["redirect_to_ad_listing_vas"]
                val vasPackId = remoteMessage.data["redirect_to_ad_listing_vas_pack_id"]
                val messageType = remoteMessage.data["type"]
                val notificationBody = remoteMessage.data["body"]

                if (remoteMessage.data["body"].isNullOrEmpty() && remoteMessage.data["title"].isNullOrEmpty())
                        return

                val flag = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M)
                        PendingIntent.FLAG_UPDATE_CURRENT
                else
                        PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT

                val pendingIntent = if (!listIdAdView.isNullOrEmpty()) {
                        val adDetailsIntent = Intent(Avito.context, AdViewActivity::class.java)
                        adDetailsIntent.putExtra(
                                Keys.AD_ID,
                                listIdAdView
                        )
                        adDetailsIntent.putExtra(
                                Keys.LIST_ID_KEY_FROM_NOTIFICATION,
                                Integer.valueOf(listIdAdView)
                        )
                        adDetailsIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        PendingIntent.getActivity(
                                Avito.context,
                                1,
                                adDetailsIntent,
                                flag
                        )


                } else if (!vasAdId.isNullOrEmpty()) {
                        val vasIntent = Intent(Avito.context, NewVasActivity::class.java)
                        val intentParams = IntentParams(
                                from = IntentParams.From.Notification("firebase"),
                                adId = vasAdId,
                                application = VasPacksApplication.AD_LISTING,
                                chosenVasPackage = vasPackId,
                        )
                        vasIntent.putExtra(
                                NewVasActivity.INTENT_PARAMS_KEY,
                                intentParams
                        )
                        vasIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        PendingIntent.getActivity(
                                applicationContext,
                                1,
                                vasIntent,
                                flag
                        )
                } else {
                        val myAccountIntent = Intent(Avito.context, MainActivity::class.java)
                        myAccountIntent.putExtra(
                                "gotoMyAccount",
                                true
                        )
                        if (adId != null)
                                myAccountIntent.putExtra("my_ads_filter", messageType)
                        myAccountIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        myAccountIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        PendingIntent.getActivity(
                                Avito.context,
                                1,
                                myAccountIntent,
                                flag
                        )
                }
                val largeicon =
                        BitmapFactory.decodeResource(
                                Avito.context?.resources,
                                R.drawable.avito_logo
                        )
                val builder = Avito.context?.let { context ->
                        NotificationCompat.Builder(
                                context, Constants.NOTIFICATION_CHANNEL_ID
                        )
                }
                builder?.setAutoCancel(true)
                        ?.setDefaults(Notification.DEFAULT_LIGHTS or Notification.DEFAULT_VIBRATE)
                        ?.setPriority(NotificationCompat.PRIORITY_HIGH)
                        ?.setCategory(NotificationCompat.CATEGORY_MESSAGE)
                        ?.setWhen(System.currentTimeMillis())
                        ?.setLargeIcon(largeicon)
                        ?.setSmallIcon(R.drawable.ic_notification)
                        ?.setContentTitle(remoteMessage.data["title"])
                        ?.setStyle(
                                NotificationCompat.BigTextStyle()
                                        .bigText(notificationBody)
                        )
                        ?.setOnlyAlertOnce(false)
                        ?.setContentText(notificationBody)
                        ?.setContentIntent(pendingIntent)

                val notificationManagerCompat =
                        Avito.context?.let { NotificationManagerCompat.from(it) }

                builder?.let {

                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                val channelId = "Services"
                                val channel = NotificationChannel(
                                        channelId,
                                        "Services",
                                        NotificationManager.IMPORTANCE_HIGH
                                )
                                notificationManagerCompat?.createNotificationChannel(
                                        channel
                                )
                                builder.setChannelId(channelId)
                        }
                        notificationManagerCompat?.notify(
                                "Services",
                                (System.currentTimeMillis() / 1000).toInt(),
                                it.build()
                        )


                }

        }
}