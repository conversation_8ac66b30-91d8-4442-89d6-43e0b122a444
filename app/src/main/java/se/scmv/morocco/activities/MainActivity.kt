package se.scmv.morocco.activities

import android.Manifest
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeContentPadding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.os.bundleOf
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.core.view.GravityCompat
import androidx.core.view.isVisible
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import com.apollographql.apollo3.api.Optional
import com.braze.ui.inappmessage.BrazeInAppMessageManager
import com.bumptech.glide.Glide
import com.google.android.material.navigation.NavigationView
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.PendingDynamicLinkData
import com.google.gson.Gson
import com.microsoft.clarity.Clarity
import com.microsoft.clarity.ClarityConfig
import com.microsoft.clarity.models.LogLevel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.leolin.shortcutbadger.ShortcutBadger
import org.greenrobot.eventbus.Subscribe
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.R
import se.scmv.morocco.account.presentation.AccountFragment
import se.scmv.morocco.account.presentation.edit_account.EditAccountActivity
import se.scmv.morocco.account.presentation.myads.AccountAdsActivity
import se.scmv.morocco.activities.main.MainViewModel
import se.scmv.morocco.activities.main.MainViewState
import se.scmv.morocco.adstickybanner.AdServerService
import se.scmv.morocco.adstickybanner.LifecycleAdServerServiceConnection
import se.scmv.morocco.adstickybanner.presentation.BubbleOverlayAd
import se.scmv.morocco.adstickybanner.presentation.openUrl
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.analytics.AnalyticsStrings
import se.scmv.morocco.analytics.AnalyticsUtils
import se.scmv.morocco.analytics.utmtagscollector.UTMTagsRetriever.retrieveUTMTags
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.avitov2.adinsert.presentation.AdInsertActivity
import se.scmv.morocco.avitov2.favorites.master.presentation.MyFavoritesFragment
import se.scmv.morocco.avitov2.favorites.master.presentation.OnSavedSearchClickListener
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParamsWithLabels
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel
import se.scmv.morocco.avitov2.floatingsearchview.FloatingSearchView
import se.scmv.morocco.avitov2.floatingsearchview.suggestions.model.SearchSuggestion
import se.scmv.morocco.avitov2.floatingsearchview.suggestions.model.SearchSuggestionImpl
import se.scmv.morocco.avitov2.floatingsearchview.util.Util
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.common.lang.LocaleManager.changeToArabic
import se.scmv.morocco.core.repeatWithCreatedLifeCycle
import se.scmv.morocco.dao.DaoManager
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account.Connected.Private
import se.scmv.morocco.domain.models.Account.Connected.Shop
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.events.AuthenticationEvent
import se.scmv.morocco.events.BusEvent
import se.scmv.morocco.events.CloseDrawerEvent
import se.scmv.morocco.events.NavigationDrawerEvent
import se.scmv.morocco.events.SwitchToTab
import se.scmv.morocco.inappupdate.InAppUpdateUtils
import se.scmv.morocco.inappupdate.InAppUpdateUtils.Companion.resumeAppUpdate
import se.scmv.morocco.inappupdate.InAppUpdateUtils.Companion.updateApp
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants.MESSAGING_NOTIFICATION_CONVERSATION_ID_LABEL_FOREGROUND
import se.scmv.morocco.messaging.common.Constants.MESSAGING_NOTIFICATION_TYPE
import se.scmv.morocco.messaging.common.Constants.MESSAGING_NOTIFICATION_TYPE_LABEL_FOREGROUND
import se.scmv.morocco.messaging.common.currentOpenedConversationId
import se.scmv.morocco.messaging.ui.messaginglist.MessagingListFragment
import se.scmv.morocco.stats.fragments.StatsFragment
import se.scmv.morocco.type.AdLocationFilter
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.type.AdParamsListMatchFilters
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.ui.AvitoNavHost
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import se.scmv.morocco.ui.rememberAppState
import se.scmv.morocco.urlsprovider.AppEnvironmentsBottomSheet
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.EventBusManager
import se.scmv.morocco.utils.FlavorUtils
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.utils.Mapping
import se.scmv.morocco.utils.NightModeUtils
import se.scmv.morocco.utils.NotifyUtils.displayDefaultSnackbar
import se.scmv.morocco.utils.NotifyUtils.displaySnackbar
import se.scmv.morocco.utils.NotifyUtils.displaySuccessSnackbar
import se.scmv.morocco.utils.StringUtils.isNumeric
import se.scmv.morocco.utils.URLUtils
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.isNotEmpty
import se.scmv.morocco.widgets.BottomNavigationBar
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AppCompatActivity(), OnSavedSearchClickListener {

    private val TAG = MainActivity::class.java.simpleName
    private var rootView: View? = null
    private var toolbar: androidx.appcompat.widget.Toolbar? = null

    // UI variables
    var pager: ViewPager? = null
        private set
    private val mFragmentsList: MutableList<Fragment> = ArrayList()

    private var mDrawerLayout: DrawerLayout? = null
    private var mDrawerToggle: ActionBarDrawerToggle? = null
    private var usernameLabel: TextView? = null
    private var memberSinceLabel: TextView? = null
    private var navigationView: NavigationView? = null
    private var filteringBar: ConstraintLayout? = null
    private var controlBarCity: TextView? = null
    private var controlBarCityThreeDots: TextView? = null

    private var stickyBanner: ImageView? = null
    private var notifAdComposeView: FrameLayout? = null

    private var isComingFromDeepLink = false

    private var bottomNavigationBar: BottomNavigationBar? = null
    var accountInfoUpdate: Runnable? = null


    //InAPP Update
    private var appUpdateManager: AppUpdateManager? = null
    private var updateAppItem: MenuItem? = null
    private var appEnvironmentItem: MenuItem? = null
    private var environmentsBottomSheet: AppEnvironmentsBottomSheet? = null
    private val mHandler: Handler = Handler()

    private val nmUtils = NightModeUtils(this)

    private var mSearchView: FloatingSearchView? = null
    private val mIsDarkSearchTheme = false

    private var hasToLoadFilters: Boolean = false
    private val daoManager: DaoManager = DaoManager.getInstance()

    private val mainViewModel: MainViewModel by viewModels()
    private val filtersSharedViewModel: ListingFiltersSharedViewModel by viewModels()

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    @Inject
    lateinit var accountRepository: AccountRepository

    private val requestPermission: ActivityResultLauncher<String> = registerForActivityResult(
        ActivityResultContracts.RequestPermission(), object : ActivityResultCallback<Boolean> {
            override fun onActivityResult(result: Boolean) {

            }
        }
    )

    private val adServerServiceConnection =
        LifecycleAdServerServiceConnection(this, AdServerService::class.java) {
            observeImageSlide(it)
            observeNotifAd(it)
        }

    override fun onCreate(savedInstanceState: Bundle?) {

        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)
        appUpdateManager = AppUpdateManagerFactory.create(this)
        splashScreen.setKeepOnScreenCondition {
            mainViewModel.viewState.value.shouldKeepSplashScreen()
        }
        setContent {
            AvitoTheme {
                val state = mainViewModel.viewState.collectAsStateWithLifecycle().value
                if (state is MainViewState.Success) {
                    val appState = rememberAppState(state.account)
                    Scaffold(
                        modifier = Modifier.safeContentPadding(),
                        snackbarHost = {
                            SnackBarHostForSnackBarController()
                        }
                    ) { paddingValues ->
                        AvitoNavHost(
                            appState = appState,
                            updateAppLanguage = { LocaleManager.switchLanguage() },
                            openContactSupport = { openContactSupport() },
                            onUpdateAppClicked = {

                            },
                            modifier = Modifier.fillMaxSize().padding(paddingValues)
                        )
                    }
                }
            }
        }
        langDialog()
        handleFirebaseDynamicLinks()
        appUpdateManager?.let { updateApp(this, it) }
        if (BuildConfig.DEBUG) {
            environmentsBottomSheet = AppEnvironmentsBottomSheet(context = this)
        }
        goToConversationFrom(intent)

        // fill category and region names in the control bar in case of deeplink
        handleOnLaunchDeepLink()
        checkNotificationPermission()
        val config = ClarityConfig(
            projectId = getString(R.string.clarity_project_id),
            logLevel = LogLevel.None
        )
        Clarity.initialize(
            applicationContext,
            config
        )
    }

    override fun onStart() {
        super.onStart()
        EventBusManager.instance?.register(this)
    }

    override fun onStop() {
        super.onStop()
        EventBusManager.instance?.unregister(this)
    }

    private fun observeImageSlide(service: AdServerService) {
        stickyBanner?.setOnClickListener {
            service.recordClick {
                Log.d("recordClick", it)
                URLUtils.openUrlInWebView(
                    context = this@MainActivity,
                    url = it,
                    toolbarTitle = it
                )
            }
        }
        lifecycleScope.launch {
            service.imageSlideFlow.collect {
                if (it.isNotEmpty()) {
                    stickyBanner?.visibility = View.VISIBLE
                    Glide.with(this@MainActivity)
                        .load(it)
                        .into(stickyBanner!!)
                } else {
                    stickyBanner?.visibility = View.GONE
                }
            }
        }
    }

    private fun observeNotifAd(service: AdServerService) {
        lifecycleScope.launch {
            service.notifAdFlow.collect { notifAd ->
                if (notifAd != null) {
                    notifAdComposeView?.visibility = View.VISIBLE
                    (notifAdComposeView?.getChildAt(0) as ComposeView).setContent {
                        Log.d(
                            "observeNotifAd",
                            "getChildAtCount: ${notifAdComposeView?.childCount}"
                        )
                        Log.d(
                            "observeNotifAd",
                            "getChildAtC: ${(notifAdComposeView?.getChildAt(0) as ComposeView).isVisible}"
                        )
                        BubbleOverlayAd(
                            notifAd = notifAd,
                            onFormSubmit = { name, phone, email, city ->
                                adServerServiceConnection.storeLeads(
                                    campaign = notifAd.leadsforceCampaign ?: "",
                                    name = name,
                                    phone = phone,
                                    email = email,
                                    city = city
                                )
                            },
                            onNotifAdClick = {
                                adServerServiceConnection.recordClick()
                                adServerServiceConnection.recordCreativeImpression()
                            }
                        ) {
                            adServerServiceConnection.recordCreativeClick()
                            it.openUrl(this@MainActivity)
                        }
                    }
                } else {
                    notifAdComposeView?.visibility = View.GONE
                }
            }
        }
    }

    private fun checkNotificationPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) ==
            PackageManager.PERMISSION_GRANTED
        ) {
            //Permission GRANTED
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                AlertDialog.Builder(this)
                    .setTitle(R.string.notification_ask_permission_message)
                    .setPositiveButton(R.string.notification_ask_permission_positive_text_button) { dialog, _ ->
                        dialog.dismiss()
                        requestPermission.launch(Manifest.permission.POST_NOTIFICATIONS)
                    }
                    .setNegativeButton(R.string.notification_ask_permission_negative_text_button) { dialog, _ ->
                        dialog.dismiss()
                    }.show()
            }
        }

    }

    private fun updateCityControlBar(paramsWithLabels: GetListingQueryParamsWithLabels) {
        // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
        val cityParam = paramsWithLabels.cityLabel
        val areaParam = paramsWithLabels.areaLabel
        controlBarCity?.text = if (!cityParam.isNullOrEmpty() || !areaParam.isNullOrEmpty()) {
            StringBuilder().apply {
                if (cityParam.isNullOrBlank().not()) {
                    append(cityParam)
                }
                if (areaParam.isNullOrBlank().not()) {
                    append(String.format(" : %s", areaParam))
                }
            }.toString()
        } else {
            getString(R.string.all_cities)
        }
    }

    private fun updateSearchTextFromFilters(it: String?) {
        if (it.isNullOrBlank() || mSearchView?.query.isNullOrBlank()) {
            mSearchView?.setSearchText(it)
        }
    }

    private fun setupFloatingSearch() {
        repeatWithCreatedLifeCycle {
            mainViewModel.searchSuggestions.collectLatest { suggestions: List<SearchSuggestionImpl> ->
                if (suggestions.isEmpty()) {
                    mSearchView?.hideProgress()
                } else {
                    mSearchView?.apply {
                        swapSuggestions(suggestions, getString(R.string.suggestions))
                        hideProgress()
                    }
                }
                controlBarCity?.text = getString(R.string.all_cities)

            }
        }
        mSearchView!!.setOnQueryChangeListener { oldQuery, newQuery ->
            if (newQuery == "" && oldQuery == "CLEAR") {
                val previousFilters = filtersSharedViewModel.previousFilters
                val updatedFilters = previousFilters.copy(
                    getListingQueryParams = previousFilters.getListingQueryParams.copy(text = null)
                )
                filtersSharedViewModel.onFiltersChanged(updatedFilters)

            } else if (oldQuery != "" && newQuery == "") {
                mSearchView!!.clearSuggestions(getString(R.string.recent_searches))
                mainViewModel.onSearchCleared()
            } else if (newQuery.length > 2 && oldQuery == "") {
                mSearchView!!.hideProgress()
            } else if (newQuery.length > 2 && newQuery != oldQuery) {
                mSearchView!!.showProgress()
                mainViewModel.onSearchChanged(newQuery)
            } else {
                mSearchView!!.hideProgress()
            }
        }

        mSearchView!!.setOnMenuClickListener(object :
            FloatingSearchView.OnLeftMenuClickListener {
            override fun onMenuOpened() {
                displayLeftSideBar()

            }

            override fun onMenuClosed() {}
        })


        mSearchView!!.setOnBindSuggestionCallback { suggestionView, leftIcon, textView, item, itemPosition ->
            val suggestion: SearchSuggestionImpl = item as SearchSuggestionImpl
            val textColor = if (mIsDarkSearchTheme) "#ffffff" else "#000000"
            val textLight = if (mIsDarkSearchTheme) "#bfbfbf" else "#787878"
            if (suggestion.isHistory) {
                leftIcon.setImageDrawable(
                    ResourcesCompat.getDrawable(
                        resources,
                        R.drawable.ic_history_black_24dp, null
                    )
                )
                Util.setIconColor(
                    leftIcon,
                    Color.parseColor(textColor)
                )
                leftIcon.alpha = .36f
            } else {
                leftIcon.alpha = 0.0f
                leftIcon.setImageDrawable(null)
            }
            textView.setTextColor(Color.parseColor(textColor))
            val text: String = suggestion.getBody(this).replaceFirst(
                oldValue = mSearchView!!.query,
                newValue = "<font color=\"" + textLight + "\">" + mSearchView!!.query + "</font>"
            )
            textView.text = Html.fromHtml(text)
        }

        mSearchView!!.setOnClearSearchActionListener {
            // displayFilterSideBar()
        }


        mSearchView?.setOnFocusChangeListener(
            object : FloatingSearchView.OnFocusChangeListener {
                override fun onFocus() {
                    // Retrieve recent searches from SharedPreferences
                    val recentSearches = getRecentSearches()


                    mSearchView?.apply {
                        swapSuggestions(recentSearches, getString(R.string.recent_searches))
                        hideProgress()
                    }
                }

                override fun onFocusCleared() {
                }

            }
        )
        mSearchView!!.setOnSearchListener(
            object : FloatingSearchView.OnSearchListener {
                override fun onSuggestionClicked(searchSuggestion: SearchSuggestion?) {
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                    searchSuggestion?.let { suggestion: SearchSuggestion ->
                        if (!suggestion.isHistory)
                            saveRecentSearch(
                                SearchSuggestionImpl(
                                    suggestion.suggestion,
                                    suggestion.keyword,
                                    suggestion.categoryName,
                                    suggestion.id,
                                    suggestion.adTypeName,
                                    suggestion.cityId,
                                    suggestion.cityName,
                                    suggestion.adTypeKey,
                                    suggestion.modelKey,
                                    suggestion.model,
                                    suggestion.modelName,
                                    suggestion.brandKey,
                                    suggestion.brand,
                                    suggestion.brandName,
                                    true
                                )
                            )
                        val previousFilters = filtersSharedViewModel.previousFilters
                        if (suggestion.id == previousFilters.getListingQueryParams.categoryId && suggestion.adTypeKey.equals(
                                previousFilters.getListingQueryParams.type?.name, ignoreCase = true
                            )
                        ) {
                            val updatedFilters = previousFilters.copy(
                                getListingQueryParams = previousFilters.getListingQueryParams.copy(
                                    text = suggestion.keyword,
                                    location = AdLocationFilter(
                                        cityIds = Optional.present(
                                            if (suggestion.cityId != 0)
                                                listOf(
                                                    suggestion.cityId
                                                )
                                            else
                                                emptyList()
                                        )
                                    ),
                                    params = previousFilters.getListingQueryParams.params?.copy(
                                        listMatch = Optional.present(
                                            AdParamsListMatchFilters(
                                                textList = Optional.present(
                                                    listOfNotNull(
                                                        suggestion.brand.takeIf { !it.isNullOrBlank() }
                                                            ?.let {
                                                                AdParamListTextFilter(
                                                                    name = suggestion.brandKey,
                                                                    value = listOf(it)
                                                                )
                                                            },
                                                        suggestion.model.takeIf { !it.isNullOrBlank() }
                                                            ?.let {
                                                                AdParamListTextFilter(
                                                                    name = suggestion.modelKey,
                                                                    value = listOf(it)
                                                                )
                                                            }
                                                    )
                                                )
                                            )
                                        )
                                    )
                                ),
                                cityLabel = suggestion.cityName
                            )
                            filtersSharedViewModel.onFiltersChanged(updatedFilters)

                            applyFiltersOnOpen(suggestion)
                        } else {
                            hasToLoadFilters = true
                            filtersSharedViewModel.onSearchSuggestionSelected(
                                id = suggestion.id,
                                type = suggestion.adTypeKey,
                                suggestion = suggestion.keyword
                            )
                            val listingQueryParams = GetListingQueryParams(
                                categoryId = suggestion.id,
                                type = try {
                                    AdTypeKey.valueOf(suggestion.adTypeKey.uppercase())
                                } catch (e: Exception) {
                                    null
                                },
                                location = AdLocationFilter(
                                    cityIds = Optional.present(
                                        if (suggestion.cityId != 0)
                                            listOf(
                                                suggestion.cityId
                                            )
                                        else
                                            emptyList()
                                    )
                                ),
                                text = suggestion.keyword,
                                hasPrice = false,
                                hasImage = false,
                                params = ListingAdParamsFilters(
                                    listMatch = Optional.present(
                                        AdParamsListMatchFilters(
                                            textList = Optional.present(
                                                listOfNotNull(
                                                    suggestion.brand.takeIf { !it.isNullOrBlank() }
                                                        ?.let {
                                                            AdParamListTextFilter(
                                                                name = suggestion.brandKey,
                                                                value = listOf(it)
                                                            )
                                                        },
                                                    suggestion.model.takeIf { !it.isNullOrBlank() }
                                                        ?.let {
                                                            AdParamListTextFilter(
                                                                name = suggestion.modelKey,
                                                                value = listOf(it)
                                                            )
                                                        }
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                            val queryParamsWithLabels = GetListingQueryParamsWithLabels(
                                getListingQueryParams = listingQueryParams,
                                cityLabel = suggestion.cityName,
                                areaLabel = null
                            )
                            filtersSharedViewModel.onFiltersChanged(queryParamsWithLabels)

                            applyFiltersOnOpen(suggestion)
                        }
                    }
                    mSearchView?.clearSuggestions(getString(R.string.suggestions))
                }

                override fun onSearchAction(currentQuery: String?) {
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                    currentQuery?.let {
                        val previousFilters = filtersSharedViewModel.previousFilters
                        val updatedFilters = previousFilters.copy(
                            getListingQueryParams = previousFilters.getListingQueryParams.copy(text = it)
                        )
                        filtersSharedViewModel.onFiltersChanged(updatedFilters)

                        saveRecentSearch(
                            SearchSuggestionImpl(
                                it,
                                it,
                                "",
                                0,
                                "",
                                0,
                                "",
                                "",
                                "",
                                "",
                                "",
                                "",
                                "",
                                "",
                                true
                            )
                        )
                    }
                    mSearchView?.clearSuggestions(getString(R.string.suggestions))
                    mainViewModel.onSearchCleared()
                }
            }
        )
    }

    private fun applyFiltersOnOpen(suggestion: SearchSuggestion) {
        hasToLoadFilters = true
        val reverseMapParams = reverseMapParams(suggestion.getSavedSearchQuery()).toMutableMap()
        filtersSharedViewModel.onApplyFiltersFromSaveSearchOrDeeplink(reverseMapParams)

    }

    /**
     * -----------------------------------
     * Bottom Navigation bar click listener :  HOME | PROFILE ( AD INSERT ) MESSAGING | FAVORITES
     * -----------------------------------
     */
    private fun trackBottomNavigation(tabPosition: Int) {
        val manager = AnalyticsManager.instance
        var tag: String? = null
        val level2Site = 0
        when (tabPosition) {
            BottomNavigationBar.NAVIGATION_POSITION_HOME -> {
                // TODO : track home tab
            }

            BottomNavigationBar.NAVIGATION_POSITION_FAVORITES_STATS -> {
                tag = getString(R.string.tm_event_my_account_tab_visited)
                if (isComingFromDeepLink) manager?.logEvent(
                    tag,
                    mutableMapOf(),
                    true
                )
            }

            BottomNavigationBar.NAVIGATION_POSITION_MESSAGING -> if (!isComingFromDeepLink) {
                tag = getString(R.string.tm_event_messaging_tab_visited)
                trackMessaging("Listing tab")
            } else {
                val eventProperties = mutableMapOf<String?, String?>()
                if (AccountToken.isLoggedIn(this)) {
                    val currentAccount = Account.getCurrentAccount(this)
                    eventProperties.putAll(
                        AnalyticsUtils.generateMapFromAccountObject(
                            currentAccount
                        )
                    )
                }
                tag = getString(R.string.tm_event_messaging_tab_visited)
                if (isComingFromDeepLink) manager?.logEvent(
                    tag,
                    eventProperties,
                    true
                )
            }

            BottomNavigationBar.NAVIGATION_POSITION_PROFILE -> {
                tag = getString(R.string.tm_event_notification_tab_visited)
                if (isComingFromDeepLink) manager?.logEvent(
                    tag,
                    mutableMapOf(),
                    true
                )
            }
        }
        isComingFromDeepLink = false
        tag?.let {
            FirebaseAnalytics.getInstance(this).logEvent(
                getString(R.string.gtm_open_screen),
                bundleOf(getResources().getString(R.string.gtm_screen_label) to it)
            )
        }
    }

    /**
     * -----------------------------------
     * Firebase Dynamic links handler
     * -----------------------------------
     * Must be executed only when first installed
     */
    private fun handleFirebaseDynamicLinks() {
        if (Utils.getBooleanPreference(
                this,
                Utils.PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL,
                true
            )
        ) {
            FirebaseDynamicLinks.getInstance().getDynamicLink(intent)
                .addOnSuccessListener(this) { pendingDynamicLinkData: PendingDynamicLinkData? ->
                    var deepLink: Uri? = null
                    if (pendingDynamicLinkData != null) {
                        deepLink = pendingDynamicLinkData.link
                    }
                    if (deepLink != null) {
                        Utils.savePreference(
                            this,
                            Utils.PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL,
                            false
                        )
                        startActivity(Intent(Intent.ACTION_VIEW, deepLink))
                    }
                }
        }
    }

    override fun onResume() {
        super.onResume()
        refreshAccountControls()
        handleOnLaunchDeepLink()
        appUpdateManager?.let { resumeAppUpdate(this, it) }
        setUpdateItemVisibility()
        setEnvironmentItemVisibility()
        switchFavoritesStatsItemVisibility()
        mSearchView?.setSearchHint(getString(R.string.search_field_hint))
        BrazeInAppMessageManager.getInstance().registerInAppMessageManager(this)
    }

    override fun onPause() {
        super.onPause()
        accountInfoUpdate?.let { mHandler.removeCallbacks(it) }
        BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(this)
    }

    private fun langDialog() {
        if (!Utils.getBooleanPreference(this, "popupSwitch")) {
            showlangPopup()
        }
    }

    private fun showlangPopup() {
        val builder: AlertDialog.Builder =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AlertDialog.Builder(this, R.style.AlertDialogCustom)
            } else {
                AlertDialog.Builder(this)
            }
        builder.setTitle(R.string.lang_popup_title)
        builder.setMessage(R.string.lang_popup_message)
        builder.setPositiveButton(resources.getString(R.string.french_language)) { _, _ ->
            Utils.savePreference(this@MainActivity, "popupSwitch", true)
            FirebaseAnalytics
                .getInstance(this)
                .setUserProperty("lang", LocaleManager.getCurrentLanguage())
            FirebaseAnalytics.getInstance(this).logEvent(
                getString(R.string.tm_event_switch_to_french_popup),
                bundleOf()
            )
        }
        builder.setNegativeButton(resources.getString(R.string.arabic_language)) { _, _ ->
            Utils.savePreference(this@MainActivity, "popupSwitch", true)
            FirebaseAnalytics
                .getInstance(this)
                .setUserProperty("lang", LocaleManager.getCurrentLanguage())
            FirebaseAnalytics.getInstance(this).logEvent(
                getString(R.string.tm_event_switch_to_arabic_popup),
                bundleOf()
            )
            changeToArabic()
            Utils.rebirthApp(this)
        }
        builder.show()
    }

    fun getRecentSearches(): List<SearchSuggestionImpl> {
        val sharedPreferences = this.getSharedPreferences("recent_searches", MODE_PRIVATE)
        val gson = Gson()

        // Get the JSON string from SharedPreferences (defaults to an empty list if not found)
        val recentSearchesJson = sharedPreferences.getString("search_list", "[]") ?: "[]"

        // Deserialize the JSON string directly into a list of SearchSuggestionImpl objects
        return gson.fromJson(recentSearchesJson, Array<SearchSuggestionImpl>::class.java).toList()
    }

    fun saveRecentSearch(suggestion: SearchSuggestionImpl) {
        val sharedPreferences = this.getSharedPreferences("recent_searches", MODE_PRIVATE)
        val gson = Gson()

        // Retrieve the current list of recent searches
        val recentSearchesJson = sharedPreferences.getString("search_list", "[]") ?: "[]"
        val recentSearches =
            gson.fromJson(recentSearchesJson, Array<SearchSuggestionImpl>::class.java)
                .toMutableList()

        // Add the new search suggestion to the front (most recent)
        recentSearches.remove(suggestion) // Remove duplicates if already present
        recentSearches.add(0, suggestion)

        // Limit the list to a maximum size of 5
        val maxItems = 5
        if (recentSearches.size > maxItems) {
            recentSearches.subList(maxItems, recentSearches.size).clear()
        }

        // Save the updated list back to SharedPreferences
        val suggestionsJson = gson.toJson(recentSearches)
        sharedPreferences.edit().putString("search_list", suggestionsJson).apply()
    }

    private fun showEligibilityPopup(totalActiveAds: Int?, maxTotalAds: Int?) {
        val builder: AlertDialog.Builder =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AlertDialog.Builder(this, R.style.AlertDialogCustom)
            } else {
                AlertDialog.Builder(this)
            }
        builder.setTitle(resources.getText(R.string.dialog_activate_limit_reached_title))
        builder.setMessage(
            resources.getText(R.string.limit_reached)
                .toString() + " «" + totalActiveAds + " / " + maxTotalAds + "», " + resources.getString(
                R.string.sub_shop
            ).lowercase(
                Locale.getDefault()
            )
        )
        builder.setNeutralButton(resources.getText(R.string.ef_ok)) { _, _ -> }
        builder.show()
    }

    private fun initPagerFragments() {
        mFragmentsList.clear()
        if (FlavorUtils.isPrivateFlavor()) {
            // adding all fragments to the pager
            mFragmentsList.add(
                AdsListingFragment.newInstance().apply {
                    title = <EMAIL>(R.string.tab_search)
                }
            )
            mFragmentsList.add(MyFavoritesFragment.newInstance())
            mFragmentsList.add(MessagingListFragment.newInstance())
            mFragmentsList.add(AccountFragment())
        } else {
            // adding all fragments to the pager
            mFragmentsList.add(
                AdsListingFragment.newInstance().apply {
                    title = <EMAIL>(R.string.tab_search)
                }
            )
            mFragmentsList.add(StatsFragment())
            mFragmentsList.add(MessagingListFragment.newInstance())
            mFragmentsList.add(AccountFragment())
        }

    }

    /**
     * Fill Category and location fields in control bar in case of deeplink.
     */
    private fun handleOnLaunchDeepLink() {
        val intent = intent
        intent?.let {
            if (Intent.ACTION_VIEW == intent.action) {
                isComingFromDeepLink = true
                val uri = intent.data
                retrieveUTMTags(uri)
                fillInDeepLinkData(uri)
            }
            if (intent.getBooleanExtra("gotoMyAccount", false)) {
                intent.putExtra("gotoMyAccount", false)
                bottomNavigationBar?.updateSelection(BottomNavigationBar.NAVIGATION_POSITION_PROFILE)
                openMyAdsFromPushNotification(intent.getStringExtra("my_ads_filter"))
            }
            if (intent.getBooleanExtra("gotoMyEcommerceAds", false)) {
                intent.putExtra("gotoMyEcommerceAds", false)
                bottomNavigationBar?.updateSelection(BottomNavigationBar.NAVIGATION_POSITION_PROFILE)
            }
        }
        // Avoid recalling deep link when resumed
        intent?.action = null
    }

    private fun openMyAdsFromPushNotification(adStatus: String?) {
        var myAdsListInitFilter = Constants.AD_STATUS_ACTIVE
        when (adStatus) {
            "ad_accepted" -> myAdsListInitFilter = Constants.AD_STATUS_ACTIVE
            "ad_refused" -> myAdsListInitFilter = Constants.AD_STATUS_REFUSED
            "ad_submitted" -> myAdsListInitFilter = Constants.AD_STATUS_PENDING_REVIEW
            "ad_pending_payment" -> myAdsListInitFilter =
                Constants.AD_STATUS_PENDING_PAYMENT
        }
        // todo
        val intent = Intent(this, AccountAdsActivity::class.java)
        intent.putExtra("my_ads_filter", myAdsListInitFilter)
        startActivity(intent)
    }

    private fun fillInDeepLinkData(uri: Uri?) {
        if (uri != null) {
            when (uri.path) {
                getString(R.string.app_dl_newsfeed) -> {
                    //  switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_PROFILE)
                    intent?.action = null
                }

                getString(R.string.app_dl_messaging) -> {
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_MESSAGING)
                    intent?.action = null
                }

                getString(R.string.app_dl_my_favorites) -> {
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_FAVORITES_STATS)
                    intent?.action = null
                }

                getString(R.string.app_dl_my_ads) -> {
                    intent?.action = null
//                    val intent = Intent(this, MyAdsActivity::class.kotlin)
//                    startActivity(intent)
                }
            }
            val mappingObject = Mapping()
            val keys = uri.queryParameterNames
            controlBarCity?.text = getString(R.string.all_cities)
            if (keys != null) {
                for (key in keys) {
                    when (key) {
                        "city", "ca" -> {
                            val cityValue = uri.getQueryParameter(key)
                            val isCityNumeric =
                                cityValue?.let { isNumeric(it) }
                            if (cityValue != "" && isCityNumeric == true) {
                                controlBarCity?.text =
                                    uri.getQueryParameter(key)
                                        ?.toInt()?.let {
                                            LocaleManager.getCurrentLanguage()?.let { lang ->
                                                mappingObject.getCitybyId(
                                                    it,
                                                    lang
                                                )
                                            }
                                        }
                            }
                        }

                        "reg" -> {
                            val cityValue = uri.getQueryParameter(key)
                            if (cityValue != "") {
                                val list =
                                    uri.getQueryParameter(key)
                                        ?.split(",")
                                var cities = ""
                                val cityString =
                                    list?.forEach { it ->
                                        it.toInt().let {
                                            LocaleManager.getCurrentLanguage()?.let { lang ->
                                                cities += mappingObject.getCitybyId(
                                                    it,
                                                    lang
                                                )
                                            }
                                        }
                                    }
                                controlBarCity?.text = cities
                            }
                        }

                        "category", "cg" -> {
                        }
                    }
                }
            }
            if (keys?.isNotEmpty() == true) {
                buildSearchDataModelFrom(uri)
            }
        }
    }

    fun buildSearchDataModelFrom(data: Uri) {
        val scheme = data.scheme
        val host = data.host
        if (host != null && scheme != null) {
            val path = data.path
            if (path.equals("/listing", ignoreCase = true) || path.equals(
                    "/list",
                    ignoreCase = true
                )
            ) {
                val queryString = extractQueryString(data.toString())
                onListingSearchTriggered(queryString)
            }
        }
    }

    fun extractQueryString(url: String): String {
        val queryStartIndex = url.indexOf("?")
        return if (queryStartIndex != -1 && queryStartIndex < url.length - 1) {
            url.substring(queryStartIndex + 1)
        } else {
            ""
        }
    }

    private fun handleLogInOut() {
        val intent: Intent
        val requestCode: Int
        navigationView?.let {
            if (mDrawerLayout != null && navigationView != null && mDrawerLayout?.isDrawerOpen(
                    it
                ) == true
            ) {
                mDrawerLayout?.closeDrawer(it)
            }
        }

        if (!AccountToken.isLoggedIn(this)) {
            intent = Intent(this, AuthenticationActivity::class.java)
            requestCode = AuthenticationActivity.REQUEST_SIGN_IN
        } else {
            FirebaseAnalytics.getInstance(this@MainActivity).logEvent(
                getString(R.string.tm_event_edit_account_pressed),
                bundleOf()
            )
            intent = Intent(this, EditAccountActivity::class.java)
            requestCode = EditAccountActivity.REQUEST_UPDATE_ACCOUNT
        }
        startActivityForResult(intent, requestCode)
    }

    /**
     * Analytic tools tracking of ad insertion button click
     */
    private fun trackAIEventFromFAB() {
        //Amplitude & AppBoy event tracking
        val eventProperties = HashMap<String?, String?>()
        eventProperties[this.getString(R.string.am_property_source)] = "Listing"
        AnalyticsManager.instance
            ?.logEvent(AnalyticsStrings.TRIGGER_AD_INSERT, eventProperties, true)
    }

    private fun openAdInsert() {
        lifecycleScope.launch(Dispatchers.IO) {
            if (!AccountToken.isLoggedIn(this@MainActivity)) {
                withContext(Dispatchers.Main) {
                    val intent = Intent(
                        this@MainActivity,
                        AuthenticationActivity::class.java
                    )
                    startActivityForResult(
                        intent,
                        AuthenticationActivity.REQUEST_SIGN_AD_INSERTION
                    )
                }
            } else {
                withContext(Dispatchers.Main) {
                    val intent = Intent(
                        this@MainActivity,
                        AdInsertActivity::class.java
                    )
                    startActivity(intent)
                }
            }
        }
    }


    private fun closeDrawer() {
        if (mDrawerLayout?.isDrawerOpen(GravityCompat.END) == true) mDrawerLayout?.closeDrawer(
            GravityCompat.END
        ) else if (mDrawerLayout?.isDrawerOpen(GravityCompat.START) == true) mDrawerLayout?.closeDrawer(
            GravityCompat.START
        )
    }

    private fun trackAIEventFromMenu() {

        //Amplitude & AppBoy event tracking
        val manager = AnalyticsManager.instance
        val eventProperties = HashMap<String?, String?>()

        eventProperties[this.getString(R.string.am_property_source)] = "LeftMenu"
        manager?.logEvent(AnalyticsStrings.TRIGGER_AD_INSERT, eventProperties, true)
    }

    private fun trackMessaging(source: String) {
        //Amplitude & AppBoy event tracking
        val manager = AnalyticsManager.instance
        val eventProperties = HashMap<String?, String?>()
        eventProperties[this.getString(R.string.am_property_source)] = source
        if (manager != null) {
            if (AccountToken.isLoggedIn(this)) {
                val currentAccount = Account.getCurrentAccount(this)
                eventProperties.putAll(
                    AnalyticsUtils.generateMapFromAccountObject(
                        currentAccount
                    )
                )
            }
            manager.logEvent(
                getString(R.string.clicked_on_inbox),
                eventProperties,
                false
            )
        }
        FirebaseAnalytics.getInstance(this).logEvent(
            getString(R.string.clicked_on_inbox),
            bundleOf()
        )
    }

    /**
     * Method that handles a click action on the "contactez nous" button in the left drawer
     */
    private fun openContactSupport() {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data =
                Uri.parse("mailto:" + getString(R.string.customer_services_email))
            intent.putExtra(
                Intent.EXTRA_SUBJECT,
                getString(R.string.customer_services_email_subject)
            )
            intent.putExtra(Intent.EXTRA_TEXT, menuContactMessageTemplate())
            startActivityForResult(intent, 5678)
        } catch (e: ActivityNotFoundException) {
            val builder = AlertDialog.Builder(this)
            builder.setTitle(R.string.customer_services_email_no_apps_title)
            builder.setMessage(R.string.customer_services_email_no_apps_description)
            builder.setNeutralButton(getString(R.string.common_ok)) { dialog, _ -> dialog.dismiss() }
            builder.show()
        }
    }

    /**
     * Method that returns the email template for "contactez nous"
     *
     * @return: String representing the contact message body to be sent
     */
    private fun menuContactMessageTemplate(): String {
        return StringBuilder("\n\n\n\n")
            .append("----------------------\n")
            .append("----------------------\n")
            .append(this.getString(R.string.customer_services_email_footer_message))
            .append("\n")
            .append(this.getString(R.string.customer_services_email_device_os_version))
            .append(Build.VERSION.RELEASE)
            .append("\n")
            .append(this.getString(R.string.customer_services_email_device_model))
            .append(Build.MANUFACTURER)
            .append(" ").append(Build.DEVICE)
            .append(" ").append(Build.MODEL)
            .append("\n")
            .append(this.getString(R.string.customer_services_email_application_version))
            .append(BuildConfig.VERSION_NAME)
            .toString()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            val token = data?.getStringExtra(AuthenticationActivity.TOKEN_KEY) ?: return
            val loginTypeName = data.getStringExtra(AuthenticationActivity.LOGIN_TYPE_KEY) ?: return
            val loginType = try {
                LoginType.valueOf(loginTypeName)
            } catch (e: IllegalArgumentException) {
                return
            }
            mainViewModel.onUserConnected(loginType, token)
        }
        when (requestCode) {
            InAppUpdateUtils.UPDATE_REQUEST_CODE -> {
                if (resultCode == RESULT_OK) return

                val isImmediateUpdateEnabled =
                    Utils.getBooleanPreference(this, Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE)
                if (isImmediateUpdateEnabled) {
                    handleUpdateDismiss()
                }
            }

            AuthenticationActivity.REQUEST_SIGN_IN -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                refreshAccountControls()
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            AuthenticationActivity.REQUEST_SIGN_AD_INSERTION -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                openAdInsert()
            }

            AuthenticationActivity.REQUEST_SIGN_IN_MY_ACCOUNT -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_PROFILE)
                refreshAccountControls()
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            AuthenticationActivity.REQUEST_SIGN_IN_FAVORITES_STATS -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_FAVORITES_STATS)
                refreshAccountControls()
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            EditAccountActivity.REQUEST_UPDATE_ACCOUNT -> {
                if (resultCode == EditAccountActivity.RESULT_LOGOUT) {
                    displayDefaultSnackbar(
                        TODO(),
                        R.string.common_logout_success_message
                    )
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                }
                refreshAccountControls()
            }

            AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING -> if (resultCode == RESULT_OK) {
                refreshAccountControls()
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_MESSAGING)
            }

            AuthenticationActivity.REQUEST_SIGN_IN_FILTERS -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                // displayFilterSideBar()
                filtersSharedViewModel.onSaveSearchAfterSignIn()
            }

            INAPP_UPDATE_REQUEST_CODE -> if (resultCode != RESULT_OK) {
                setUpdateItemVisibility()
                if (resultCode == RESULT_CANCELED) displaySnackbar(
                    TODO(),
                    if (LocaleManager.isFr()) Utils.getStringPreference(
                        this,
                        Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT
                    ) else Utils.getStringPreference(
                        this,
                        Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR
                    ),
                    -1
                )
            }

            else -> if (resultCode == Constants.RESULT_SIGN_IN_FROM_AD_DETAIL) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
            } else {
                super.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    private fun handleUpdateDismiss() {
        AlertDialog.Builder(this)
            .setTitle(R.string.update_dismiss_title)
            .setMessage(R.string.update_dismiss_message)
            .setPositiveButton(R.string.timeout_second_retry_message_action) { _, _ ->
                appUpdateManager?.let { updateApp(this, it) }
            }
            .setCancelable(false)
            .show()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        goToConversationFrom(intent)
    }

    private fun goToConversationFrom(intent: Intent?) {
        val extras = intent?.extras
        val conversationId = extras?.getString(
            MESSAGING_NOTIFICATION_CONVERSATION_ID_LABEL_FOREGROUND
        )
        val notificationType = extras?.getString(
            MESSAGING_NOTIFICATION_TYPE_LABEL_FOREGROUND
        )
        if (notificationType == MESSAGING_NOTIFICATION_TYPE) {
            if (conversationId != null) {
                if (currentOpenedConversationId.isNotEmpty() && currentOpenedConversationId != conversationId) {
                    // TODO (supportFragmentManager.findFragmentByTag(currentOpenedConversationId) as ConversationBottomSheet?)?.dismiss()
                }

                if (pager?.currentItem != BottomNavigationBar.NAVIGATION_POSITION_MESSAGING) {
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_MESSAGING)
                    (mFragmentsList[BottomNavigationBar.NAVIGATION_POSITION_MESSAGING] as MessagingListFragment).scrollToPosition(
                        0
                    )
                }
                (mFragmentsList[BottomNavigationBar.NAVIGATION_POSITION_MESSAGING] as MessagingListFragment).resetConversationUnreadCount(
                    conversationId
                )
                // TODO
                /*if (supportFragmentManager.findFragmentByTag(conversationId) == null && AccountToken.isLoggedIn(
                        this
                    ) && !AccountToken.isSessionExpired(this)
                ) {
                    ConversationBottomSheet.newInstance(
                        conversationId = conversationId,
                        chatListUpdateListener = mFragmentsList[BottomNavigationBar.NAVIGATION_POSITION_MESSAGING] as MessagingListFragment,
                        conversationActionsListener = mFragmentsList[BottomNavigationBar.NAVIGATION_POSITION_MESSAGING] as MessagingListFragment

                    )
                        .show(
                            supportFragmentManager,
                            conversationId
                        )
                }*/
            }

        }
    }

    override fun onBackPressed() {
        // If user is on first page (ad listing), when he presses back confirm his action
        if (mDrawerLayout?.isDrawerOpen(GravityCompat.END) == true) mDrawerLayout?.closeDrawer(
            GravityCompat.END
        ) else if (mDrawerLayout?.isDrawerOpen(GravityCompat.START) == true) mDrawerLayout?.closeDrawer(
            GravityCompat.START
        ) else if (pager?.currentItem != BottomNavigationBar.NAVIGATION_POSITION_HOME) {
            // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
        } else {
            confirmLeavingApp()
        }
    }

    private fun confirmLeavingApp() {
        val builder: AlertDialog.Builder =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AlertDialog.Builder(this, R.style.AlertDialogCustom)
            } else {
                AlertDialog.Builder(this)
            }
        builder.setTitle(R.string.app_name)
            .setMessage(R.string.onbackpressed_dialog_message)
            .setNegativeButton(R.string.no, null)
            .setPositiveButton(R.string.yes) { _, _ ->
                setResult(RESULT_CANCELED)
                finishAffinity()
            }
            .show()
    }

    private fun displayLeftSideBar() {
        navigationView?.let { mDrawerLayout?.openDrawer(it) }
    }

    private fun trackSearchDrawerOpen() {
        //tracking event instance amplitude & appboy
        val manager = AnalyticsManager.instance
        if (manager != null) {
            val eventProperties = HashMap<String?, String?>()
            eventProperties[this.getString(R.string.am_property_source)] = "NavBar"
            manager.logEvent(
                this.getString(R.string.am_event_open_search),
                eventProperties,
                true
            )
        }
    }

    private fun trackSearchDrawerOpenNew() {
        val analyticsManager = AnalyticsManager.instance
        if (analyticsManager != null) {
            val eventProperties = HashMap<String?, String?>()
            eventProperties["element_name"] = "triggered_search"
            eventProperties["page_name"] = "listing"
            analyticsManager.logFirebaseEvent("element_clicked", eventProperties)
        }
    }

    private fun refreshAccountControls() {
        val navigationHeader = navigationView?.getHeaderView(0)
        val userInfoLayout =
            navigationHeader?.findViewById<LinearLayout>(R.id.logged_in_user_info)
        val headerMessage =
            navigationHeader?.findViewById<LinearLayout>(R.id.logged_out_header_message)
        val navigationMenu = navigationView?.menu
        val menuItem = navigationMenu?.findItem(R.id.menu_account)
        // This should be done in the viewModel, but since we'll remove all this when migrating to compose no need to wast time.
        lifecycleScope.launch {
            val account = accountRepository.currentAccount.firstOrNull()
            withContext(Dispatchers.Main) {
                val contact = when (account) {
                    is Private -> account.contact
                    is Shop -> account.contact
                    else -> null
                }
                if (contact != null) {
                    userInfoLayout?.visibility = View.VISIBLE
                    headerMessage?.visibility = View.GONE
                    usernameLabel?.text = contact.name
                    val dateValues = contact.creationDate.split("-").toTypedArray()
                    if (dateValues.isNotEmpty())
                        memberSinceLabel?.text = getString(
                            R.string.drawer_membre_depuis,
                            dateValues[0]
                        )
                } else {
                    userInfoLayout?.visibility = View.GONE
                    headerMessage?.visibility = View.VISIBLE
                    if (menuItem != null) menuItem.title = getString(R.string.sign_in)
                }

            }
        }
        if (AccountToken.isLoggedIn(this)) {
            val currentAccount = Account.getCurrentAccount(this)
            if (currentAccount != null) {
                userInfoLayout?.visibility = View.VISIBLE
                headerMessage?.visibility = View.GONE
                usernameLabel?.text = currentAccount.name
                val dateValues =
                    currentAccount.activeSince.split("-").toTypedArray()
                if (dateValues.isNotEmpty())
                    memberSinceLabel?.text = getString(
                        R.string.drawer_membre_depuis,
                        dateValues[0]
                    )
            }
            if (menuItem != null) menuItem.title = this.getString(R.string.my_info)
        } else {
            userInfoLayout?.visibility = View.GONE
            headerMessage?.visibility = View.VISIBLE
            if (menuItem != null) menuItem.title = this.getString(R.string.sign_in)
        }
        invalidateOptionsMenu()
    }

    private fun setUpdateItemVisibility() {
        val navigationMenu = navigationView?.menu
        updateAppItem = navigationMenu?.findItem(R.id.menu_update)
        appUpdateManager?.appUpdateInfo?.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
            updateAppItem?.isVisible =
                (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE // For a flexible update, use AppUpdateType.FLEXIBLE
                        && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE))
        }
        invalidateOptionsMenu()
    }

    private fun setEnvironmentItemVisibility() {
        val navigationMenu = navigationView?.menu
        appEnvironmentItem = navigationMenu?.findItem(R.id.menu_app_environment)
        appEnvironmentItem?.isVisible = BuildConfig.DEBUG
        invalidateOptionsMenu()
    }

    private fun switchFavoritesStatsItemVisibility() {
        val navigationMenu = navigationView?.menu
        val isShopUser = FlavorUtils.isShopFlavor()
        navigationMenu?.findItem(R.id.menu_my_statistics)?.isVisible = isShopUser
        navigationMenu?.findItem(R.id.menu_my_favorites)?.isVisible = !isShopUser
        invalidateOptionsMenu()
    }

    fun logoutUser() {
        EventBusManager.instance?.postSticky(AuthenticationEvent(AuthenticationEvent.TYPE_LOGGED_OUT))
        AccountToken.deleteCurrentToken(this)
        Utils.removePreference(this, Account.ACCOUNT_PREF)
        AnalyticsManager.instance?.unSetUserId()
        analyticsHelper.clearUserData()
        FlavorUtils.resetFlavor()
    }

    /**
     * Catch various events:
     * - Search start event
     * - Navigation drawer open event
     * - Delete search success event
     *
     * @param event: Caught event to be handled
     */
    @Subscribe(sticky = true)
    fun <T : BusEvent?> onEvent(event: T) {
        if (event is NavigationDrawerEvent) {
            if (event.side == NavigationDrawerEvent.Side.LEFT) mDrawerLayout?.openDrawer(
                GravityCompat.START
            ) else mDrawerLayout?.openDrawer(GravityCompat.END)
        } else if (event is SwitchToTab) {
            // switchViewPager(event.position)
            EventBusManager.instance?.removeStickyEvent<BusEvent>(event)
        } else if (event is CloseDrawerEvent) {
            mDrawerLayout?.closeDrawers()
        }
    }

    fun setMessagingCount(count: Long) {
        ShortcutBadger.applyCount(this, count.toInt())
        bottomNavigationBar?.setMessagingCount(count)
    }

    /**
     * Enable displaying Braze In-App Messages
     *
     * @return true : enable
     */
    fun enableAppboyInAppMessages(): Boolean {
        return true
    }

    companion object {
        private const val INAPP_UPDATE_REQUEST_CODE = 530
        private const val CATEGORY_FILTER_KEY = "category"
        private const val AD_TYPE_FILTER_KEY = "adType"
    }

    override fun onListingSearchTriggered(query: String) {
//        switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
        hasToLoadFilters = true
        val reverseMapParams = reverseMapParams(query).toMutableMap()
//        findAndPutCityParams(reverseMapParams)?.let {
//            reverseMapParams["cityLabel"] = (it to "")
//        }
//        findAndPutAreaParams(reverseMapParams)?.let {
//            reverseMapParams["areaLabel"] = (it to "")
//        }
//        mSearchView?.setSearchText(reverseMapParams["keyword"]?.first as String? ?: "")


        filtersSharedViewModel.onApplyFiltersFromSaveSearchOrDeeplink(reverseMapParams)

        // displayFilterSideBar()
    }

    private fun reverseMapParams(queryString: String): Map<String, Pair<Any, String>> {
        val selectedFilters: MutableMap<String, Pair<Any, String>> = mutableMapOf()

        val params = queryString.split("&")
        for (param in params) {
            val parts = param.split("=")
            if (parts.size == 2) {
                val key = parts[0].removePrefix("&")
                val value = parts[1].trim()

                val selectedValue: Any = when {
                    value.contains("-") -> {
                        val (first: String, second: String) = value.split("-")
                        try {
                            Pair(first.toInt(), second.toInt())
                        } catch (e: NumberFormatException) {
                            Pair(first, second)
                        }
                    }

                    value.equals("true", ignoreCase = true) -> true
                    value.equals("false", ignoreCase = true) -> false
                    else -> value
                }
                selectedFilters[key] = (selectedValue to "") // to review
            }
        }
        return selectedFilters
    }
}