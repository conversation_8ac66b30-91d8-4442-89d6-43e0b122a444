package se.scmv.morocco.activities.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.apollographql.apollo3.ApolloClient
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.Avito
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.avitov2.core.domain.usecases.CreateLoginSessionUseCase
import se.scmv.morocco.avitov2.core.domain.usecases.RefreshCategoriesAndCitiesUseCase
import se.scmv.morocco.avitov2.floatingsearchview.suggestions.model.SearchSuggestionImpl
import se.scmv.morocco.core.getTokenStoreId
import se.scmv.morocco.data.mappers.toAccount
import se.scmv.morocco.data.session.JwtManager
import se.scmv.morocco.data.session.Session
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.data.session.Token
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.getApolloClientWithAuth
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.utils.FlavorUtils.Companion.isShopFlavor
import se.scmv.morocco.utils.StoreUtils.saveStoreInfoInPref
import se.scmv.morocco.utils.isNotEmpty
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val refreshCategoriesAndCitiesUseCase: RefreshCategoriesAndCitiesUseCase,
    private val createLoginSessionUseCase: CreateLoginSessionUseCase,
    private val sessionManager: SessionManager,
    private val apolloClient: ApolloClient,
    accountRepository: AccountRepository
) : ViewModel() {

    val viewState: StateFlow<MainViewState> = accountRepository.currentAccount.map {
        MainViewState.Success(it)
    }.stateIn(
        scope = viewModelScope,
        initialValue = MainViewState.Loading,
        started = SharingStarted.WhileSubscribed(5_000),
    )

    private var lastRequestTime: Long = 0 // To store the last request timestamp
    var isCheckingPreConnected = true

    private val _searchQuery = MutableStateFlow("")
    @OptIn(ExperimentalCoroutinesApi::class)
    val searchSuggestions: Flow<List<SearchSuggestionImpl>> = _searchQuery
        .mapLatest {
            if (it.isBlank()) {
                return@mapLatest emptyList()
            }
            val currentTime = System.currentTimeMillis()
            // Check if it's been more than 1.5 second since the last request
            val timeDifference = currentTime - lastRequestTime
            // If 1 second has passed
            if (timeDifference >= 1_500) {
                lastRequestTime = currentTime  // Update last request time
                fetchSuggestions(it)
            } else {
                // If less than 1 second passed, schedule a request after the remaining time
                delay(1_500 - timeDifference)
                fetchSuggestions(it)
            }
        }

    init {
        viewModelScope.launch {
            refreshCategoriesAndCitiesUseCase()
            checkIfPreConnected()
        }
    }

    fun onSearchChanged(query: String) {
        _searchQuery.update { query }
    }

    fun onSearchCleared() {
        viewModelScope.launch {
            _searchQuery.update { "" }
        }
    }

    fun onUserConnected(loginType: LoginType, token: String) {
        viewModelScope.launch { createLoginSessionUseCase(loginType, token) }
    }

    private suspend fun fetchSuggestions(searchQuery: String): List<SearchSuggestionImpl> {
        return emptyList()
    }


    // TODO Remove this temporary check after migrating all features to new auth system.
    private suspend fun checkIfPreConnected() {
        val isOldConnected = AccountToken.isLoggedIn(Avito.context)
        val isNewConnected = sessionManager.currentAccount.firstOrNull()?.let {
            it is se.scmv.morocco.domain.models.Account.NotConnected
        } ?: false
        if (isOldConnected && !isNewConnected) {
            try {
                if (isShopFlavor()) {
                    refreshStoreInfo()
                } else {
                    refreshAccountInfo()
                }
                isCheckingPreConnected = false
            } catch (e: Exception) {
                isCheckingPreConnected = false
            }
        } else {
            isCheckingPreConnected = false
        }
    }

    private suspend fun refreshStoreInfo() {
        AccountToken.getCurrentToken(Avito.context)?.token?.let { token ->
            val storeId = getTokenStoreId()
            val response = getApolloClientWithAuth("Bearer $token").query(
                GetMyStoreInfoQuery(storeId.toString())
            ).execute()
            if (response.errors.isNullOrEmpty()) {
                response.data?.getMyStoreInfo?.let { storeInfo ->
                    Avito.context?.let {
                        saveStoreInfoInPref(
                            Avito.context!!,
                            storeInfo
                        )
                    }
                    val account = Account()
                    account.email =
                        storeInfo.email
                    account.name =
                        storeInfo.name
                    account.phone =
                        storeInfo.phones[0]?.number
                    account.activeSince =
                        storeInfo.startDate
                    account.accountId = storeId
                    Account.setCurrentAccount(account)
                    val session: Session = JwtManager.mapToSession(token, 1)
                    val allowedAccess = if (session is Session.Shop) {
                        session.allowedAccess
                    } else {
                        AllowedAccess.default
                    }
                    val pbAccount = storeInfo.toAccount(
                        accountId = storeId.toString(),
                        allowedAccess = allowedAccess
                    )
                    sessionManager.createSession(
                        token = Token(token, token),
                        account = pbAccount
                    )
                }
            }
        }
    }

    private suspend fun refreshAccountInfo() {
        AccountToken.getCurrentToken(Avito.context)?.token?.let { token ->
            val response = getApolloClientWithAuth("Bearer $token").query(
                GetMyAccountInfoQuery()
            ).execute()
            if (response.errors.isNullOrEmpty()) {
                response.data?.getMyAccountInfo?.let { getMyAccountInfo ->
                    val account = Account()

                    getMyAccountInfo.id.let {
                        account.accountId =
                            it.toInt()
                    }
                    getMyAccountInfo.name.let {
                        account.name = it
                    }
                    getMyAccountInfo.email.let {
                        account.email = it
                    }
                    getMyAccountInfo.phone.let {
                        account.phone = it.number
                        account.isPhoneHidden =
                            if (it.isHidden) 1 else 0
                    }
                    getMyAccountInfo.location?.let {
                        if (it.city.id.isNotEmpty())
                            account.city =
                                it.city.id.toInt()
                    }
                    getMyAccountInfo.registeredAt.let {
                        account.activeSince = it
                    }
                    Account.setCurrentAccount(account)
                    val pbAccount = getMyAccountInfo.toAccount()
                    sessionManager.createSession(Token(token, token), account = pbAccount)
                }
            }
        }
    }
}

