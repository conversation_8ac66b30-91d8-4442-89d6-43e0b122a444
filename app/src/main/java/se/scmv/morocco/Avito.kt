package se.scmv.morocco

import android.content.Context
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.multidex.BuildConfig
import androidx.multidex.MultiDexApplication
import androidx.work.Configuration
import androidx.work.WorkManager
import com.braze.Braze
import com.braze.BrazeActivityLifecycleCallbackListener
import com.braze.support.BrazeLogger
import com.facebook.ads.AudienceNetworkAds
import com.facebook.appevents.AppEventsLogger
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.gms.security.ProviderInstaller
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import dagger.hilt.android.HiltAndroidApp
import io.realm.Realm
import io.realm.RealmConfiguration
import io.realm.exceptions.RealmMigrationNeededException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.dao.AvitoRealmMigration
import se.scmv.morocco.domain.models.DomainRealmModule
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.messaging.utils.MessagingUtils
import se.scmv.morocco.services.TAG
import se.scmv.morocco.utils.Constants.PRIVATE_FLAVOR
import se.scmv.morocco.utils.FlavorUtils
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.Utils.APP_FLAVOR
import java.security.KeyManagementException
import java.security.NoSuchAlgorithmException
import java.util.concurrent.Executors
import javax.inject.Inject
import javax.net.ssl.SSLContext


/**
 * This is the first executed class
 * Updated by Soufiane on 10/10/16
 * Updated by hb on 12/16/2016.
 */

@HiltAndroidApp
class Avito : MultiDexApplication() {
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    @Inject lateinit var remoteConfig: FirebaseRemoteConfig
    @Inject lateinit var firebaseMessaging: FirebaseMessaging
    @Inject lateinit var sslContext: SSLContext
    @Inject lateinit var analyticsHelper: AnalyticsHelper
    @Inject lateinit var braze: Braze

    companion object {
        @JvmField
        var APP_ENV: String? = null

        @JvmField
        var FLAVOR: String? = null
        //TODO: should be removed this static call
        var context: Context? = null
            private set
        init {
            //TODO: clean up all png icons for which a vector drawable alternative is available
            //TODO: clean up some folders that target specific API versions (ie drawable-anydpi-v21 & drawable-v16)
            AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        }
    }

    override fun onCreate() {
        super.onCreate()
        context = this
        realmInitForSpecificMarketplace()
        setupAppEnvironment()
        initAppFlavor()
        if (AccountToken.isLoggedIn(context) && (AccountToken.isSessionExpired(context) || AccountToken.isTokenInvalid())) {
            logoutUser()
        }

        applicationScope.launch(
            CoroutineExceptionHandler { _, error ->
                se.scmv.morocco.utils.Log.e(
                    this@Avito::class.TAG,
                    "Critical error during app initialization: ${error.message}")
            }
        ) {
            try {
                // Initialize critical services first
                initCriticalServices()

                // Initialize non-critical services
                initNonCriticalServices()

                // Initialize analytics and tracking
                initAnalytics()

                // Initialize Firebase
                initFirebase()
            } catch (e: Exception) {
                se.scmv.morocco.utils.Log.e(
                    this@Avito::class.TAG,
                    "Failed to initialize app: ${e.message}",
                )
                // add crash reporting
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }

        val workManagerConfig = Configuration.Builder()
            .setMinimumLoggingLevel(Log.VERBOSE)
            .setExecutor(Executors.newSingleThreadExecutor())
            .build()
        WorkManager.initialize(this, workManagerConfig)
    }

    private suspend fun initCriticalServices() {
        try {
            FirebaseApp.initializeApp(this)
            initSSL()
            setupRemoteConfig()
        } catch (e: Exception) {
            se.scmv.morocco.utils.Log.e(
                this@Avito::class.TAG,
                "Failed to initialize critical services: ${e.message}",
            )
            throw e // Re-throw as this is critical
        }
    }

    private suspend fun initNonCriticalServices() {
        try {
            BrazeLogger.logLevel = BrazeLogger.SUPPRESS
            MobileAds.initialize(this)
            AudienceNetworkAds.initialize(this)
            registerActivityLifecycleCallbacks(BrazeActivityLifecycleCallbackListener())
        } catch (e: Exception) {
            se.scmv.morocco.utils.Log.e(
                this@Avito::class.TAG,
                "Failed to initialize non-critical services: ${e.message}",
            )
        }
    }

    private suspend fun initAnalytics() {
        try {
            AppEventsLogger.activateApp(this)
            AnalyticsManager.initialize(applicationContext)
        } catch (e: Exception) {
            // Don't re-throw as analytics are non-critical
            se.scmv.morocco.utils.Log.e(
                this@Avito::class.TAG,
                "Failed to initialize analytics: ${e.message}",
            )
        }
    }

    private fun initAppFlavor() {
        FLAVOR = Utils.getStringPreferenceWithDefaultValue(this, APP_FLAVOR, PRIVATE_FLAVOR)
        if (!AccountToken.isLoggedIn(this)) {
            FLAVOR = PRIVATE_FLAVOR
        }
    }

    private fun initSSL() {
        try {
            // Install the provider in the main thread
            try {
                ProviderInstaller.installIfNeeded(applicationContext)
            } catch (e: GooglePlayServicesRepairableException) {
                // Google Play services is not installed, disabled, or the version is too old
                Log.e("Avito", "Google Play Services needs to be updated", e)
                // Continue with SSL setup anyway as other features might work
            } catch (e: GooglePlayServicesNotAvailableException) {
                // Google Play services is not available
                Log.e("Avito", "Google Play Services not available", e)
                // Continue with SSL setup anyway as other features might work
            }

            // Initialize SSL context regardless of provider installation
            sslContext.init(null, null, null)
            sslContext.createSSLEngine()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } catch (e: KeyManagementException) {
            e.printStackTrace()
        }
    }

    private suspend fun initFirebase() {
        runCatching {
            val fcmToken = firebaseMessaging.token.await()
            setupNewMessagingFirebaseToken(fcmToken)
            braze.registeredPushToken = fcmToken
        }.onFailure {
            Log.e(
                "FirebaseMessaging:",
                "Fetching FCM registration token failed",
                it
            )
        }
        runCatching {
            val instanceId = FirebaseAnalytics.getInstance(applicationContext).appInstanceId.await()
            Utils.savePreference(
                applicationContext,
                Utils.FIREBASE_ANALYTICS_INSTANCE_ID,
                instanceId
            )
        }.onFailure {
            Log.e(
                "FirebaseAnalytics",
                "Exception while refreshing FirebaseAnalytics appInstanceId",
                it
            )
        }
    }

    private fun logoutUser() {
        AccountToken.deleteCurrentToken(this)
        Utils.removePreference(this, Account.ACCOUNT_PREF)
        AnalyticsManager.instance?.unSetUserId()
        analyticsHelper.clearUserData()
        FlavorUtils.resetFlavor()
    }

    private fun setupNewMessagingFirebaseToken(token: String) {
        val currentToken = Utils.getStringPreference(
            this,
            Constants.FIREBASE_MESSAGING_TOKEN
        )

        if (!AccountToken.isLoggedIn(context) && !currentToken.isNullOrBlank()) {
            MessagingUtils.unRegisterFirebaseToken(
                currentToken
            )
        }

        if (AccountToken.isLoggedIn(context) && (AccountToken.isSessionExpired(context) || AccountToken.isTokenInvalid())) {
            currentToken?.let { tk ->
                MessagingUtils.unRegisterFirebaseToken(
                    tk
                )
            }
        }
        if (AccountToken.isLoggedIn(context) && !AccountToken.isSessionExpired(context) && !AccountToken.isTokenInvalid()) {
            Utils.savePreference(
                context,
                Constants.FIREBASE_MESSAGING_TOKEN, token
            )
            MessagingUtils.registerFirebaseToken(
                newToken = token
            )

        }
    }

    private fun setupAppEnvironment() {
        APP_ENV = Utils.getStringPreferenceWithDefaultValue(context, Utils.APP_ENV, "pro")
    }

    private fun setupRemoteConfig() {
        var cacheExpiration: Long = 21600 // 6 hours in seconds.
        // If your app is using developer mode, cacheExpiration is set to 0,
        // so each fetch will retrieve values from the service.
        if (BuildConfig.DEBUG) {
            cacheExpiration = 0
        }
        remoteConfig.setDefaultsAsync(Keys.getDefaultConfigs())
        val task = remoteConfig.fetch(cacheExpiration)
        task.addOnSuccessListener { _: Void? ->
            remoteConfig.activate()
            Utils.updateRemoteConfigPreferences(context, remoteConfig)
        }
        task.addOnFailureListener { e: Exception? ->
            Utils.updateRemoteConfigPreferences(
                context,
                remoteConfig
            )
        }
    }


    private fun realmInitForSpecificMarketplace() {
        try {
            Realm.init(applicationContext)
            val config = RealmConfiguration.Builder()
                .deleteRealmIfMigrationNeeded()
                .name("avito.realm")
                .schemaVersion(8)
                .migration(AvitoRealmMigration())
                .allowWritesOnUiThread(true)
                .addModule(DomainRealmModule())
                .build()
            Realm.setDefaultConfiguration(config)
        } catch (e: Exception) {
            Log.e("Avito", "Failed to initialize Realm: ${e.message}", e)
            // If it's a migration exception, try to delete the realm
            if (e is RealmMigrationNeededException) {
                try {
                    Realm.getDefaultConfiguration()?.let { Realm.deleteRealm(it) }
                } catch (innerException: Exception) {
                    Log.e("Avito", "Failed to delete Realm after migration exception", innerException)
                }
            }
            // Continue app initialization even if Realm failed to initialize
        }
    }

    override fun onLowMemory() {
        super.onLowMemory()
        if (applicationScope.isActive) {
            applicationScope.cancel()
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        if (applicationScope.isActive) {
            applicationScope.cancel()
        }
    }
}