package se.scmv.morocco.avitov2.listing.presentation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import se.scmv.morocco.activities.WebViewActivity
import se.scmv.morocco.ad.listing.AdsListingRoute
import se.scmv.morocco.ad.listing.AdsListingViewModel
import se.scmv.morocco.ad.listing.UiEvent
import se.scmv.morocco.adstickybanner.AdServerService
import se.scmv.morocco.adstickybanner.LifecycleAdServerServiceConnection
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.analytics.AnalyticsStrings.FRB_CONTENT_TYPE
import se.scmv.morocco.avitov2.adview.presentation.AdViewActivity
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParamsWithLabels
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment.Companion.NC_ANALYTICS_EVENT_AD_VIEW_CLOSED
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment.Companion.NC_QUERY_PARAM_UTM_MEDIUM
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment.Companion.NC_QUERY_PARAM_UTM_SOURCE
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment.Companion.NC_QUERY_VALUE_UTM_MEDIUM
import se.scmv.morocco.avitov2.listing.presentation.AdsListingFragment.Companion.NC_QUERY_VALUE_UTM_SOURCE
import se.scmv.morocco.core.repeatWithCreatedLifeCycle
import se.scmv.morocco.databinding.FragmentAdsListingBinding
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.events.FavoriteAdEvent
import se.scmv.morocco.fragments.TrackableFragment
import se.scmv.morocco.utils.URLUtils
import se.scmv.morocco.utils.isNotNull
import javax.inject.Inject


@AndroidEntryPoint
class AdsListingFragment : TrackableFragment() {

    @Inject
    lateinit var accountRepository: AccountRepository

    companion object {
        fun newInstance() = AdsListingFragment()

        //NEW Construction URL Constants
        const val NC_ANALYTICS_EVENT_AD_VIEW_CLOSED = " Closed NC web view"
        const val NC_QUERY_PARAM_UTM_SOURCE = "utm_source"
        const val NC_QUERY_VALUE_UTM_SOURCE = "avito_integration_android"
        const val NC_QUERY_PARAM_UTM_MEDIUM = "utm_medium"
        const val NC_QUERY_VALUE_UTM_MEDIUM = "listing_integration_android"
    }

    private var _binding: FragmentAdsListingBinding? = null
    private val binding: FragmentAdsListingBinding
        get() = _binding!!

    private val viewModel: AdsListingViewModel by viewModels()
    private val filtersSharedViewModel: ListingFiltersSharedViewModel by activityViewModels()

    private var filters: GetListingQueryParamsWithLabels? = null

    private val analyticsManager = AnalyticsManager.instance

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentAdsListingBinding.inflate(inflater, container, false)
        val composeView = binding.composeView
        composeView.apply {
            setContent {
                AvitoTheme {
                    val accountState = remember { mutableStateOf<Account?>(null) }
                    LaunchedEffect(Unit) {
                        accountRepository.currentAccount.collectLatest {
                            accountState.value = it
                        }
                    }
                    AdsListingRoute(
                        filtersFlow = MutableStateFlow(emptyList()),
                        navigateToFilters = {},
                        notifyCategoryChanged = { _,_ -> },
                        viewModel = viewModel,
                        newItemsLoaded = {
                            adServerServiceConnection?.recordImpression()
                        },
                        onCancelExtendedDeliveryClicked = ::onCancelExtendedDeliveryClicked,
                        openNewConstruction = {
                            openNewConstruction(requireContext(), it)
                        },
                        openAdView = { ad ->
                            openAdViewActivity(ad)
                        }
                    )
                }
            }
        }
        return _binding?.root
    }

    private fun openAdViewActivity(ad: ListingAd) {
        when (ad) {
            is ListingAd.Published -> AdViewActivity.open(
                requireContext(),
                ad.listId,
                ad.defaultImage.orEmpty(),
                ad.title,
                ad.date,
                ad.imageCount,
                ad.videoCount,
                ad.videoUrl,
                ad.isStore,
                ad.price.getCurrentPriceWithCurrency(),
                (ad.price as? AdPrice.Available)?.oldWithCurrency,
                ad.location.area?.name + ", " + ad.location.name,
                "${ad.category.name} ${ad.adType.orEmpty()}",
                ad.category.id,
                ad.isUrgent == true,
                ad.isHotDeal == true,
                ad.discount
            )

            is ListingAd.Premium -> AdViewActivity.open(
                requireContext(),
                ad.listId,
                ad.defaultImage.orEmpty(),
                ad.title,
                ad.date,
                ad.imageCount,
                ad.videoCount,
                ad.videoUrl,
                ad.isStore,
                ad.price.getCurrentPriceWithCurrency(),
                (ad.price as? AdPrice.Available)?.oldWithCurrency,
                ad.location.area?.name + ", " + ad.location.name,
                "${ad.category.name} ${ad.adType.orEmpty()}",
                ad.category.id,
                ad.isUrgent == true,
                ad.isHotDeal == true,
                ad.discount
            )

            else -> {}
        }
    }

    private var myService: AdServerService? = null

    private var adServerServiceConnection: LifecycleAdServerServiceConnection? = null


    override fun onResume() {
        super.onResume()
        adServerServiceConnection =
            LifecycleAdServerServiceConnection(requireContext(), AdServerService::class.java) {
                myService = it
            }
        lifecycleScope.launch {
            lifecycle.addObserver(adServerServiceConnection!!)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        renderState()
        repeatWithCreatedLifeCycle {
            filtersSharedViewModel.filtersEvents
                .filterIsInstance<ListingFiltersSharedViewModel.FiltersEvents.FiltersChanged>()
                .collectLatest {
                    applyFilters(it.filters)
                }
        }
    }

    override fun getScreenViewAnalyticalName(): String {
        return FirebaseAnalytics.Event.SCREEN_VIEW
    }

    override fun getAnalyticsProperties(): HashMap<String?, String?> {
        val eventMap =
            analyticsManager?.createListingPropertiesEvents(filters?.getListingQueryParams, true)
        eventMap?.set(FirebaseAnalytics.Param.SCREEN_NAME, "search")
        eventMap?.set(FRB_CONTENT_TYPE, "search")
        eventMap?.set(FirebaseAnalytics.Param.SCREEN_CLASS, "AdsListingFragment")
        return eventMap ?: hashMapOf()
    }

    override fun onOffline() {
        binding.offlineLayout.root.visibility = View.VISIBLE
    }

    override fun onConnected() {
        binding.offlineLayout.root.visibility = View.GONE
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    fun onFavoriteEvent(event: FavoriteAdEvent) {
        //TODO: implement this
        //adsListingAdapter.updateFavoriteStatus(
        //    UpdateFavoriteStatus(
        //        adId = event.adId,
        //        isFavorite = event.isFavorite,
        //        updated = event.isSuccess
        //    )
        //)
    }


    private fun renderState() {
        with(binding) {
            repeatWithCreatedLifeCycle {
            }
        }
    }

    private fun onCancelExtendedDeliveryClicked() {
        filters?.let { notNullFilters ->
            val newParams = notNullFilters.getListingQueryParams.copy(offersShipping = false)
            filtersSharedViewModel.onFiltersChanged(notNullFilters.copy(getListingQueryParams = newParams))
        }
    }

    private fun onCategoryClicked(listingCategory: Category) {
        filtersSharedViewModel.onEvent(
            ListingFiltersSharedViewModel.FiltersEvents.OnListingCategorySelected(listingCategory)
        )
    }


    private fun applyFilters(params: GetListingQueryParamsWithLabels) {
        viewModel.onEvent(UiEvent.ListingUiEvents.OnFilterChanged(emptyList()))
        if (params.isNotNull()) {
            analyticsManager?.sendListedSearchAnalytics(
                analyticsManager.createListingPropertiesEvents(params.getListingQueryParams, false)
            )
        }
        filters = params
    }
}

private fun openNewConstruction(context: Context, externalLink: String) {
    val url = URLUtils.modifyUrlParams(
        externalLink = externalLink,
        newParams = mapOf(
            NC_QUERY_PARAM_UTM_SOURCE to NC_QUERY_VALUE_UTM_SOURCE,
            NC_QUERY_PARAM_UTM_MEDIUM to NC_QUERY_VALUE_UTM_MEDIUM,
        )
    )
    val intent = Intent(context, WebViewActivity::class.java).apply {
        putExtra(WebViewActivity.WEBVIEWBODY, WebViewActivity.IMMONEUF)
        putExtra(WebViewActivity.WEBVIEWURL, url)
        putExtra(WebViewActivity.ON_CLOSE_ANALYTICS_MESSAGE, NC_ANALYTICS_EVENT_AD_VIEW_CLOSED)
    }
    context.startActivity(intent)
}
