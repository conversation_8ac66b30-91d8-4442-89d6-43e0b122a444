package se.scmv.morocco.avitov2.core.domain.usecases

import android.content.Context
import com.braze.Braze
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.analytics.models.UserProperties
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.core.getRequestToken
import se.scmv.morocco.core.getTokenStoreId
import se.scmv.morocco.events.RefreshAccountInfoEvent
import se.scmv.morocco.getApolloClientWithAuth
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.utils.MessagingUtils
import se.scmv.morocco.services.TAG
import se.scmv.morocco.utils.EventBusManager
import se.scmv.morocco.utils.FlavorUtils
import se.scmv.morocco.utils.Log
import se.scmv.morocco.utils.StoreUtils.saveStoreInfoInPref
import javax.inject.Inject

class CreateLoginSessionUseCase @Inject constructor(
    @ApplicationContext private val context: Context,
    private val analyticsHelper: AnalyticsHelper,
    private val firebaseMessaging: FirebaseMessaging
) {
    suspend operator fun invoke(loginType: LoginType, token: String) {
        createSessionAndSignInMessaging(token)
        val accountToken = AccountToken.getCurrentToken(context)
        if (accountToken?.isStore == false) {
            refreshAccountInfo(loginType)
        } else {
            refreshStoreInfo(loginType)
        }
    }

    private fun createSessionAndSignInMessaging(accessToken: String) {
        val accountToken = AccountToken()
        accountToken.saveToken(context, accessToken)
        AccountToken.setSessionIsExpired(false)
        // register new token to firebase messaging service
        runCatching {
            firebaseMessaging.token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    android.util.Log.w(
                        TAG,
                        "Fetching FCM registration token failed",
                        task.exception
                    )
                    return@addOnCompleteListener
                }
                MessagingUtils.registerFirebaseToken(newToken = task.result)
                Braze.getInstance(context).registeredPushToken = task.result
            }
        }.onFailure {
            android.util.Log.e(
                "TEST",
                "Exception while registering Firebase token with Braze.",
                it
            )
        }
    }

    private suspend fun refreshStoreInfo(loginType: LoginType) {
        getRequestToken()?.let {
            val storeId = getTokenStoreId()
            try {
                val response = getApolloClientWithAuth(it)
                    .query(GetMyStoreInfoQuery(storeId.toString()))
                    .execute()
                response.data?.getMyStoreInfo?.let { storeInfo ->
                    saveStoreInfoInPref(context, storeInfo)
                    val account = Account().apply {
                        email = storeInfo.email
                        name = storeInfo.name
                        phone = storeInfo.phones[0]?.number
                        activeSince = storeInfo.startDate
                        accountId = storeId
                        accountType = 1
                    }
                    Account.setCurrentAccount(account)
                    trackLoggedInEvent(loginType, account)
                    FlavorUtils.setShopsFlavor()
                }
            } catch (e: Exception) {
                Log.e("refreshAccountInfo", "Error: ${e.localizedMessage}")
            }
        }
    }

    private suspend fun refreshAccountInfo(loginType: LoginType) {
        getRequestToken()?.let { token ->
            try {
                val response = getApolloClientWithAuth(token)
                    .query(GetMyAccountInfoQuery())
                    .execute()
                response.data?.getMyAccountInfo?.let { info ->
                    val account = Account().apply {
                        accountId = info.id.toInt()
                        name = info.name
                        email = info.email
                        phone = info.phone.number
                        isPhoneHidden = if (info.phone.isHidden) 1 else 0
                        info.location?.city?.id?.toIntOrNull()?.let { city = it }
                        activeSince = info.registeredAt
                        accountType = 0
                    }
                    Account.setCurrentAccount(account)
                    trackLoggedInEvent(loginType, account)
                    EventBusManager.instance?.postEvent(RefreshAccountInfoEvent())
                }
            } catch (e: Exception) {
                Log.e("refreshAccountInfo", "Error: ${e.localizedMessage}")
            }
        }
    }

    private fun trackLoggedInEvent(loginType: LoginType, account: Account) {
        analyticsHelper.identify(userId = account.email)
        analyticsHelper.setUserProperties(
            properties = UserProperties(
                name = account.name,
                phone = account.phone,
                email = account.email
            )
        )
        val firebaseProps = setOf(
            Param(key = AnalyticsEvent.ParamKeys.LANG, value = LocaleManager.getCurrentLanguage()),
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = if (account.accountType == 1) {
                    AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP
                } else AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            ),
            Param(key = AnalyticsEvent.ParamKeys.LOGIN_TYPE, value = loginType.name.lowercase()),
            Param(key = AnalyticsEvent.ParamKeys.PAGE_NAME, value = loginType.name.lowercase())
        )
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.LOGGED_IN,
                properties = firebaseProps
            ),
            where = setOf(AnalyticsAddons.FIREBASE)
        )
        val brazeEvent = when (loginType) {
            LoginType.EMAIL, LoginType.PHONE -> AnalyticsEvent.Types.LOG_IN
            LoginType.FACEBOOK -> AnalyticsEvent.Types.LOG_IN_FACEBOOK
            LoginType.GMAIL -> AnalyticsEvent.Types.LOG_IN_GOOGLE
        }
        analyticsHelper.logEvent(
            event = AnalyticsEvent(name = brazeEvent),
            where = setOf(AnalyticsAddons.BRAZE)
        )
        initAnalyticsManager(account)
    }

    // TODO Once we use the new analytics helper in all the app, the AnalyticsManager will be unused, so remove this function
    private fun initAnalyticsManager(account: Account) {
        val userProperties = mutableMapOf<String?, String?>().apply {
            put(AnalyticsManager.NAME_ATTRIBUTE, account.name)
            put(AnalyticsManager.EMAIL_ATTRIBUTE, account.email)
            put(AnalyticsManager.PHONE_ATTRIBUTE, account.phone)
        }
        AnalyticsManager.instance?.setUserId(userProperties)
    }
}