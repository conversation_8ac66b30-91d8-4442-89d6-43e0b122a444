package se.scmv.morocco.avitov2.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.avitov2.adinsert.domain.navigation.AccountNavigationImpl
import se.scmv.morocco.domain.navigation.AccountNavigation
import se.scmv.morocco.domain.navigation.AppAction
import se.scmv.morocco.domain.navigation.AppNavigation
import se.scmv.morocco.domain.navigation.EcommerceNavigator
import se.scmv.morocco.domain.navigation.ShopNavigator
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.navigation.AppActionImpl
import se.scmv.morocco.navigation.AppNavigatorImpl
import se.scmv.morocco.navigation.EcommerceNavigatorImpl
import se.scmv.morocco.shoppage.ShopNavigatorImpl

@Module
@InstallIn(SingletonComponent::class)
object NavigationModule {
    @Provides
    fun provideAppNavigator(@ApplicationContext context: Context): AppNavigation {
        return AppNavigatorImpl(context)
    }

    @Provides
    fun provideAppAction(
        accountRepository: AccountRepository,
        @ApplicationContext context: Context
    ): AppAction{
        return AppActionImpl(accountRepository, context)
    }

    @Provides
    fun provideShopNavigator(@ApplicationContext context: Context): ShopNavigator {
        return ShopNavigatorImpl(context)
    }

    @Provides
    fun provideAccountNavigator(@ApplicationContext context: Context): AccountNavigation {
        return AccountNavigationImpl(context)
    }

    @Provides
    fun provideEcommerceNavigator(): EcommerceNavigator {
        return EcommerceNavigatorImpl()
    }
}