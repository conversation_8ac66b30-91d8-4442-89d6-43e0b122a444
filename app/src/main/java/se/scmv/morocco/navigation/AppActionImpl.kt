package se.scmv.morocco.navigation

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import io.realm.Realm
import se.scmv.morocco.domain.models.AdRecord
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.toAdRecord
import se.scmv.morocco.domain.navigation.AppAction
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.utils.ActionsUtils
import javax.inject.Inject

class AppActionImpl @Inject constructor(
    private val accountRepository: AccountRepository,
    @ApplicationContext private val context: Context
): AppAction {
    override suspend fun isInFavorites(id: String): Boolean {
         return getLocalFavoriteAds().firstOrNull { it.adId == id.toInt() }?.isFavorite ?: false
    }

    private fun getLocalFavoriteAds(): List<AdRecord> {
        return Realm.getDefaultInstance().where(AdRecord::class.java)
            .equalTo("isFavorite", true)
            .findAll()
            .toList()
    }

    override suspend fun addToFavorite(storeListingAd: ListingAd.Published): Boolean {
        val response = accountRepository.bookmarkAd(storeListingAd.toAdRecord())
        return (response is Resource.Success)
    }

    override suspend fun removeFromFavorite(storeListingAd: ListingAd.Published): Boolean {
        val response = accountRepository.unBookmarkAd(storeListingAd.toAdRecord())
        return (response is Resource.Success)
    }

    override suspend fun openWhatsappPhoneNumber(phoneNumber: String) {
        ActionsUtils.whatsappPhoneNumber(context = context, phoneNumber)
    }
}