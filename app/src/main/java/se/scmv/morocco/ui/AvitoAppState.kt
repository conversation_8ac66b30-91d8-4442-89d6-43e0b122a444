package se.scmv.morocco.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import se.scmv.morocco.account.presentation.navigation.AccountBookmarksRoute
import se.scmv.morocco.account.presentation.navigation.AccountMasterRoute
import se.scmv.morocco.account.presentation.navigation.AccountStatisticsRoute
import se.scmv.morocco.account.presentation.navigation.MessagingRoute
import se.scmv.morocco.ad.navigation.AdInsertRoute
import se.scmv.morocco.ad.navigation.ListingRoute
import se.scmv.morocco.authentication.presentation.navigation.AuthScreen
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

@Composable
fun rememberAppState(
    account: Account,
    navController: NavHostController = rememberNavController(),
    homeNavController: NavHostController = rememberNavController(),
    coroutineScope: CoroutineScope = rememberCoroutineScope()
): AvitoAppState {
    return remember(navController, homeNavController, account, coroutineScope) {
        AvitoAppState(
            appNavController = navController,
            homeNavController = homeNavController,
            account = account,
            coroutineScope = coroutineScope
        )
    }
}

@Stable
class AvitoAppState(
    val appNavController: NavHostController,
    val homeNavController: NavHostController,
    val account: Account,
    coroutineScope: CoroutineScope
) {
    // NAVIGATION
    private val previousDestination = mutableStateOf<NavDestination?>(null)

    val currentDestination: NavDestination?
        @Composable get() {
            // Collect the currentBackStackEntryFlow as a state
            val currentEntry = homeNavController.currentBackStackEntryFlow
                .collectAsState(initial = null)

            // Fallback to previousDestination if currentEntry is null
            return currentEntry.value?.destination.also { destination ->
                if (destination != null) {
                    previousDestination.value = destination
                }
            } ?: previousDestination.value
        }

    val currentTopLevelDestination: TopLevelDestination?
        @Composable get() {
            return TopLevelDestination.entries.firstOrNull { topLevelDestination ->
                currentDestination?.hasRoute(route = topLevelDestination.route) == true
            }
        }

    /**
     * Map of top level destinations to be used in the TopBar and BottomBar. The key is the route.
     */
    val topLevelDestinations: List<TopLevelDestination> = TopLevelDestination.entries.filter {
        when (account) {
            is Account.Connected.Shop -> it != TopLevelDestination.BOOKMARKS
            else -> it != TopLevelDestination.STATS
        }
    }

    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        val topLevelNavOptions = navOptions {
            // Pop up to the start destination of the graph to
            // avoid building up a large stack of destinations
            // on the back stack as users select items
            popUpTo(homeNavController.graph.findStartDestination().id) {
                saveState = true
            }
            // Avoid multiple copies of the same destination when
            // reselecting the same item
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }

        when (topLevelDestination) {
            TopLevelDestination.LISTING -> homeNavController.navigate(ListingRoute, topLevelNavOptions)
            TopLevelDestination.BOOKMARKS -> homeNavController.navigate(
                AccountBookmarksRoute,
                topLevelNavOptions
            )

            TopLevelDestination.STATS -> homeNavController.navigate(
                AccountStatisticsRoute,
                topLevelNavOptions
            )

            TopLevelDestination.MESSAGING -> homeNavController.navigate(
                MessagingRoute,
                topLevelNavOptions
            )

            TopLevelDestination.ACCOUNT -> homeNavController.navigate(
                AccountMasterRoute,
                topLevelNavOptions
            )
        }
    }

    fun navigateToAdInsert() {
        if (account.isLogged()) {
            appNavController.navigate(AdInsertRoute())
        } else appNavController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
    }

    fun navigateToAuth() {
        appNavController.navigate(AuthScreen.NAV_GRAPH_ROUTE)
    }

    fun <T : Any> navigate(
        route: T,
        navOptions: NavOptions? = null,
        navigatorExtras: Navigator.Extras? = null
    ) {
        appNavController.navigate(
            route = route,
            navOptions = navOptions,
            navigatorExtras = navigatorExtras
        )
    }

    // FILTERS
    private val _filters = MutableStateFlow(emptyList<OrionBaseComponentValue>())
    val filters = _filters.asStateFlow()

    fun updateFilters(newFilters: List<OrionBaseComponentValue>) {
        _filters.update { newFilters }
    }
}