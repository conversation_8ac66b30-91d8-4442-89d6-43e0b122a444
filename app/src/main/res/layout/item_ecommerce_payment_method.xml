<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/payment_method_container"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:layout_weight="1"
    android:background="@drawable/payment_method_bg"
    android:orientation="horizontal"
    android:padding="@dimen/space_normal">

    <ImageView
        android:id="@+id/vas_payment_method_image"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="0.3"
        tools:src="@drawable/vas_payment_method_img" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="0.6"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/vas_payment_method_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:gravity="center_vertical"
            android:textColor="@color/vas_main_text_color"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="@string/webview_title_credit_card_payment" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/vas_payment_method_subname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:gravity="center_vertical"
            android:textColor="@color/black"
            android:textSize="13sp"
            android:textStyle="bold"
            tools:text="@string/webview_title_credit_card_payment" />
    </androidx.appcompat.widget.LinearLayoutCompat>


    <RadioButton
        android:id="@+id/vas_payment_method_chosen"
        style="@style/RadioButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:buttonTint="@color/darkGray"
        android:clickable="false"
        android:gravity="center_vertical" />
</LinearLayout>