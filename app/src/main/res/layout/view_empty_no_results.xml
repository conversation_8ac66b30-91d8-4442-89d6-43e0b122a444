<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/offline_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@color/appBackground"
    android:visibility="visible">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/textview_noresult"
        style="@style/emptyViewTitle"
        android:text="@string/oups"
        app:layout_constraintBottom_toTopOf="@+id/text_view_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_view_description"
        style="@style/emptyViewDescription"
        android:text="@string/listing_screen_empty_state_description"
        app:layout_constraintBottom_toTopOf="@+id/ic_no_result"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textview_noresult" />

    <ImageView
        android:id="@+id/ic_no_result"
        style="@style/emptyViewImage"
        app:srcCompat="@drawable/no_search_result_illustration"
        app:layout_constraintBottom_toTopOf="@+id/filters_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_description" />

    <Button
        android:id="@+id/filters_btn"
        style="@style/OrionButtonPrimary"
        android:layout_below="@+id/ic_no_result"
        android:text="@string/common_search"
        android:layout_height="wrap_content"
        android:layout_width="208dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ic_no_result" />

</androidx.constraintlayout.widget.ConstraintLayout>

