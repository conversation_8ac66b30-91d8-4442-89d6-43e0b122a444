<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <!-- Top sticky ImageView -->
    <ImageView
        android:id="@+id/sticky_banner_img"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_gravity="top"
        android:scaleType="centerCrop"
        android:contentDescription="@null"
        android:visibility="gone"
        android:elevation="10dp" />

    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/drawer_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/appBackground"
        tools:context=".activities.MainActivity">

        <!-- The main content view -->
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/main_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/action_bar_color"
                app:elevation="0dp">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?android:attr/actionBarSize"
                    android:background="@color/action_bar_color"
                    android:minHeight="?android:attr/actionBarSize"
                    app:layout_scrollFlags="enterAlways"
                    app:navigationIcon="@drawable/ic_round_menu_24px"
                    app:popupTheme="@style/AppTheme.PopupOverlay"
                    android:theme="@style/Toolbar"
                    app:titleTextAppearance="@style/Toolbar.TitleText" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="#efefef" />

                <include
                    layout="@layout/listing_control_bar_layout"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_alignParentTop="true"
                    app:layout_scrollFlags="scroll|enterAlways" />

            </com.google.android.material.appbar.AppBarLayout>

            <se.scmv.morocco.widgets.NoSwipePager
                android:id="@+id/tab_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingBottom="55dp"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <se.scmv.morocco.avitov2.floatingsearchview.FloatingSearchView
                android:id="@+id/floating_search_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:floatingSearch_close_search_on_keyboard_dismiss="true"
                app:floatingSearch_dismissFocusOnItemSelection="true"
                app:floatingSearch_dimBackground="false"
                app:floatingSearch_dismissOnOutsideTouch="true"
                app:floatingSearch_leftActionMode="showHamburger"
                app:floatingSearch_searchBarMarginLeft="0dp"
                app:floatingSearch_searchBarMarginRight="0dp"
                app:floatingSearch_searchBarMarginTop="0dp"
                app:floatingSearch_searchHint="@string/search_field_hint"
                app:floatingSearch_showSearchKey="true"
                app:floatingSearch_suggestionsListAnimDuration="250"
                app:floatingSearch_viewTextColor="@color/avitoPrimary"/>

            <se.scmv.morocco.widgets.BottomNavigationBar
                android:id="@+id/bottomNavigationBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <FrameLayout
            android:id="@+id/notificationAd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <androidx.compose.ui.platform.ComposeView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </FrameLayout>



        <!-- Navigation Drawer -->
        <com.google.android.material.navigation.NavigationView
            android:id="@+id/navigation_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:background="@color/action_bar_color"
            android:maxWidth="@dimen/max_width_nav_view"
            app:itemTextAppearance="@style/navigationViewMenuItemStyle"
            app:menu="@menu/drawer" />

        <!-- Filters Fragment -->
        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fc_listing_filters"
            android:name="se.scmv.morocco.avitov2.filters.presentation.ListingFiltersFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginStart="-65dp"
            android:fitsSystemWindows="true"
            android:tag="fragment_listing_filters"
            tools:layout="@layout/fragment_listing_filters" />
    </androidx.drawerlayout.widget.DrawerLayout>
</LinearLayout>