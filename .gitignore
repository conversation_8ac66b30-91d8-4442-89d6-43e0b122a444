# Built application files
/*/build/
*.ap_
app/*.apk

# Java class files
*.class


# Crashlytics configuations
com_crashlytics_export_strings.xml

# Gradle generated files
.gradle/
.gradle/*

.kotlin

# Signing files
.signing/


# User-specific configurations
.idea/libraries/
.idea/workspace.xml
.idea/tasks.xml
.idea/.name
.idea/compiler.xml
.idea/copyright/profiles_settings.xml
.idea/encodings.xml
.idea/misc.xml
.idea/modules.xml
.idea/scopes/scope_settings.xml
.idea/vcs.xml


### INTELLIJ ###
NextgenAndroidApp/NextgenAndroidApp.iml
*.iml
#*.ipr
#*.iws
.idea

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

#Librairie Files 

libraries/photoview/build/
libraries/viewPagerIndicator/build/
libraries/library/build/
libraries/FloatLabeledEditText/build/

# Local configuration file (sdk path, etc)
local.properties

/projectFilesBackup/
/build/
/captures/
captures/
libraries/viewPagerIndicator/
/captures/*
app/avito/*
app/avito/
app/release
/keystore
app/debug/
release/
debug/

