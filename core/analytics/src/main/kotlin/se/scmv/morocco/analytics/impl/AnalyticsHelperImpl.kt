package se.scmv.morocco.analytics.impl

import com.braze.Braze
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.formatters.toBrazeProperties
import se.scmv.morocco.analytics.formatters.toBundle
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.UserProperties
import javax.inject.Inject

/**
 * Implementation of the [AnalyticsHelper] interface for logging events and managing user data
 * across multiple analytics platforms including Firebase, Braze, and Facebook.
 *
 * @property braze The Braze SDK instance for tracking user events and properties.
 * @property facebookLogger The Facebook SDK instance for logging analytics events.
 * @property firebaseAnalytics The Firebase Analytics instance for logging events and user properties.
 */
internal class AnalyticsHelperImpl @Inject constructor(
    private val braze: Braze,
    private val facebookLogger: AppEventsLogger,
    private val firebaseAnalytics: FirebaseAnalytics,
) : AnalyticsHelper {

    override fun logEvent(event: AnalyticsEvent, where: Set<AnalyticsAddons>) {
        where.forEach {
            when (it) {
                AnalyticsAddons.FIREBASE -> firebaseAnalytics.logEvent(
                    event.name,
                    event.properties.toBundle()
                )

                AnalyticsAddons.BRAZE -> braze.logCustomEvent(
                    event.name,
                    event.properties.toBrazeProperties()
                )

                AnalyticsAddons.FACEBOOK -> facebookLogger.logEvent(
                    event.name,
                    event.properties.toBundle()
                )
            }
        }
    }

    override fun identify(userId: String) {
        braze.changeUser(userId)
        AppEventsLogger.setUserID(userId)
        firebaseAnalytics.setUserId(userId)
    }

    override fun setUserProperties(properties: UserProperties) {
        braze.currentUser?.apply {
            setFirstName(properties.name)
            setEmail(properties.email)
            setPhoneNumber(properties.phone)
        }
        AppEventsLogger.setUserData(
            email = properties.email,
            firstName = properties.name,
            phone = properties.phone,
            lastName = null, dateOfBirth = null, gender = null,
            city = null, state = null, zip = null, country = null
        )
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.NAME, properties.name)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.EMAIL, properties.email)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.PHONE, properties.phone)
    }

    override fun clearUserData() {
        braze.requestImmediateDataFlush()
        braze.changeUser(null)
        facebookLogger.flush()
        AppEventsLogger.clearUserID()
        AppEventsLogger.clearUserData()
        firebaseAnalytics.setUserId(null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.NAME, null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.EMAIL, null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.PHONE, null)
    }
}