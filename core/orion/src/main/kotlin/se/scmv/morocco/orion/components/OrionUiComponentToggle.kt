package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import se.scmv.morocco.designsystem.components.AvSwitch
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionToggle

class OrionUiComponentToggle(
    private val uiConfig: OrionToggle,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyBooleanValue? = null,
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var state by mutableStateOf(
        OrionKeyBooleanValue(
            id = uiConfig.baseData.id,
            value = initialValue?.value ?: uiConfig.defaultValue
        )
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(url = uiConfig.baseData.iconUrl)
            AvSwitch(
                checked = state.value,
                title = uiConfig.baseData.title,
                onCheckChanged = { checked ->
                    state = state.copy(value = checked)
                    listener.onValueChanged(uiConfig, collectValue())
                },
            )
        }
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (isVisible.not()) return emptyList()
        return listOf(state)
    }

    override fun resetValue() {
        state = state.copy(value = false)
    }
}