package se.scmv.morocco.orion.components

import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastFirstOrNull
import androidx.compose.ui.util.fastMap
import kotlinx.coroutines.flow.StateFlow
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.orion.OrionAlert
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionBooleans
import se.scmv.morocco.domain.models.orion.OrionGroupHeader
import se.scmv.morocco.domain.models.orion.OrionImageUploader
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyNumberValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringListValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionLap
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.domain.models.orion.OrionMinMax
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionNativeDropdown
import se.scmv.morocco.domain.models.orion.OrionNumberCounter
import se.scmv.morocco.domain.models.orion.OrionSelectExtended
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdown
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionSlider
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionTextField
import se.scmv.morocco.domain.models.orion.OrionTimePicker
import se.scmv.morocco.domain.models.orion.OrionToggle
import se.scmv.morocco.domain.models.orion.OrionVas
import se.scmv.morocco.domain.models.orion.OrionVideoUploader
import se.scmv.morocco.orion.components.media.OrionUiComponentImageUploader
import se.scmv.morocco.orion.components.media.OrionUiComponentMedia
import se.scmv.morocco.orion.components.media.OrionUiComponentVideoUploader
import se.scmv.morocco.ui.SnackBarType

interface OptionalItemsListener {
    fun onPrincipalImageChanged(image: OrionUiComponentMedia?) {}
    fun onVasPackageSelected(vasPack: VasPack, vasPackage: VasPackage) {}
    fun onExeSlotDaySelected(day: String) {}
    fun onExeSlotTimeSelected(timeId: String) {}
    fun onAddImageClicked() {}
    fun showSnackBar(uiText: UiText, type: SnackBarType) {}
    fun executeVasAction(action: VasAction) {}
    fun onValueChanged(component: OrionBaseComponent, values: List<OrionBaseComponentValue>) {}
}

fun OrionBaseComponent.toOrionUiComponent(
    categories: List<CategoryTree>,
    categoryId: String,
    adTypeKey: AdTypeKey,
    hasNextTextFieldItem: Boolean,
    initialValues: List<OrionBaseComponentValue>,
    listener: OptionalItemsListener,
    lapViewState: StateFlow<AdInsertLapViewState>? = null
): OrionUiComponent? {
    val stringValues = initialValues.filterIsInstance<OrionKeyStringValue>()
    val stringListValue = initialValues.filterIsInstance<OrionKeyStringListValue>()
    val numberValues = initialValues.filterIsInstance<OrionKeyNumberValue>()
    val booleanValues = initialValues.filterIsInstance<OrionKeyBooleanValue>()
    return when (this) {
        is OrionSingleSelectCategoryDropdown -> {
            val (initialValue, enabled) = initialValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .fastFirstOrNull { it.id == baseData.id }
                ?.let {
                    OrionSingleSelectCategoryDropdownValue(
                        id = baseData.id,
                        categoryId = it.categoryId,
                        adTypeKey = it.adTypeKey
                    ) to allowParentSelection
                } ?: run {
                OrionSingleSelectCategoryDropdownValue(
                    id = baseData.id,
                    categoryId = categoryId,
                    adTypeKey = adTypeKey
                ) to true
            }

            OrionUiComponentSingleSelectCategoryDropdown(
                uiConfig = this.copy(enabled = enabled),
                initialValue = initialValue,
                categories = categories,
                listener = listener
            )
        }

        is OrionGroupHeader -> OrionUiComponentGroupHeader(uiConfig = this)
        is OrionTextField -> OrionUiComponentTextField(
            uiConfig = this,
            hasNextTextFieldItem = hasNextTextFieldItem,
            listener = listener,
            initialValue = initialValues.fastFirstOrNull { it.id == baseData.id }?.let {
                OrionKeyStringValue(id = it.id, value = it.stringValue())
            }
        )

        is OrionToggle -> OrionUiComponentToggle(
            uiConfig = this,
            listener = listener,
            initialValue = booleanValues.fastFirstOrNull { it.id == baseData.id },
        )

        is OrionNumberCounter -> OrionUiComponentNumberCounter(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionSelectExtended -> OrionUiComponentSelectExtended(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionBooleans -> OrionUiComponentBooleans(
            uiConfig = this,
            listener = listener,
            initialValue = booleanValues.fastFilter { value -> value.id in items.fastMap { it.id } }
        )

        is OrionTimePicker -> OrionUiComponentTimePicker(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionNativeDropdown -> OrionUiComponentNativeDropdown(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionAlert -> OrionUiComponentAlert(uiConfig = this)
        is OrionImageUploader -> OrionUiComponentImageUploader(
            uiConfig = this,
            initialValue = initialValues.filterIsInstance<OrionMediaUploaderValue>()
                .fastFirstOrNull { it.id == baseData.id },
            onPrincipalImageChanged = listener::onPrincipalImageChanged
        )

        is OrionVideoUploader -> OrionUiComponentVideoUploader(
            uiConfig = this,
            initialValue = initialValues.filterIsInstance<OrionMediaUploaderValue>()
                .fastFirstOrNull { it.id == baseData.id },
        )

        is OrionLap -> lapViewState?.let { OrionUiComponentLap(uiConfig = this, viewState = it) }
        is OrionVas -> lapViewState?.let {
            OrionUiComponentVas(
                uiConfig = this,
                lapViewState = it,
                onVasPackageSelected = listener::onVasPackageSelected,
                onExeSlotDaySelected = listener::onExeSlotDaySelected,
                onExeSlotTimeSelected = listener::onExeSlotTimeSelected,
                onAddImageClicked = listener::onAddImageClicked,
                showSnackBar = listener::showSnackBar
            )
        }

        is OrionSmartDropDown -> OrionUiComponentSmartDropdown(
            uiConfig = this,
            listener = listener,
            initialParentValue = stringValues.fastFirstOrNull { it.id == baseData.id },
            initialChildValue = stringValues.fastFirstOrNull { it.id == childBaseData.id },
        )

        is OrionMultipleSelectSmartDropDown -> OrionUiComponentMultipleSelectSmartDropdown(
            uiConfig = this,
            listener = listener,
            initialParentValue = stringListValue.fastFirstOrNull { it.id == baseData.id },
            initialChildValue = stringListValue.fastFirstOrNull { it.id == childBaseData.id },
        )

        is OrionMinMax -> OrionUiComponentMinMax(uiConfig = this, listener = listener)

        is OrionSlider -> OrionUiComponentSlider(uiConfig = this, listener = listener)
    }
}