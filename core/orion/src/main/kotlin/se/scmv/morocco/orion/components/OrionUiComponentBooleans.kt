package se.scmv.morocco.orion.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastMap
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.components.AvChipGroup
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ChipStateHandler
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionBooleans
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue

class OrionUiComponentBooleans(
    private val uiConfig: OrionBooleans,
    private val listener: OptionalItemsListener,
    initialValue: List<OrionKeyBooleanValue> = emptyList()
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var chips by mutableStateOf(
        uiConfig.items.map {
            ChipData(
                id = it.id,
                name = it.name,
                selected = initialValue.any { value -> it.id == value.id }
            )
        }.toPersistentList()
    )

    @Composable
    override fun Content(modifier: Modifier) {
        AvChipGroup(
            chips = chips,
            onChipClicked = {
                chips = ChipStateHandler.handleMultiSelect(chips, it).toPersistentList()
                listener.onValueChanged(uiConfig, collectValue())
            }
        )
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return chips.fastFilter { it.selected }.map { OrionKeyBooleanValue(it.id, true) }
    }

    override fun resetValue() {
        chips = chips.fastMap { it.copy(selected = false) }.toPersistentList()
    }
}