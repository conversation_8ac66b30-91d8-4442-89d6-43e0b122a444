package se.scmv.morocco.orion.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.orion.presentation.FilterSharedValueManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SharedFlowModule {

    @Provides
    @Singleton
    fun provideFilterSharedFlow(): FilterSharedValueManager {
        return FilterSharedValueManager()
    }
}