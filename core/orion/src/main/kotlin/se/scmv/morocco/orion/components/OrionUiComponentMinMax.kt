package se.scmv.morocco.orion.components

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.core.text.isDigitsOnly
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionMinMax

class OrionUiComponentMinMax(
    private val uiConfig: OrionMinMax,
    private val listener: OptionalItemsListener
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var min by mutableStateOf("")
    private var max by mutableStateOf("")

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig) {
                with(baseData) {
                    IconAndTitleRow(iconUrl = iconUrl, title = title, required = required)
                }
                Row(
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    MinMaxTextField(
                        modifier = Modifier.weight(1f),
                        placeholder = range.first.toString(),
                        suffix = suffix,
                        text = min,
                        onTextChanged = {
                            val minInt = it.toIntOrNull()
                            if (it.isBlank() || (minInt != null && minInt < range.second)) {
                                min = it
                                listener.onValueChanged(uiConfig, collectValue())
                            }
                        }
                    )
                    MinMaxTextField(
                        modifier = Modifier.weight(1f),
                        placeholder = range.second.toString(),
                        suffix = suffix,
                        text = max,
                        onTextChanged = {
                            val minInt = it.toIntOrNull()
                            if (it.isBlank() || (minInt != null && minInt < range.second)) {
                                max = it
                                listener.onValueChanged(uiConfig, collectValue())
                            }
                        }
                    )
                }
            }
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not()) return true

        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        val minValue: Int? = min.toIntOrNull()
        val maxValue: Int? = max.toIntOrNull()
        return when {
            minValue != null || maxValue != null -> OrionKeyRangeValue(
                id = uiConfig.baseData.id,
                min = minValue ?: uiConfig.range.first,
                max = maxValue ?: uiConfig.range.second
            ).let { listOf(it) }

            else -> emptyList()
        }
    }

    override fun resetValue() {
        min = ""
        max = ""
    }

    @Composable
    fun MinMaxTextField(
        placeholder: String,
        suffix: String?,
        text: String,
        onTextChanged: (String) -> Unit,
        modifier: Modifier = Modifier
    ) {
        Row(
            modifier = modifier
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline,
                    shape = MaterialTheme.shapes.small,
                )
                .height(intrinsicSize = IntrinsicSize.Min),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextField(
                modifier = Modifier.weight(1f),
                value = text,
                onValueChange = {
                    if (it.isBlank() || it.isDigitsOnly()) {
                        onTextChanged(it)
                    }
                },
                placeholder = {
                    Text(
                        text = placeholder,
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done
                ),
                colors = TextFieldDefaults.colors(
                    unfocusedContainerColor = MaterialTheme.colorScheme.background,
                    focusedContainerColor = MaterialTheme.colorScheme.background,
                    unfocusedIndicatorColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent,
                )
            )
            if (!suffix.isNullOrBlank()) {
                VerticalDivider(modifier = Modifier)
                Text(
                    modifier = Modifier.padding(horizontal = MaterialTheme.dimens.medium),
                    text = suffix,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}