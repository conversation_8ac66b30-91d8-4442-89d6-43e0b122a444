package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.util.fastMap
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.components.AvChipGroup
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ChipStateHandler
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionSelectExtended
import se.scmv.morocco.domain.models.orion.OrionSelectedKeysValue
import se.scmv.morocco.orion.R
import java.util.regex.Pattern

class OrionUiComponentSelectExtended(
    private val uiConfig: OrionSelectExtended,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyStringValue? = null
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var chips by mutableStateOf(
        uiConfig.items.map {
            ChipData(id = it.id, name = it.name, selected = it.id == initialValue?.value)
        }.toPersistentList()
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                IconAndTitleRow(title = title, required = required, iconUrl = iconUrl)
            }
            AvChipGroup(
                chips = chips,
                onChipClicked = {
                    error = null
                    val newChips = if (uiConfig.allowMultiSelect) {
                        ChipStateHandler.handleMultiSelect(chips, it)
                    } else ChipStateHandler.handleMonoSelect(chips, it)
                    chips = newChips.toPersistentList()
                    listener.onValueChanged(uiConfig, collectValue())
                }
            )
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not()) return true

        if (uiConfig.allowMultiSelect) {
            val selected = chips.fastFilter { it.selected }
            if (selected.isEmpty()) {
                error = UiText.FromRes(R.string.orion_empty_field_error)
                return false
            }

            selected.fastForEach {
                val errorMessage = validateItem(it)
                if (errorMessage != null) {
                    error = UiText.Text(errorMessage)
                    return false
                }
            }
        } else {
            val selectedItem = chips.firstOrNull { it.selected }
            val errorMessage = validateItem(selectedItem)
            if (errorMessage != null) {
                error = UiText.Text(errorMessage)
                return false
            }
        }

        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        val selected = chips.fastFilter { it.selected }
        return if (uiConfig.allowMultiSelect) {
            when {
                uiConfig.baseData.required && selected.isEmpty() -> {
                    throw IllegalArgumentException(
                        "OrionUiComponentSelectExtended with MultiSelect: ${uiConfig.baseData.title} is required but not selected, make sure validate is called !"
                    )
                }
                selected.isEmpty() -> {
                    emptyList()
                }
                else -> listOf(
                    OrionSelectedKeysValue(
                        id = uiConfig.baseData.id,
                        keys = selected.fastMap { it.id }
                    )
                )
            }
        } else {
            val first = selected.firstOrNull()
            when {
                first == null && uiConfig.baseData.required -> {
                    throw IllegalArgumentException(
                        "OrionUiComponentSelectExtended with MonoSelect: ${uiConfig.baseData.title} is required but not selected, make sure validate is called !"
                    )
                }

                first != null -> {
                    listOf(OrionKeyStringValue(id = uiConfig.baseData.id, value = first.id))
                }

                else -> emptyList()
            }
        }
    }

    override fun resetValue() {
        chips = chips.fastMap { it.copy(selected = false) }.toPersistentList()
    }

    private fun validateItem(item: ChipData?): String? {
        uiConfig.baseData.validations.forEach { validation ->
            val isValid = checkRegexRule(item?.id.orEmpty(), validation.regex)
            if (isValid.not()) {
                return validation.errorMessage
            }
        }
        return null
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}