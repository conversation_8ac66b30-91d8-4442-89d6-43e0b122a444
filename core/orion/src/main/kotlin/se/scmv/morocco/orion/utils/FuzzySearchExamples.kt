package se.scmv.morocco.orion.utils

/**
 * Examples of how to use the FuzzySearchUtils in different scenarios
 */
object FuzzySearchExamples {

    // Example data classes
    data class User(val id: String, val name: String, val email: String)
    data class Product(val id: String, val title: String, val description: String, val category: String)
    data class City(val name: String, val country: String, val population: Int)

    /**
     * Example 1: Simple string search
     */
    fun searchInStringList() {
        val cities = listOf("Paris", "London", "New York", "Tokyo", "Berlin", "Madrid")
        val query = "par"
        
        val results = FuzzySearchUtils.performFuzzySearch(query, cities)
        
        results.forEach { result ->
            println("Match: ${result.matchedText} (Score: ${result.matchScore})")
        }
        // Output: Match: Paris (Score: 0.9)
    }

    /**
     * Example 2: Search in custom objects with single field
     */
    fun searchUsers() {
        val users = listOf(
            User("1", "<PERSON>", "<EMAIL>"),
            User("2", "<PERSON>", "<EMAIL>"),
            User("3", "<PERSON>", "<EMAIL>")
        )
        val query = "john"
        
        val results = FuzzySearchUtils.performFuzzySearch(
            query = query,
            items = users,
            textExtractor = { it.name }
        )
        
        results.forEach { result ->
            println("User: ${result.item.name} (Score: ${result.matchScore})")
        }
        // Output: 
        // User: John Doe (Score: 0.8)
        // User: Bob Johnson (Score: 0.8)
    }

    /**
     * Example 3: Search in multiple fields
     */
    fun searchProductsMultipleFields() {
        val products = listOf(
            Product("1", "iPhone 15", "Latest Apple smartphone", "Electronics"),
            Product("2", "Samsung Galaxy", "Android smartphone", "Electronics"),
            Product("3", "Apple Watch", "Smartwatch by Apple", "Wearables")
        )
        val query = "apple"
        
        // Search in title
        val titleResults = FuzzySearchUtils.performFuzzySearch(
            query = query,
            items = products,
            textExtractor = { it.title }
        )
        
        // Search in description
        val descriptionResults = FuzzySearchUtils.performFuzzySearch(
            query = query,
            items = products,
            textExtractor = { it.description }
        )
        
        // Combine and deduplicate results
        val allResults = (titleResults + descriptionResults)
            .distinctBy { it.item.id }
            .sortedByDescending { it.matchScore }
        
        allResults.forEach { result ->
            println("Product: ${result.item.title} (Score: ${result.matchScore})")
        }
    }

    /**
     * Example 4: Custom search configuration
     */
    fun searchWithCustomConfig() {
        val cities = listOf(
            City("New York", "USA", 8_400_000),
            City("Los Angeles", "USA", 3_900_000),
            City("Chicago", "USA", 2_700_000)
        )
        val query = "angeles"
        
        val config = FuzzySearchUtils.SearchConfig(
            threshold = 0.5f,           // Higher threshold for stricter matching
            caseSensitive = false,      // Case insensitive
            normalizeText = true,       // Normalize accents/diacritics
            exactMatchScore = 1.0f,
            startsWithScore = 0.95f,
            containsScore = 0.85f
        )
        
        val results = FuzzySearchUtils.performFuzzySearch(
            query = query,
            items = cities,
            textExtractor = { it.name },
            config = config
        )
        
        results.forEach { result ->
            println("City: ${result.item.name} (Score: ${result.matchScore})")
        }
    }

    /**
     * Example 5: Using search results for highlighting
     */
    fun highlightSearchResults() {
        val items = listOf("Mercedes-Benz", "BMW", "Audi", "Volkswagen")
        val query = "mer"
        
        val results = FuzzySearchUtils.performFuzzySearch(query, items)
        
        results.forEach { result ->
            val (before, match, after) = with(FuzzySearchUtils) {
                result.getHighlightedParts(query)
            }
            
            println("Highlighted: $before**$match**$after")
        }
        // Output: Highlighted: **Mer**cedes-Benz
    }

    /**
     * Example 6: Hierarchical search (like the dropdown component)
     */
    fun hierarchicalSearch() {
        data class Category(val name: String, val items: List<String>)
        
        val categories = listOf(
            Category("Fruits", listOf("Apple", "Banana", "Orange")),
            Category("Vegetables", listOf("Carrot", "Broccoli", "Spinach")),
            Category("Dairy", listOf("Milk", "Cheese", "Yogurt"))
        )
        val query = "app"
        
        val results = mutableListOf<FuzzySearchUtils.SearchResult<String>>()
        
        categories.forEach { category ->
            // Search in category name
            val categoryResults = FuzzySearchUtils.performFuzzySearch(
                query = query,
                items = listOf(category.name)
            )
            results.addAll(categoryResults)
            
            // Search in category items
            val itemResults = FuzzySearchUtils.performFuzzySearch(
                query = query,
                items = category.items
            )
            results.addAll(itemResults)
        }
        
        val sortedResults = results.sortedByDescending { it.matchScore }
        
        sortedResults.forEach { result ->
            println("Match: ${result.matchedText} (Score: ${result.matchScore})")
        }
        // Output: Match: Apple (Score: 0.8)
    }
}
