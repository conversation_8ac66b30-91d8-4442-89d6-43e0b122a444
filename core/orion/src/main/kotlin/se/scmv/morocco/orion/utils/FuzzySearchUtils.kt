package se.scmv.morocco.orion.utils

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import se.scmv.morocco.common.extensions.normalized

/**
 * Utility class for performing fuzzy search operations on various data types.
 * Provides flexible search capabilities with configurable scoring and filtering.
 */
object FuzzySearchUtils {

    /**
     * Data class representing a search result with metadata
     */
    data class SearchResult<T>(
        val item: T,
        val matchScore: Float,
        val matchedText: String,
        val matchStartIndex: Int = -1,
        val matchEndIndex: Int = -1
    )

    // Data classes for search results
    data class SearchableItem<T>(
        val item: T,
        val isParent: Boolean,
        val parent: T? = null,
        val matchScore: Float = 0f,
        val matchedText: String = ""
    )

    /**
     * Configuration for fuzzy search behavior
     */
    data class SearchConfig(
        val threshold: Float = 0.3f,
        val caseSensitive: Boolean = false,
        val normalizeText: Boolean = true,
        val exactMatchScore: Float = 1.0f,
        val startsWithScore: Float = 0.9f,
        val containsScore: Float = 0.8f
    )

    // Fuzzy search implementation using extracted utility
    fun <T> performFuzzySearch(
        query: String,
        items: List<T>,
        textExtractor: (T) -> String,
        childrenExtractor: (T) -> List<T>,
    ): List<SearchableItem<T>> {
        if (query.isBlank()) return emptyList()

        val results = mutableListOf<SearchableItem<T>>()

        items.forEach { parent ->
            // Search in parent name
            val parentResults = performFuzzySearch(
                query = query,
                items = listOf(parent),
                textExtractor = textExtractor
            )

            parentResults.forEach { result ->
                results.add(
                    SearchableItem(
                        item = result.item,
                        isParent = true,
                        matchScore = result.matchScore,
                        matchedText = result.matchedText
                    )
                )
            }

            // Search in children
            val childResults = FuzzySearchUtils.performFuzzySearch(
                query = query,
                items = childrenExtractor(parent),
                textExtractor = textExtractor
            )

            childResults.forEach { result ->
                results.add(
                    SearchableItem(
                        item = result.item,
                        isParent = false,
                        parent = parent,
                        matchScore = result.matchScore,
                        matchedText = result.matchedText
                    )
                )
            }
        }

        // Sort by match score (highest first)
        return results.sortedByDescending { it.matchScore }
    }

    /**
     * Performs fuzzy search on a list of items using a text extraction function
     *
     * @param query The search query string
     * @param items The list of items to search through
     * @param textExtractor Function to extract searchable text from each item
     * @param config Search configuration options
     * @return List of search results sorted by match score (highest first)
     */
    fun <T> performFuzzySearch(
        query: String,
        items: List<T>,
        textExtractor: (T) -> String,
        config: SearchConfig = SearchConfig()
    ): List<SearchResult<T>> {
        if (query.isBlank()) return emptyList()

        val normalizedQuery = if (config.normalizeText) query.normalized() else query
        val results = mutableListOf<SearchResult<T>>()

        items.forEach { item ->
            val text = textExtractor(item)
            val normalizedText = if (config.normalizeText) text.normalized() else text

            val score = calculateFuzzyScore(
                normalizedQuery,
                normalizedText,
                config
            )

            if (score >= config.threshold) {
                val matchIndex =
                    findMatchIndex(normalizedQuery, normalizedText, config.caseSensitive)
                results.add(
                    SearchResult(
                        item = item,
                        matchScore = score,
                        matchedText = text,
                        matchStartIndex = matchIndex.first,
                        matchEndIndex = matchIndex.second
                    )
                )
            }
        }

        return results.sortedByDescending { it.matchScore }
    }

    /**
     * Performs fuzzy search on a list of strings
     *
     * @param query The search query string
     * @param items The list of strings to search through
     * @param config Search configuration options
     * @return List of search results sorted by match score (highest first)
     */
    fun performFuzzySearch(
        query: String,
        items: List<String>,
        config: SearchConfig = SearchConfig()
    ): List<SearchResult<String>> {
        return performFuzzySearch(query, items, { it }, config)
    }

    @Composable
    fun createHighlightedText(text: String, query: String) = buildAnnotatedString {
        // Use the fuzzy search utility to find match indices
        val searchResult = performFuzzySearch(
            query = query,
            items = listOf(text)
        ).firstOrNull()

        if (searchResult != null && searchResult.matchStartIndex >= 0) {
            val highlightedParts = with(FuzzySearchUtils) {
                searchResult.getHighlightedParts(query)
            }
            val beforeMatch = highlightedParts.first
            val match = highlightedParts.second
            val afterMatch = highlightedParts.third

            // Add text before match
            append(beforeMatch)

            // Add highlighted match
            withStyle(
                style = SpanStyle(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            ) {
                append(match)
            }

            // Add text after match
            append(afterMatch)
        } else {
            append(text)
        }
    }

    /**
     * Extension function to highlight matching text in search results
     *
     * @param query The search query used for highlighting
     * @param caseSensitive Whether highlighting should be case sensitive
     * @return Pair of (textBeforeMatch, matchedText, textAfterMatch)
     */
    fun <T> SearchResult<T>.getHighlightedParts(
        query: String,
        caseSensitive: Boolean = false
    ): Triple<String, String, String> {
        if (matchStartIndex < 0 || matchEndIndex < 0) {
            return Triple(matchedText, "", "")
        }

        val beforeMatch = matchedText.substring(0, matchStartIndex)
        val match = matchedText.substring(matchStartIndex, matchEndIndex)
        val afterMatch = matchedText.substring(matchEndIndex)

        return Triple(beforeMatch, match, afterMatch)
    }

    /**
     * Calculates fuzzy match score between query and target text
     *
     * @param query The search query (should be normalized if needed)
     * @param target The target text (should be normalized if needed)
     * @param config Search configuration
     * @return Match score between 0.0 and 1.0
     */
    fun calculateFuzzyScore(
        query: String,
        target: String,
        config: SearchConfig = SearchConfig()
    ): Float {
        if (query.isEmpty() || target.isEmpty()) return 0f

        // Exact match gets highest score
        if (target.equals(query, ignoreCase = !config.caseSensitive)) {
            return config.exactMatchScore
        }

        // Check for contains match
        if (target.contains(query, ignoreCase = !config.caseSensitive)) {
            return if (target.startsWith(query, ignoreCase = !config.caseSensitive)) {
                config.startsWithScore
            } else {
                config.containsScore
            }
        }

        // Calculate Levenshtein distance for fuzzy matching
        val distance = levenshteinDistance(query, target)
        val maxLength = maxOf(query.length, target.length)

        return if (maxLength == 0) 0f
        else maxOf(0f, 1f - (distance.toFloat() / maxLength))
    }

    /**
     * Calculates the Levenshtein distance between two strings
     *
     * @param s1 First string
     * @param s2 Second string
     * @return The minimum number of single-character edits required to transform s1 into s2
     */
    fun levenshteinDistance(s1: String, s2: String): Int {
        val len1 = s1.length
        val len2 = s2.length
        val dp = Array(len1 + 1) { IntArray(len2 + 1) }

        for (i in 0..len1) dp[i][0] = i
        for (j in 0..len2) dp[0][j] = j

        for (i in 1..len1) {
            for (j in 1..len2) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[len1][len2]
    }

    /**
     * Finds the start and end index of the match in the target text
     *
     * @param query The search query
     * @param target The target text
     * @param caseSensitive Whether the search should be case sensitive
     * @return Pair of (startIndex, endIndex) or (-1, -1) if no match found
     */
    private fun findMatchIndex(
        query: String,
        target: String,
        caseSensitive: Boolean
    ): Pair<Int, Int> {
        val startIndex = target.indexOf(query, ignoreCase = !caseSensitive)
        return if (startIndex >= 0) {
            startIndex to (startIndex + query.length)
        } else {
            -1 to -1
        }
    }

    fun <T> groupSearchResults(
        searchResults: List<SearchableItem<T>>,
        idExtractor: (T) -> String,
        childrenReplace: (T, List<T>) -> T
    ): List<T> {
        val parentMap = mutableMapOf<String, T>()
        val childrenMap = mutableMapOf<String, MutableList<T>>()

        searchResults.forEach { searchableItem ->
            val id = idExtractor(searchableItem.item)
            if (searchableItem.isParent) {
                // Add parent if not already added
                if (!parentMap.containsKey(id)) {
                    parentMap[id] = searchableItem.item
                    childrenMap[id] = mutableListOf()
                }
            } else {
                // Add child and ensure parent is included
                val parent = searchableItem.parent!!
                val parentId = idExtractor(parent)
                if (!parentMap.containsKey(parentId)) {
                    parentMap[parentId] = parent
                    childrenMap[parentId] = mutableListOf()
                }
                childrenMap[parentId]?.add(searchableItem.item)
            }
        }

        // Reconstruct the items with filtered children
        return parentMap.values.map { parent ->
            val children = childrenMap[idExtractor(parent)] ?: emptyList()
            childrenReplace(parent, children)
        }
    }
}
