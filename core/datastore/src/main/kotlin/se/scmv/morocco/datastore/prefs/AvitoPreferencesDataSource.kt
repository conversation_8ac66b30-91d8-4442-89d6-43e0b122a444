package se.scmv.morocco.datastore.prefs

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import javax.inject.Inject

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class AvitoPreferencesDataSource @Inject constructor(
    @ApplicationContext private val context: Context,
) {
    suspend fun saveString(key: String, value: String) {
        context.dataStore.edit { prefs ->
            prefs[stringPreferencesKey(key)] = value
        }
    }

    fun observeString(key: String): Flow<String?> {
        return context.dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }
    }

    suspend fun getString(key: String): String? {
        return context.dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }.firstOrNull()
    }

    suspend fun getString(key: String, defaultValue: String): String {
        val prefValue = context.dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }.firstOrNull()
        return prefValue ?: defaultValue
    }

    suspend fun delete(key: String) {
        context.dataStore.edit { prefs ->
            prefs.remove(stringPreferencesKey(key))
        }
    }
}