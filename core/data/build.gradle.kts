plugins {
    id("avito.android.library")
    id("avito.android.hilt")
    alias(libs.plugins.apollo3)
    alias(libs.plugins.google.devtools.ksp)
}

android {
    namespace = "se.scmv.morocco.data"

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    implementation(project(":core:datastore"))
    implementation(project(":core:analytics"))

    // TODO Upgrade to apollo 4.
    // Apollo
    implementation(libs.apollo3.runtime)
    implementation(libs.apollo3.normalized.cache)

    // Retrofit
    implementation(libs.com.squareup.retrofit2.retrofit)
    implementation(libs.com.squareup.retrofit2.retrofit.mock)
    implementation(libs.com.squareup.retrofit2.converter.gson)
    implementation(libs.com.squareup.retrofit2.adapter.rxjava2)

    // Okhttp
    implementation(platform(libs.com.squareup.okhttp3.okhttpBom))
    implementation(libs.com.squareup.okhttp3.okhttp)
    implementation(libs.com.squareup.okhttp3.logging.interceptor)
    implementation(libs.com.squareup.okhttp3.mockwebserver)

    // Room
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    implementation(libs.firebase.config.ktx)
    ksp(libs.androidx.room.compiler)

    // Paging
    implementation(libs.androidx.paging)
    implementation(libs.androidx.paging.common.android)

    // Compressor
    implementation(libs.compressor)

    // Firebase
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.com.google.firebase.config)
}

// Allow references to generated code
kapt {
    correctErrorTypes = true
}

apollo {
    service("avito") {
        packageName.set("se.scmv.morocco")
        generateApolloMetadata.set(true)
        schemaFile.set(File("src/main/graphql/se/scmv/morocco/schema.graphqls"))
        srcDir(file("src/main/graphql/"))
        // Enable generation of metadata for use by downstream modules
        mapScalarToUpload("Upload")
        generateDataBuilders.set(true)
    }
}