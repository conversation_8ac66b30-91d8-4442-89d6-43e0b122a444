query GetListingAds(
    $adFilter: ListingAdFilter,
    $includeNewConstructionAds: <PERSON><PERSON>an,
    $extendPublishedAdsSearchIfNeeded: <PERSON>olean,
    $startNewScroll: Boolean!,
    $publishedAndNCAdsNextScrollId: String,
    $premiumAdsLatestScrollId: String,
    $adProperty: AdSortProperty!,
    $sortOrder: SortOrder!,
    $isStore: Boolean
){
    publishedAndNC: getListingAds(
        query: {
            filters: {
                ad: $adFilter
                seller: {
                    isStore: $isStore
                }
                extension : {
                    includeNewConstructionAds : $includeNewConstructionAds
                    extendPublishedAdsSearchIfNeeded: $extendPublishedAdsSearchIfNeeded
                }
            }
            sort: {
                adProperty: $adProperty
                sortOrder: $sortOrder
            }
            page: {
                size: 35
                number: 0
            }
            metadata: {
                startNewScroll: $startNewScroll
                latestScrollID: $publishedAndNCAdsNextScrollId
            }
        }
    ){
        publishedAndNCAds: ads {
            publishedAndNCDetails: details {
                ... on PublishedAd {
                    ...PublishedAdFragment
                }
                ... on NewConstructionAd{
                    title
                    price {
                        withCurrency
                        withoutCurrency
                    }
                    location {
                        city {
                            id
                            name
                            trackingValue
                        }
                        area {
                            id
                            name
                            trackingValue
                        }
                        address
                    }
                    media {
                        defaultImage {
                            defaultPath
                        }
                        mediaCount
                    }
                    externalLink
                }
            }
            searchExtension {
                extensionType
            }
            publishedAndNCMetadata: metadata {
                nextScrollId
            }
        }
    }
    premium: getListingAds(
        query: {
            filters: {
                ad: $adFilter
                seller: {
                    isStore: $isStore
                }
                extension : {
                    extendPublishedAdsSearchIfNeeded: $extendPublishedAdsSearchIfNeeded
                }
            }
            sort: {
                adProperty: $adProperty
                sortOrder: $sortOrder
            }
            page: {
                size: 3
                number: 0
            }
            metadata: {
                startNewScroll: $startNewScroll
                latestScrollID: $premiumAdsLatestScrollId
            }
        }
    ){
        premiumAds: ads {
            premiumDetails: details {
                ... on PublishedAd{
                    ...PublishedAdFragment
                }
            }
            premiumMetadata: metadata {
                nextScrollId
            }
        }
    }
}