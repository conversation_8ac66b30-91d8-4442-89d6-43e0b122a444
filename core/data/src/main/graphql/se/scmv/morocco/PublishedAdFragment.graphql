fragment PublishedAdFragment on PublishedAd {
    adId
    listId
    title
    description
    listTime
    ...PublishedAdPrice
    discount
    media {
        defaultImage {
            paths {
                standard
            }
        }
        media {
            images {
                paths {
                    standard
                }
            }
            videos {
                defaultPath
            }
        }
        mediaCount
    }
    type{
        key
        name
    }
    category {
        id
        name
        trackingValue
        parent {
            id
            name
            trackingValue
        }
    }
    location {
        city {
            id
            name
            trackingValue
        }
        area {
            id
            name
            trackingValue
        }
    }
    seller{
        ... on StoreProfile {
            isVerifiedSeller
            name
            logo {
                defaultPath
            }
        }
        ... on PrivateProfile {
            name
        }
    }
    ...PublishedAdParam
    isHighlighted
    isHotDeal
    isUrgent
    isInMyFavorites
    isEcommerce
    offersShipping
    sellerType
}