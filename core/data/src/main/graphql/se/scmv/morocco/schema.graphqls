extend type Mutation {
    uploadAdMedia(request: AdMediaUploadInput): AdMediaUploadResponse!

    getAdinUIConfig(category:ID!, adType: AdTypeKey!): AdinStepsConfigResponse!
    submitAd(ad: SubmitAdInput!): SubmitAdResponse! @isAuthenticated
    patchAd(ad: PatchAdInput!): PatchAdResponse! @hasRole(role:ANY)
    bulkActivateAds(adIds: [String!]!): [AdOperation!]! @isAuthenticated
    ActivateAd(adId: String!): AdOperation! @hasRole(role:ANY)
    bulkDeactivateAds(adIds: [String!]!, reason: DeactivationReasonInput!): [AdOperation!]! @isAuthenticated
    bulkDeleteAds(adIds: [String!]!, reason: DeactivationReasonInput!): [AdOperation!]! @isAuthenticated

    saveAd(input: SaveAdInput!): ID! @hasRole(role:ANY)
    unsaveAd(input: UnsaveAdInput!): ID! @hasRole(role:ANY)
    saveSearch(input: SaveSearchInput!): SaveSearchResponse! @hasRole(role:ANY)
    unsaveSearch(input: UnsaveSearchInput!): UnsaveSearchResponse! @hasRole(role:ANY)
}

extend type Query{
    getAdForEdit(adId: ID!): AdForEditResponse! @hasRole(role:ANY)
    getCarVersions(carParams: AdCarParamsInput!): [CarVersions!]!
    checkInsertionEligibility: InsertionEligibilityResponse! @hasRole(role:ANY)
    getMySavedAds(page: Page!): SavedAdsResponse @hasRole(role:ANY)
    getMySavedSearches: MySavedSearches! @hasRole(role:ANY)
    checkActivationCode(input: CheckActivationCodeInput!): CheckActivationCodeResponse!
}

input PriceUpdate {
    "if price is null, the price will be deleted, to avoid updating the price, please set priceUpdate field to null"
    price: Int
}

input PatchAdInput {
    adId: ID!
    "if priceUpdate is null, the price won't be updated"
    priceUpdate: PriceUpdate
    hotDeal: Boolean
    discount: Int
    urgent: Boolean
}

type PatchAdResponse {
    adId: ID!
}

input SaveAdInput {
    listId: ID!
}

input UnsaveAdInput {
    listId: ID!
}

type SavedAdsResponse {
    ads: [PublishedAd!]
    count: Count!
}

type MySavedSearches {
    searches: [MySavedSearch!]!
}

type MySavedSearch {
    id: Int!
    query: String!
    title: String!
    savedTime: String!
}

input CheckActivationCodeInput {
    code: String!
    phone: String
}

type CheckActivationCodeResponse {
    isValid: Boolean!
}

input SaveSearchInput {
    query: String!
}

type SaveSearchResponse {
    id: Int!
    query: String!
}

input UnsaveSearchInput {
    id: Int!
    query: String!
}

type UnsaveSearchResponse {
    query: String!
}

input AdMediaUploadInput {
    type: AdMediaType!
    file: Upload!
}

enum AdMediaType{
    IMAGE
    IMAGE360
    VIDEO
}
input AdCarParamsInput{
    brand:  ID!
    model:  ID!
    year: Int!
    fuelType: ID!
    gearType: ID!
    fiscalPower: Int!
}
type CarVersions{
    versionId: ID!
    label: String!
}
scalar Upload

type SubmitAdResponse {
    adId: ID!
}
input RequestAccountRecoveryInput {
    email: String!
}

type RequestAccountRecoveryResponse {
    email: String!
}

input RequestAccountRecoveryByPhoneInput {
    phone: String!
}

type RequestAccountRecoveryByPhoneResponse {
    phone: String!
}

input ResetMyPasswordInput {
    code: String!
    newPassword: String!
}

type ResetMyPasswordResponse {
    code: String!
}

input RequestPhoneVerificationCodeInput {
    phone: String!
    isUserSigningUp: Boolean
}

type RequestPhoneVerificationCodeResponse {
    phone: String!
}

input VerifyPhoneInput {
    code: String!
}

type VerifyPhoneResponse {
    code: String!
}

type AdMediaUploadResponse {
    id: String!
}

type AdinStepsConfigResponse{
    steps: [AdinStepConfig!]!
}


type AdinNumericParamConfig{
    id: String!
    name: String!
    required: Boolean!
    trackingValue: String!
}

type AdinBooleanParamConfig{
    id: String!
    name: String!
    required: Boolean!
    trackingValue: String!
}

type AdinListParamValueConfig{
    key: ID!
    value: String!
    trackingValue: String!
}

type AdinListParamConfig{
    id: String!
    name: String!
    required: Boolean!
    trackingValue: String!
    values: [AdinListParamValueConfig!]!
}

union AdinParamConfig = AdinNumericParamConfig | AdinBooleanParamConfig | AdinListParamConfig

type AdinStepConfig{
    step: Int!
    params: [AdinParamConfig!]!
}


input SubmitAdNumericParamInput{
    id: String!
    value: Int!
}

input SubmitAdBooleanParamInput{
    id: String!
    value: Boolean!
}

input SubmitAdListKeyParamInput{
    id: String!
    value: String!
}

input SubmitAdParamsInput{
    numeric: [SubmitAdNumericParamInput!]!
    boolean: [SubmitAdBooleanParamInput!]!
    listKey: [SubmitAdListKeyParamInput!]!
}

type AdEditMediaID{
    id: String!
    defaultPath: String!
}

type AdEditMedia{
    "Ad images"
    images: [AdEditMediaID!]!
    "Ad 360° images"
    images360: [AdEditMediaID!]!
    "Ad videos"
    videos: [AdEditMediaID!]!
}


input SubmitAdMediaIDInput{
    id: String!
}

input SubmitAdMediaInput{
    "Ad images"
    images: [SubmitAdMediaIDInput!]!
    "Ad 360° images"
    images360: [SubmitAdMediaIDInput!]!
    "Ad videos"
    videos: [SubmitAdMediaIDInput!]!
}

type AdEditLocation {
    city: ID!
    area: ID
    address: String
}

input SubmitAdLocationInput {
    city: ID!
    area: ID
    address: String
}

input SubmitAdInput {
    adId: ID
    category: ID!
    price: Int!
    type: AdTypeKey!
    title: String!
    description: String!
    name: String!
    phone: String!
    phoneHidden: Boolean!
    location: SubmitAdLocationInput!
    params: SubmitAdParamsInput!
    isSifm: Boolean
    media: SubmitAdMediaInput
}

enum AdDeactivationReason{
    SOLD_ON_SITE
    SOLD_OTHER_MEANS
    EDIT_OR_CHANGE
    RENEW_OR_BUMP
    GIVE_UP
    OTHER
}

enum AdDeactivationSoldOnSiteDuration{
    ONE_DAY
    ONE_WEEK
    ONE_MONTH
    OVER_ONE_MONTH
    DONT_REMEMBER
}

input DeactivationReasonInput {
    reason: AdDeactivationReason!
    "Should be set when reason=SOLD_ON_SITE, to specify the duration on which the item was sold"
    soldOnSiteDuration: AdDeactivationSoldOnSiteDuration
    "Should be set when reason=OTHER, to specify the reason for the deactivation"
    otherReasonText: String
}

type BulkAdOperations{
    adOperations: [AdOperation!]!
}

type AdEditNumericParam{
    id: String!
    numericValue: Int!
}

type AdEditBooleanParam{
    id: String!
    booleanValue: Boolean!
}

type AdEditListKeyParam{
    id: String!
    keyValue: String!
}

union AdEditParam = AdEditNumericParam | AdEditBooleanParam | AdEditListKeyParam

type AdForEditResponse{
    category: ID!
    price: Int!
    type: AdTypeKey!
    title: String!
    description: String!
    name: String!
    phone: String!
    phoneHidden: Boolean!
    location: AdEditLocation!
    params: [AdEditParam!]!
    isSifm: Boolean
    media: AdEditMedia
}


type AdOperationError{
    code: String!
    label: String!
}

type AdOperation{
    adId: String!
    succeded: Boolean!
    error: AdOperationError
}

type Mutation {
    loginByEmail(email: String!, password: String!): LoginResponse
    registerAccount(input: RegisterAccountInput!): RegisterAccountResponse!
    loginByFacebook(token: String!): LoginResponse
    loginWithGoogle(token: String!): LoginResponse
    loginByIdentifier(identifier: String!, password: String!): LoginResponse
    requestAccountRecovery(input: RequestAccountRecoveryInput!): RequestAccountRecoveryResponse!
    requestAccountRecoveryByPhone(input: RequestAccountRecoveryByPhoneInput!): RequestAccountRecoveryByPhoneResponse!
    resetMyPassword(input: ResetMyPasswordInput!): ResetMyPasswordResponse!
    logout: Boolean @isAuthenticated
    requestPhoneVerification(input: RequestPhoneVerificationCodeInput!): RequestPhoneVerificationCodeResponse!
    verifyPhone(input: VerifyPhoneInput!): VerifyPhoneResponse!
}

input RegisterAccountInput {
    name: String
    email: String
    phone: MyAccountPhoneInput!
    location: MyAccountLocationInput
    password: String!
    phoneVerificationCode: String
}
type RegisterAccountResponse {
    info: MyAccountInfo!
    login: LoginResponse
}

type Location {
    city: City!
    address: String
}

type LoginResponse {
    accessToken: String!
    refreshToken: String!
}

directive @isAuthenticated on FIELD_DEFINITION | QUERY
directive @hasRole(role: Role!) on FIELD_DEFINITION | QUERY

enum Role {
    PRIVATE
    CAR_RATING
    NORMAL_STORE_OWNER
    ECOM_STORE_OWNER
    ANY_STORE_OWNER
    ANY
}

type AdMedia{
    images: [AdImage]!
    images360: [AdImage360]!
    videos: [AdVideo]!
}

type AdImage{
    paths: AdImagePaths!
}

type AdImagePaths {
    smallThumbnail: String!
    largeThumbnail: String!
    standard: String!
    fullHd: String!
}

type AdImage360{
    defaultPath: String!
}

type AdVideo{
    defaultPath: String!
}

type AdMediaMetadata {
    defaultImage: AdImage
    media: AdMedia
    mediaCount: Int!
}

type AdLocation {
    city: City!
    area: Area
    address: String
}

type City {
    id: ID!
    name: String!
    trackingValue: String!
}

type Area {
    id: ID!
    name: String!
    trackingValue: String!
}

union UserProfile = PrivateProfile | StoreProfile # union is used to provide __typename

interface UserProfileInterface {
    name: String!
    registrationDay: String!
    phone: ProfilePhone!
}

type PrivateProfile implements UserProfileInterface{
    accountId: ID!
    name: String!
    registrationDay: String!
    phone: ProfilePhone!
}

type ProfilePhone {
    number:  String
    verified: Boolean!
}

type StoreProfile implements UserProfileInterface{
    storeId: ID!
    name: String!
    registrationDay: String!
    phone: ProfilePhone!
    description: StoreDescription!
    location: StoreLocation!
    category: AdCategory!
    logo: StoreLogo
    numberOfActiveAds: Int!
    latestActiveAdsImages: [AdImage]!
    website: String
    profileUrl: String!
    type: StoreType!
    isVerifiedSeller: Boolean!
    displayLabel: Boolean!
    adFiltersAllowed: Boolean!
}

type StoreDescription {
    long: String
    short: String
}

type StoreLocation {
    city: City
    address: String
}

type AdCategory {
    id: ID!
    name: String!
    trackingValue: String!
    parent: AdCategory
}

type StoreLogo {
    defaultPath: String!
}

enum UserProfileType {
    PRIVATE
    STORE
}

type AdType {
    key: AdTypeKey
    name: String!
}

enum StoreType {
    NORMAL
    ECOMMERCE
}

enum AdTypeKey {
    SELL
    BUY
    SWAP
    LET
    RENT
    CO_RENT
    VAC_RENT
    sell
    buy
    swap
    let
    rent
    co_rent
    vac_rent
}
enum InsertionEligibility{
    ELIGIBLE
    INSERTION_LIMIT_REACHED
}

type InsertionEligibilityResponse {
    eligibility: InsertionEligibility!
    maxTotalAds: Int
    totalActiveAds: Int
}

interface AdParamInterface {
    id: ID!
    name: String!
}

union AdParam = TextAdParam | NumericAdParam | BooleanAdParam

type AdParams {
    primary: [AdParam]
    secondary: [AdParam]
    extra: [AdParam]
}

type TextAdParam implements AdParamInterface{
    id: ID!
    name: String!
    textValue: String!
    trackingValue: String!
    paramRawValue: String
}

type NumericAdParam implements AdParamInterface{
    id: ID!
    name: String!
    numericValue: Float!
    unit: String
}

type BooleanAdParam implements AdParamInterface{
    id: ID!
    name: String!
    booleanValue: Boolean!
}

input Page{
    number: Int!
    size: Int!
}

input AdsSearchResultSort {
    adProperty: AdSortProperty!
    sortOrder: SortOrder!
}

enum AdSortProperty {
    LIST_TIME
    PRICE
}

enum SortOrder {
    ASC
    DESC
}

input RangeFilter {
    greaterThanOrEqual: Float
    lessThanOrEqual: Float
}

input AdLocationFilter {
    cityIds: [Int]
    areaIds: [Int]
}

type Count{
    total: Int!
}

type AdPrice {
    withoutCurrency: Int!
    withCurrency: String!
}

type OperationStatus {
    success: Boolean!
}

enum DevicePlatform {
    ANDROID
    IOS
}

extend type Query {
    getUserNotificationPreferences: NotificationPreferences! @hasRole(role:ANY)
    getUserNotificationChannel(channelName: String!): NotificationChannel! @hasRole(role:ANY)
}

extend type Mutation {
    updateUserNotificationPreferences(
        preferences: NotificationPreferencesSettingsInput!
    ): NotificationPreferencesSettings! @hasRole(role:ANY)
    sendEmailVerification(lang: NotificationLang): String! @hasRole(role:ANY)
    registerFirebaseToken(token: FirebaseTokenInput!): OperationStatus! @hasRole(role:ANY)
}

input FirebaseTokenInput {
    platform: DevicePlatform!
    new: [String!]
    revoked: [String!]
}

extend type Query {
    getECommerceAdStats(listId: String!): ECommerceAdStats!
}

type ECommerceAdStats {
    itemsLeft: Int!
    itemsSold: Int!
}

extend type Query {
    "Returns details of total price to be paid by customer"
    getPurchaseOrderSummary(input: PurchaseOrderSummaryInput!): PurchaseOrderSummary! @hasRole(role:PRIVATE)

    "Returns shipping info saved for customer"
    getMySavedShippingInfo: MySavedShippingInfo @hasRole(role:PRIVATE)
}

extend type Mutation {
    "Confirms purchase"
    finalizePurchaseOrder(input: FinalizePurchaseOrderInput!): FinalizePurchaseOrderResult! @hasRole(role:PRIVATE)
}


"Items customer is interested to buy"
input PurchaseOrderSummaryInput {
    items: [PurchaseOrderItemInput]!
}

input PurchaseOrderItemInput {
    adId: ID!
    quantity: Int!
}

"Summary of order"
type PurchaseOrderSummary {
    items: [PurchaseOrderSummaryItem!]!
    shippingFee: Int!
    total: Int!
    availableECommercePaymentMethods: [ECommercePaymentMethod!]!
}

type PurchaseOrderSummaryItem {
    ad: PublishedAd!
    quantity: Int!
    effectivePrice: Int!
    appliedDiscount: Int!
}


"Shipping info saved for customer for re-use"
type MySavedShippingInfo {
    "will contain zero or one element"
    addresses: [ShippingInfo!]!
}

type ShippingInfo {
    fullName: String!
    telephone: String!
    email: String!
    address: String!
}

"Order placement"
input FinalizePurchaseOrderInput {
    items: [PurchaseOrderItemInput]!
    shippingInfo: ShippingInfoInput!
    paymentMethod: ECommercePaymentMethod!
}

input ShippingInfoInput {
    fullName: String!
    telephone: String!
    email: String!
    address: String!
}

type FinalizePurchaseOrderResult {
    paymentURL: String
}

enum ECommercePaymentMethod {
    CC
    COD
}

extend type Query {
    # Buyer
    getMyPurchaseOrders(query: MyPurchaseOrdersQuery!): MyPurchaseOrders! @hasRole(role:PRIVATE)

    # Seller
    getMySaleOrders(query: MySaleOrdersQuery!): MySaleOrders! @hasRole(role:ECOM_STORE_OWNER)
    getMySaleOrdersCounts: MySaleOrdersCounts! @hasRole(role:ECOM_STORE_OWNER)
}

extend type Mutation {
    "Cancel order by calling user (buyer & seller)"
    cancelOrder(id: ID!): ID! @hasRole(role:ANY)

    "Update order status from initiated to preparing (seller)"
    prepareOrder(id: ID!): ID! @hasRole(role:ECOM_STORE_OWNER)

    "Delete cancelled order (seller)"
    deleteOrder(id: ID!): ID! @hasRole(role:ECOM_STORE_OWNER)
}

# Purchase Orders

type MyPurchaseOrders {
    orders: [MyPurchaseOrderListItem!]!
    count: Count!
}

type MyPurchaseOrderListItem {
    id: ID!
    product: MyOrderProduct!
    unitsPurchased: Int!
    status: PurchaseOrderStatus!
    date: String!
    total: Int!
    paymentMethod: ECommercePaymentMethod!
}

input MyPurchaseOrdersQuery {
    filters: MyPurchaseOrdersFilterInput!
    page: Page!
}

input MyPurchaseOrdersFilterInput {
    status: PurchaseOrderStatus!
}

enum PurchaseOrderStatus {
    INITIATED
    PREPARING
    DELIVERING
    DELIVERED
    CANCELLED
}

# Sale Orders


type MySaleOrders {
    orders: [MySaleOrderListItem!]!
    count: Count!
}

type MySaleOrderListItem {
    id: ID!
    product: MyOrderProduct!
    unitsSold: Int!
    status: SaleOrderStatus!
    date: String!
    total: Int!
    paymentMethod: ECommercePaymentMethod!
}

input MySaleOrdersQuery {
    filters: MySaleOrdersFilterInput!
    page: Page!
}

input MySaleOrdersFilterInput {
    status: SaleOrderStatus!
}

enum SaleOrderStatus {
    PENDING
    READY
    DELIVERING
    DELIVERED
    CANCELLED
    RETURNED
}

# Common

type MyOrderProduct {
    sku: ID!
    listId: ID!
    name: String!
    thumbnail: String
}

# Order Counts

type MySaleOrdersCounts{
    countsByStatus: [MySaleOrderStatusCount!]!
}

type MySaleOrderStatusCount{
    status: SaleOrderStatus!
    count: Int!
}


extend type Query {
    getECommerceProductInfo(adId: ID!): ECommerceProductInfo!
}

type ECommerceProductInfo {
    itemsLeft: Int!
    itemsSold: Int!
    discount: Int!
}

type MyProducts {
    products: [MyProductListItem]!
    count: Count!
}

type MyProductListItem {
    sku: ID!
    name: String!
    units: Int!
    price: Int!
    discount: Int!
    status: MyProductStatus!
    views: Int!
    orders: Int!
    vasPackName: String!
    thumbnail: String
}

input MyProductsQuery {
    filters: MyProductsFilterInput!
    page: Page!
}

input MyProductsFilterInput {
    status: MyProductStatus
}

enum MyProductStatus {
    ACTIVATED
    OUT_OF_STOCK
    DEACTIVATED
    DELETED
}

input SubmitProductInput {
    sku: ID
    categoryId: ID!
    name: String!
    description: String!
    price: Int!
    params: SubmitAdParamsInput!
    media: SubmitAdMediaInput
    nbrUnits: Int!
    discount: Int
}

type SubmitProductResponse {
    sku: ID!
    success: Boolean
}

input AdjustProductStockInput {
    sku: ID!
    nbrUnits: Int!
}

input ApplyDiscountInput {
    sku: ID!
    discount: Int!
}

type Query {
    getPublishedAd(query: PublishedAdQuery!): PublishedAdResponse
    getListingAds(query: ListingAdsSearchQuery!): ListingAdsSearchResponse!
    autoCompleteSearch(
        query: SearchAutoCompleteQuery!
    ): SearchAutoCompleteResponse!
}

input PublishedAdQuery {
    listId: String!
}

type PublishedAdResponse {
    ad: PublishedAd
    similarAds: [PublishedAd]
}

type PublishedAd implements ListingAdInterface {
    adId: ID!
    listId: ID!
    type: AdType!
    category: AdCategory!
    title: String!
    description: String!
    price: AdPrice
    oldPrice: AdPrice
    discount: Int
    params: PublishedAdParams!
    media: AdMediaMetadata!
    seller: UserProfile!
    sellerType: UserProfileType!
    location: AdLocation!
    listTime: String!
    reservedDays: [String!]
    salesNoticeLink: String
    isHighlighted: Boolean!
    isInMyFavorites: Boolean!
    offersShipping: Boolean!
    isHotDeal: Boolean
    isUrgent: Boolean
    isEcommerce: Boolean!
    isImmoneuf: Boolean!
    isPremium: Boolean!
    immoneufProjectId: String
}

type PublishedAdParams {
    primary: [AdParam]
    secondary: [AdParam]
    extra: [AdParam]
}

input ListingAdsSearchQuery {
    filters: ListingAdsSearchFilters
    page: Page!
    sort: [AdsSearchResultSort]
    metadata: SearchQueryMetadata
}

input ListingAdsSearchFilters {
    ad: ListingAdFilter
    seller: ListingSellerFilter
    extension: ListingSearchExtensionFilter
}

input ListingAdFilter {
    text: String
    categoryId: Int
    hasImage: Boolean
    hasPrice: Boolean
    type: AdTypeKey
    price: RangeFilter
    location: AdLocationFilter
    offersShipping: Boolean
    offersShippingWithinCity: Boolean
    isHotDeal: Boolean
    isUrgent: Boolean
    isVerifiedSeller: Boolean
    isEcommerce: Boolean
    isImmoneuf: Boolean
    isPremium: Boolean
    params: ListingAdParamsFilters
}


input ListingAdParamsFilters {
    singleMatch: AdParamsSingleMatchFilters
    listMatch: AdParamsListMatchFilters
    rangeMatch: [AdParamsRangeFilter!]
}

input AdParamsSingleMatchFilters {
    text: [AdParamSingleTextFilter!]
    numeric: [AdParamSingleNumericFilter!]
    boolean: [AdParamSingleBooleanFilter!]
}

input AdParamSingleTextFilter {
    name: String!
    value: String!
}

input AdParamSingleNumericFilter {
    name: String!
    value: Float!
}

input AdParamSingleBooleanFilter {
    name: String!
    value: Boolean!
}

input AdParamsListMatchFilters {
    textList: [AdParamListTextFilter]
    numericList: [AdParamListNumericFilter]
}

input AdParamListTextFilter {
    name: String!
    value: [String!]!
}

input AdParamListNumericFilter {
    name: String!
    value: [Float!]!
}

input AdParamsRangeFilter {
    name: String!
    value: RangeFilter!
}

input ListingSellerFilter {
    isStore: Boolean
    storeId: String
}

input ListingSearchExtensionFilter {
    extendPublishedAdsSearchIfNeeded: Boolean
    includeNewConstructionAds: Boolean
}

input SearchQueryMetadata {
    searchUuid: String
    startNewScroll: Boolean
    latestScrollID: String
}

type ListingAdsSearchResponse {
    ads: ListingAdsSearchDetails!
    count: Count!
}

type ListingAdsSearchDetails {
    details: [ListingAd]!
    searchExtension: ListingAdsSearchExtension
    metadata: ListingAdsMetadata
}

type ListingAdsMetadata {
    nextScrollId: String
}

union ListingAd = PublishedAd | NewConstructionAd # union is used to provide __typename

interface ListingAdInterface {
    title: String!
    price: AdPrice
    location: AdLocation!
}

type NewConstructionAd implements ListingAdInterface {
    adId: ID!
    title: String!
    price: AdPrice
    location: AdLocation!
    externalLink: String!
    media: NewConstructionAdImagesMetadata!
}

type NewConstructionAdImagesMetadata {
    defaultImage: NewConstructionAdImage
    mediaCount: Int!
}

type NewConstructionAdImage {
    defaultPath: String!
}

type ListingAdsSearchExtension {
    extensionType: [ListingAdsSearchExtensionType]!
    extendedQuery: ListingAdsSearchExtendedQuery!
}

enum ListingAdsSearchExtensionType {
    TO_WHOLE_CITY
    TO_NEARBY_CITIES
    TO_WHOLE_COUNTRY
    TO_BIGGER_PRICE_RANGE
}

type ListingAdsSearchExtendedQuery {
    extendedPrice: Float
}

input SearchAutoCompleteQuery {
    text: String!
}

type SearchAutoCompleteResponse {
    suggestions: [PublishedAdsSearchAutoCompleteSuggestion]
}

type PublishedAdsSearchAutoCompleteSuggestion {
    suggestion: String!
    category: AdCategory
    city: City
    adType: AdType
    model: TextAdParam
    brand: TextAdParam
    phoneModel: TextAdParam
    phoneBrand: TextAdParam
}

extend type Query {
    getStores(query: ListingStoresSearchQuery!): ListingStoresSearchResponse!
    getStoreDetails(query: StoreDetailsQuery!): StoreProfile
}

input ListingStoresSearchQuery {
    filters: ListingStoresSearchFilters
    aggregationParams: ListingStoresAggregationParams
    page: Page!
    sort: [ListingStoresSearchResultSort]
    metadata: SearchQueryMetadata
}

input ListingStoresSearchFilters {
    text: String
    categoryID: String
    location: ListingStoresLocationFilter
}

input ListingStoresLocationFilter {
    cityIDs: [String]
}

input ListingStoresAggregationParams {
    category: ListingStoresAggregationParam
}

input ListingStoresAggregationParam {
    aggregate: Boolean!
    aggregationsSortOrder: SortOrder!
}

input ListingStoresSearchResultSort {
    storeProperty: StoreSortProperty!
    sortOrder: SortOrder!
}

enum StoreSortProperty {
    MOST_RECENTLY_PUBLISHED
}

input StoreDetailsQuery {
    storeID: String!
}

type ListingStoresSearchResponse{
    stores: ListingStoresSearchDetails!
    count: ListingStoresSearchResultCount!
}

type ListingStoresSearchDetails {
    details: [StoreProfile]!
}

type ListingStoresSearchResultCount {
    totalNumberOfStores: Int!
    aggregatedCounts: [ListingStoresAggregatedCount]
}

type ListingStoresAggregatedCount {
    category: AdCategory!
    numberOfStores: Int!
}

extend type Query {
    getMyAds(filters: MyAdsFilterInput!, pagination: Page!): MyAds! @isAuthenticated
    getMyAdsCount: MyAdsCount! @isAuthenticated
    getMyAd(adId: ID!): MyAd @isAuthenticated
    getMyAccountInfo: MyAccountInfo! @hasRole(role:PRIVATE)
    getMyStoreInfo(storeId: ID!): MyStoreInfo! @hasRole(role:ANY_STORE_OWNER)
}
extend type Mutation {
    updateMyAccountInfo(input: UpdateMyAccountInfoInput!): MyAccountInfo! @hasRole(role:PRIVATE)
    updateMyStoreInfo(input: UpdateMyStoreInfoInput!): MyStoreInfo! @hasRole(role:ANY_STORE_OWNER)
    updateMyPassword(input: UpdateMyPasswordInput!): UpdateMyPasswordResponse! @hasRole(role:ANY)
    deactivateAccount(input: DeactivateAccountInput!): DeactivateAccountResponse! @hasRole(role:PRIVATE)
}

# My Account

## My Account types

type MyAccountInfo {
    id: ID!
    name: String!
    email: String!
    phone: MyAccountPhone!
    location: Location
    registeredAt: String!
}

type MyAccountPhone {
    number:  String!
    isHidden: Boolean!
}

## My Account inputs

input UpdateMyAccountInfoInput {
    name: String!
    phone: MyAccountPhoneInput!
    location: MyAccountLocationInput!
    email: String
}

input MyAccountPhoneInput {
    number:  String!
    isHidden: Boolean!
}

input MyAccountLocationInput {
    cityID: ID!
}

# My Store

## My Store types

type MyStoreInfo {
    name: String!
    email: String!
    category: AdCategory!
    website: String
    description: StoreDescription!
    phones: [MyStorePhone]!
    locations: [StoreLocation]!
    logo: StoreLogo
    startDate: String!
    expiryDate: String!
    points: MyStorePoints!
    extraMediaAllowed: Boolean!
    offersDelivery: Boolean!
    membership: MyStoreMembershipDetails!
    isVerifiedSeller: Boolean!
}

type MyStorePoints {
    count: Int!
    expiryDate: String!
}

type MyStorePhone {
    number:  String!
}

type MyStoreMembershipDetails {
    id: ID!
    name: String!
}

input MyStorePhoneInput {
    number:  String!
}

## My Store inputs

input UpdateMyStoreInfoInput {
    id: ID!
    name: String!
    website: String
    description: StoreDescriptionInput
    phones: [MyStorePhoneInput!]!
    location: StoreLocationInput
}

input StoreDescriptionInput {
    long: String
    short: String
}

input StoreLocationInput {
    address: String
}

# Password Update

input UpdateMyPasswordInput {
    "currentPassword is mandatory for privates, optional for stores"
    currentPassword: String
    newPassword: String!
}

type UpdateMyPasswordResponse {
    id: ID!
}

# Account Deactivation

input DeactivateAccountInput  {
    password: String!
}

type DeactivateAccountResponse {
    success: Boolean!
}

input MyAdsFilterInput{
    "The ads status to fetch"
    status: AdStatus!
    "Currently boosted / non-boosted active ads (optional)"
    boosted: Boolean
}

type MyAds{
    ads: [MyAdSimple!]!
    count: Count!
}

"Status of the ad"
enum AdStatus {
    ACTIVE
    DEACTIVATED
    REFUSED
    DELETED
    PENDING_PAYMENT
    PENDING_REVIEW
    BOOSTED_ACTIVE_ADS
}

enum EditAdStatus {
    USER_EDIT_PENDING_REVIEW
    USER_EDIT_ACCEPTED
    USER_EDIT_REFUSED
}

interface MyAdInterface {
    adId: ID!
    category: AdCategory!
    listId: ID
    mediaCount: Int!
    price: AdPrice
    publishedAt: String
    modifiedAt: String
    refusalReason: String
    type: AdType!
    location: AdLocation!
    lastStateTime: String
    status: AdStatus
    title: String!
    performance: MyAdPerformance
}

type MyAd implements MyAdInterface {
    # common
    adId: ID!
    category: AdCategory!
    listId: ID
    mediaCount: Int!
    price: AdPrice
    discount: Int
    publishedAt: String
    modifiedAt: String
    refusalReason: String
    type: AdType!
    location: AdLocation!
    lastStateTime: String
    status: AdStatus
    title: String!
    performance: MyAdPerformance
    labels: [MyAdLabel!]
    # special fields for this type
    description: String!
    name: String!
    phone: String!
    phoneHidden: Boolean!
    params: AdParams!
    media: AdMedia
    vasPacks: [MyAdVasPack!]!
}

enum MyAdLabel {
    HOTDEAL
    URGENT
}

type VasPackages {
    count: Int!
    packages: [MyAdVasPack!]!
}

type MyAdSimple implements MyAdInterface {
    # common
    adId: ID!
    category: AdCategory!
    listId: ID
    mediaCount: Int!
    price: AdPrice
    discount: Int
    publishedAt: String
    modifiedAt: String
    refusalReason: String
    type: AdType!
    location: AdLocation!
    lastStateTime: String
    status: AdStatus
    title: String!
    performance: MyAdPerformance
    labels: [MyAdLabel!]
    # special fields for this type
    image: AdImage
    lastAppliedVasPack: MyAdVasPack
    vasPackages: VasPackages
    editAdStatus: EditAdStatus
}

"Ad performance for stores ad"
type MyAdPerformance{
    "Number of chat conversations for the ad"
    conversations:  Int!
    "Number of the ad's phone views"
    phoneViews: Int!
    "Number of ad views"
    views: Int!
}

type MyAdVasPack {
    name: String!
    startDate: String
    endDate: String
    status: String
    category: String
}

type CountStatus{
    status: AdStatus!
    count: Int!
}

type MyAdsCount{
    countByStatus: [CountStatus!]!
}

"Status of the notification channels"
enum NotificationChannelStatus {
    UNVERIFIED
    ACTIVE
    DEACTIVATED
}

"Language to use for notification"
enum NotificationLang {
    AR
    FR
}

"User notification channels represents the channels that a user can be notified by (email, push-notification, sms) and their status"
type NotificationChannel {
    "Name of the notification channel (email, phone..) (enum?)"
    name: String!
    "Status of the notification channels"
    status: NotificationChannelStatus!
}

"User notification preferneces settings"
type NotificationPreferencesSettings {
    "Enable/disable ad related notifications"
    adNotificationsEnabled: Boolean
    "Enable/disable vas related notifications"
    vasNotificationsEnabled: Boolean
    "Enable/disable payment related notifications"
    paymentNotificationsEnabled: Boolean
    "Enable/disable newsletter notifications"
    newsletterNotificationsEnabled: Boolean
    "Enable/disable chat notifications"
    chatNotificationsEnabled: Boolean
}

"User notification preferneces settings input"
input NotificationPreferencesSettingsInput {
    "Enable/disable ad related notifications"
    adNotificationsEnabled: Boolean
    "Enable/disable vas related notifications"
    vasNotificationsEnabled: Boolean
    "Enable/disable payment related notifications"
    paymentNotificationsEnabled: Boolean
    "Enable/disable newsletter notifications"
    newsletterNotificationsEnabled: Boolean
    "Enable/disable chat notifications"
    chatNotificationsEnabled: Boolean
}

"User notification preferneces"
type NotificationPreferences {
    "User notification preferneces settings"
    settings: NotificationPreferencesSettings!
}

extend type Mutation {
    collectPerformanceEvent(event: PerformanceEventInput!): PerformanceEvent!
}

input PerformanceEventInput {
    "Name of event"
    name: PerformanceEventName!

    "Required for Ad events"
    adId: ID

    "Required for store events & events on Ads belonging to Stores"
    storeId: ID

    "Required for events on Ads belonging to Privates"
    accountId: ID
}


type PerformanceEvent {
    "Name of event"
    name: PerformanceEventName!

    "Ad on which event happened"
    adId: ID

    "Store / Owner of Ad on which event happened"
    storeId: String

    "Owner of Ad on which event happened"
    accountId: String
}

enum PerformanceEventName {
    AD_DETAILS_VIEWED
    AD_PHONE_NUMBER_SHOWN
    AD_PHONE_NUMBER_CALLED
    AD_SMS_SENT
    AD_VIDEO_PLAYED
    AD_VIDEO_COMPLETED
    AD_WHATSAPP_MSG_SENT
    STORE_PHONE_NUMBER_SHOWN
}

extend type Query {
    getSeoLinks(
        query: SeoLinksSearchQuery!
    ): SeoLinksSearchResponse!
}

input SeoLinksSearchQuery {
    text: String
    sourceLink: String
}

type SeoLinksSearchResponse {
    similarLinks: [SeoLink]!
    popularLinks:  [SeoLink]!
}

type SeoLink {
    url: String!
    label: String!
}

extend type Query {
    getMyPerformanceMetricsFixedRange(range: MyPerformanceMetricsFixedRangeFilter!): [PerformanceMetricData!]! @hasRole(role:NORMAL_STORE_OWNER)

    getMyPerformanceMetricsCustomRange(from: String!, to: String!): [PerformanceMetricData!]! @hasRole(role:NORMAL_STORE_OWNER)
}

"All data points for a specific performance metric"
type PerformanceMetricData {
    "Name of the metric"
    name: String!
    "List of the recorded values for the metric"
    data: [Tuple!]!
    "Sum of the y values in Tuple"
    total: Float!
}

"2D data point"
type Tuple {
    x: String!
    y: Float!
}

enum MyPerformanceMetricsFixedRangeFilter {
    TODAY
    LAST_7_DAYS
    LAST_15_DAYS
    LAST_30_DAYS
    ALLTIME
}

extend type Query {
    getMyChat(query: MyChatQuery): MyChat! @hasRole(role:ANY)
    getMyChatConversation(query: MyChatConversationQuery): MyChatConversation! @hasRole(role:ANY)
    getMyChatConversationWithAd(adId: String!): MyChatConversationWithAd! @hasRole(role:ANY)
}
input MyChatQuery {
    size: Int
    afterTime: String
    beforeTime: String
}
input MyChatConversationQuery {
    id: ID!
    size: Int!
    afterTime: String
    beforeTime: String
}
type MyChat {
    conversations: [MyChatConversation]!
    unreadCount: Int!
}
type MyChatConversation {
    id: ID!
    ad: PublishedAd
    partner: UserProfile
    unreadCount: Int!
    lastMessage: ChatMessage
    messages: [ChatMessage]!
    isBlockedByMe: Boolean!
}
type ChatMessage {
    id: ID!
    isMine: Boolean!
    isUnread: Boolean!
    text: String
    attachment: ChatAttachment
    time: String!
}
type ChatAttachment {
    url: String!
    type: String!
}

type MyChatConversationWithAd {
    found: Boolean!
    conversationId: ID
}

extend type Mutation {
    sendChatMessage(message: SendChatMessageInput!): SendChatMessageResponse! @hasRole(role:ANY)
}
input SendChatMessageInput {
    adId: ID
    conversationId: ID
    text: String
    attachment: Upload
}
type UpdatedConversation {
    id: ID!
    message: ChatMessage!
    isNew: Boolean!
}
type SendChatMessageResponse {
    success: Boolean!
    conversation: UpdatedConversation!
}
extend type Mutation {
    markConversationAsRead(id: ID!): ChatOperationResponse! @hasRole(role:ANY)
    blockConversation(id: ID!): ChatOperationResponse! @hasRole(role:ANY)
    unblockConversation(id: ID!): ChatOperationResponse! @hasRole(role:ANY)
    clearConversation(id: ID!): ChatOperationResponse! @hasRole(role:ANY)
}
type ChatOperationResponse {
    success: Boolean!
}

type Subscription {
    subscribe(token: String!): Notification
}

type Notification {
    title: String!
    event: Event
}

interface Event {
    time: String!
}

type MessageReceived implements Event {
    id: ID!
    conversationId: ID!
    text: String
    attachment: ChatAttachment
    time: String!
}
input CheckAdLimitationRepo {
    category: ID!
    type: AdTypeKey
    phone: String!
    city: ID
}


type CheckAdLimitationStatusResponse {
    userCategoryAdCount: Int!
    configuredCategoryFreeLimit: Int
    configuredCategoryStoreLimit: Int
    listingFee: Int!
    localizedListingFee: String!
    categoryFree: AdCategory
    categoryStore: AdCategory
}

extend type Query{
    checkUserAdLimitationStatus(ad: CheckAdLimitationRepo!): CheckAdLimitationStatusResponse! @hasRole(role:ANY)
}

extend type Query {
    getVasPackages(input: GetVasPackagesInput!): VasPackagesResponse @hasRole(role: ANY)
    getVasPackage(input: GetVasPackageInput!): VasPackagesResponse @hasRole(role: ANY)
}

enum Application {
    ADINSERT
    ADLISTING
}

enum PriceUnit {
    DHS
    POINTS
    AVITO_TOKEN
}

input GetVasPackagesInput {
    application: Application!
    # one of adID or adCategory/adType is required
    adId: ID
    adCategory: String
    adType: AdTypeKey
    city: ID
    area: ID
    brand: ID
}

input GetVasPackageInput {
    packId: ID!
    adId: ID!
}

type VasCategory {
    key: String!
    image: String!
    title: String!
    description: String!
    labels: [String!]
    vasPackages: [VasPackage!]!
}

type VasPackage {
    id: ID!
    durationDays: Int!
    price: Int!
    priceUnit: PriceUnit!
}

type VasPackagesResponse {
    categories: [VasCategory!]!
    availableExecutionSlots: AvailableVasExecutionSlots
}

type AvailableVasExecutionSlots {
    days: [String!]!
    hours: [VasExecutionHours!]!
}

type VasExecutionHours {
    id: ID!
    start: String!
    end: String!
}

extend type Mutation {
    initiateOrder(input: InitOrderInput!): InitOrderResponse! @hasRole(role: ANY)
}

enum PaymentMethod {
    CASH
    CREDIT_CARD
    FATOURATI
    WALLET
}

input InitOrderInput {
    adId: ID!
    packId: ID!
    application: Application!
    slot: SelectedVasExecSlotInput
    paymentMethod: PaymentMethod!
}

input SelectedVasExecSlotInput {
    packExecDay: String!
    packExecTime: ID!
}

type InitOrderResponse {
    # one of link or code will be present
    paymentLink: String
    paymentCode: String
    expiryDate: String
}

