package se.scmv.morocco.data.repository.account

import androidx.paging.PagingSource
import androidx.paging.PagingState
import io.realm.Realm
import io.realm.Sort
import io.realm.kotlin.toFlow
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapLatest
import se.scmv.morocco.data.mappers.toPublishedAd
import se.scmv.morocco.domain.models.AdRecord
import se.scmv.morocco.domain.models.ListingAd

class AccountBookmarkedAdsLocalPagingSource(
    private val localAds: List<AdRecord>
) : PagingSource<Int, ListingAd.Published>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ListingAd.Published> {
        return try {
            val ads = localAds.map { it.toPublishedAd() }
            LoadResult.Page(
                data = ads,
                prevKey = null,
                nextKey = null
            )
        } catch (e: Exception) {
            e.printStackTrace()
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, ListingAd.Published>): Int? {
        return null
    }

    companion object {
        @OptIn(ExperimentalCoroutinesApi::class)
        fun getLocalFavoriteAds(): Flow<List<AdRecord>> {
            return Realm.getDefaultInstance().where(AdRecord::class.java)
                .equalTo("isFavorite", true)
                .sort("recordTime", Sort.DESCENDING)
                .findAll()
                .toFlow()
                .mapLatest { it.toList() }
        }
    }
}