package se.scmv.morocco.data.repository

import androidx.room.withTransaction
import com.apollographql.apollo3.ApolloClient
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.firstOrNull
import se.scmv.morocco.AutoCompleteSearchQuery
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.database.AppDatabase
import se.scmv.morocco.data.database.daos.CarCheckConfigDto
import se.scmv.morocco.data.database.daos.LoanSimulatorConfigDto
import se.scmv.morocco.data.database.entities.CityEntity
import se.scmv.morocco.data.database.entities.TownEntity
import se.scmv.morocco.data.mappers.toAdInsertStep
import se.scmv.morocco.data.mappers.toBankApp
import se.scmv.morocco.data.mappers.toCarChecked
import se.scmv.morocco.data.mappers.toCarToCheck
import se.scmv.morocco.data.mappers.toCategory
import se.scmv.morocco.data.mappers.toCategoryTree
import se.scmv.morocco.data.mappers.toCity
import se.scmv.morocco.data.mappers.toCityWithArea
import se.scmv.morocco.data.mappers.toFilters
import se.scmv.morocco.data.mappers.toListingFilterCategories
import se.scmv.morocco.data.mappers.toOrionFilters
import se.scmv.morocco.data.mappers.toSearchSuggestion
import se.scmv.morocco.data.repository.ad.AdsListingHelper
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.rest.config.ConfigApi
import se.scmv.morocco.data.rest.config.dtos.AdInsertValuesDto
import se.scmv.morocco.data.rest.config.dtos.BankAppDto
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdInsertStep
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.BankApp
import se.scmv.morocco.domain.models.CarCheckConfig
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.ListingFilterCategories
import se.scmv.morocco.domain.models.networkFailure
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.success
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.repositories.RemoteConfigRepository
import javax.inject.Inject

class ConfigRepositoryImpl @Inject constructor(
    private val configApi: ConfigApi,
    private val appDatabase: AppDatabase,
    private val sessionManager: SessionManager,
    private val remoteConfigRepository: RemoteConfigRepository,
    private val apolloClient: ApolloClient,
    private val gson: Gson
) : ConfigRepository {

    override suspend fun getCities(): List<City> {
        return appDatabase.cityDao().getAll().map {
            it.toCity()
        }
    }

    override suspend fun getCityWithArea(cityId: String, areaId: String?): City? {
        return appDatabase.cityDao().getCityWithTowns(cityId)?.toCityWithArea(areaId = areaId)
    }

    override suspend fun getFilters(
        categoryId: String,
        type: String
    ): ListingCategoryFilters.Filters {
        val response = configApi.getFilters(
            categoryId.toIntOrNull() ?: 0,
            "sell",
            LocaleManager.getCurrentLanguage()
        )
        return response.filters.toFilters()
    }

    override suspend fun getFiltersCategories(): Resource<List<CategoryTree>, NetworkAndBackendErrors> {
        return try {
            val result = configApi.getFiltersCategories()
            success(result.mapNotNull { it.toCategoryTree(null) })
        } catch (e: Exception) {
            e.printStackTrace()
            return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getFiltersCategories(categoryId: String): ListingFilterCategories? {
        return try {
            configApi.getFiltersCategories().firstOrNull {
                (it.category.id == categoryId)
            }?.toListingFilterCategories()
        } catch (e: Exception) {
            return null
        }
    }

    override suspend fun getFilters(
        categoryId: String,
        adTypeKey: AdTypeKey
    ): Resource<List<OrionBaseComponent>, NetworkAndBackendErrors> {
        return try {
            val result = configApi.getFilters(
                categoryId = categoryId.toInt(),
                typeId = adTypeKey.name.lowercase()
            )
            success(result.filters.toOrionFilters())
        } catch (e: Exception) {
            e.printStackTrace()
            return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getAdInsertCategories(): Resource<List<CategoryTree>, NetworkAndBackendErrors> {
        return try {
            val result = configApi.getAdInsertCategories()
            success(result.mapNotNull { it.toCategoryTree(null) })
        } catch (e: Exception) {
            e.printStackTrace()
            return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getAdInsertSteps(
        categoryId: String,
        adTypeKey: AdTypeKey
    ): Resource<List<AdInsertStep>, NetworkAndBackendErrors> {
        val account = sessionManager.currentAccount.firstOrNull() as? Account.Connected
            ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        return try {
            val steps = configApi.getAdInsertSteps(
                categoryId = categoryId.toInt(),
                typeId = adTypeKey.name.lowercase()
            )
            success(steps.steps.map { it.toAdInsertStep(account) })
        } catch (e: Exception) {
            return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getCategory(id: String): Resource<Category, NetworkAndBackendErrors> {
        return try {
            val category = configApi.getFiltersCategories().firstOrNull {
                (it.category.id == id)
            } ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
            with(category) {
                Category(
                    id = id,
                    name = name.orEmpty(),
                    icon = icon,
                    trackingName = trackingName.orEmpty(),
                    parent = null
                )
            }.let { success(it) }
        } catch (e: Exception) {
            return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getSupportedBankApps(): Resource<List<BankApp>, NetworkAndBackendErrors> {
        val bankAppsJson = remoteConfigRepository.getString(
            key = RemoteConfigRepository.SUPPORTED_BANK_APPS_ANDROID_KEY
        )
        return try {
            val result = gson.fromJson<List<BankAppDto>>(
                bankAppsJson,
                object : TypeToken<List<BankAppDto>>() {}.type
            )
            success(result.map { it.toBankApp() })
        } catch (e: Exception) {
            failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun getListingCategories(): Resource<List<String>, NetworkAndBackendErrors> {
        val enabledIds =
            remoteConfigRepository.getString(key = RemoteConfigRepository.LISTING_CATEGORIES_IDS_CONFIG_KEY)
                .run {
                    gson.fromJson<Array<Int>?>(this, object : TypeToken<Array<Int>?>() {}.type)
                }
        if (enabledIds.isNullOrEmpty()) return networkFailure(NetworkErrors.UNKNOWN)
        return success(enabledIds.map { it.toString() })
    }

    override suspend fun getLoanSimulatorConfig(
        adCategoryId: String,
        type: String?
    ): LoanSimulatorConfig? {
        val config = remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        return try {
            val dto: LoanSimulatorConfigDto = gson.fromJson(config, LoanSimulatorConfigDto::class.java)
            val isEnabled = dto.loanCategories.any {
                it.category.lowercase() == adCategoryId.lowercase() && it.type.lowercase() == type.orEmpty().lowercase()
            }
            if (isEnabled)
                LoanSimulatorConfig(
                    loanDurations = dto.loanDurations,
                    defaultDuration = dto.defaultDuration,
                    interestPercentage = dto.interestPercentage,
                    redirectionUrl = dto.redirectionUrl
                ) else null
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getCarCheckConfig(
        adCategoryId: String,
        type: String?,
        cityId: String?
    ): CarCheckConfig? {
        val config = remoteConfigRepository.getString(RemoteConfigRepository.CAR_CHECK_CONFIG_KEY)
        return try {
            val dto: CarCheckConfigDto = gson.fromJson(config, CarCheckConfigDto::class.java)

            // Check if the adCategoryId and type match the configuration criteria
            val isEnabled = dto.carCheckConfig.isVisible
                    && dto.carCheckConfig.serviceAvailableCitiesKeys.contains(cityId)
                    && dto.carCheckConfig.categoryTypeConditions.any {
                        it.category.lowercase() == adCategoryId.lowercase()
                        && it.type.lowercase() == type.orEmpty().lowercase()
                    }

            // If enabled, return the appropriate config based on isCarChecked
            if (isEnabled) {
                CarCheckConfig(
                    carToCheck = dto.carCheckConfig.carToCheck.toCarToCheck(),
                    carChecked = dto.carCheckConfig.carChecked.toCarChecked()
                )
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getSearchSuggestions(
        query: String
    ): Resource<List<SearchSuggestion>, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(AutoCompleteSearchQuery(query)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.autoCompleteSearch?.suggestions },
            toDomainModel = { data -> data.mapNotNull { it?.toSearchSuggestion() } }
        )
    }

    override suspend fun buildFiltersValuesFor(searchSuggestion: SearchSuggestion): List<OrionBaseComponentValue> {
        return AdsListingHelper.buildFiltersValuesFor(searchSuggestion)
    }

    override suspend fun refresh() {
        refreshCities()
    }

    private suspend fun refreshCities() {
        coroutineScope {
            val frCitiesDeferred = async { getCities("fr") }
            val arCitiesDeferred = async { getCities("ar") }

            // Await both results at once using awaitAll()
            val (frCities, arCities) = awaitAll(frCitiesDeferred, arCitiesDeferred)
            val cities = mutableListOf<CityEntity>()
            val towns = mutableListOf<TownEntity>()
            frCities.mapIndexed { i, frCity ->
                val arCity = arCities.getOrNull(i)?.let { arCity ->
                    when {
                        arCity.key == frCity.key -> arCity
                        else -> arCities.find { it.key == frCity.key }
                    }
                }
                val key = frCity.key ?: return@mapIndexed null
                val frName = frCity.name ?: return@mapIndexed null
                cities.add(
                    CityEntity(
                        id = key,
                        nameFr = frName,
                        nameAr = arCity?.name.orEmpty(),
                        trackingName = frCity.trackingName.orEmpty()
                    )
                )
                val cityTowns = frCity.children?.values?.mapNotNull { frTown ->
                    val townKey = frTown.key
                    val townFrName = frTown.name
                    if (townKey != null && townFrName != null) {
                        val townArName = arCity?.children?.values?.firstOrNull { it.key == key }?.name
                        TownEntity(
                            id = townKey,
                            nameFr = townFrName,
                            nameAr = townArName.orEmpty(),
                            trackingName = frTown.trackingName.orEmpty(),
                            cityId = frCity.key
                        )
                    } else null
                }
                towns.addAll(cityTowns.orEmpty())
            }.filterNotNull()
            appDatabase.withTransaction {
                appDatabase.cityDao().clear()
                appDatabase.cityDao().insert(cities)
                appDatabase.townDao().insert(towns)
            }
        }
    }

    private suspend fun getCities(lang: String): List<AdInsertValuesDto> {
        return try {
            configApi.getAdInsertSteps(categoryId = 1010, typeId = "sell", lang = lang)
                .steps.firstOrNull()?.fieldGrouping?.find { it.id == "addressgroup" }?.fields
                ?.find { it.id == "city" }
                ?.values.orEmpty()
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    private fun processCategory(
        enabledIds: Array<Int>,
        categoryDto: ListingFilterCategoriesDto,
        categories: MutableList<Category>
    ) {
        if (categoryDto.category.id.toInt() in enabledIds) {
            val category = categoryDto.toCategory()
            categories.add(category)
        }

        categoryDto.children?.forEach { child ->
            processCategory(enabledIds, child, categories)
        }
    }
}