package se.scmv.morocco.data.repository.account

import androidx.datastore.core.DataStore
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import io.realm.Realm
import io.realm.Sort
import io.realm.kotlin.executeTransactionAwait
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext
import se.scmv.morocco.AddToFavoritesMutation
import se.scmv.morocco.CancelOrderMutation
import se.scmv.morocco.GetAccountStatisticsQuery
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMySavedSearchesQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.RemoveFromFavoritesMutation
import se.scmv.morocco.SaveSearchMutation
import se.scmv.morocco.UnsaveSearchMutation
import se.scmv.morocco.UpdateMyAccountInfoMutation
import se.scmv.morocco.UpdateMyStoreInfoMutation
import se.scmv.morocco.data.database.AppDatabase
import se.scmv.morocco.data.mappers.toAccount
import se.scmv.morocco.data.mappers.toAccountStatisticsMetric
import se.scmv.morocco.data.mappers.toGraphqlOrderStatus
import se.scmv.morocco.data.mappers.toMyPerformanceMetricsFixedRangeFilter
import se.scmv.morocco.data.mappers.toPbAccount
import se.scmv.morocco.data.mappers.toRecentSearchEntity
import se.scmv.morocco.data.mappers.toSavedSearch
import se.scmv.morocco.data.mappers.toSearchSuggestion
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.PbCity
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.domain.models.AccountOrderStatus
import se.scmv.morocco.domain.models.AccountStatisticsMetric
import se.scmv.morocco.domain.models.AccountStatisticsRange
import se.scmv.morocco.domain.models.AdRecord
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.type.MyAccountLocationInput
import se.scmv.morocco.type.MyAccountPhoneInput
import se.scmv.morocco.type.MyStorePhoneInput
import se.scmv.morocco.type.SaveAdInput
import se.scmv.morocco.type.SaveSearchInput
import se.scmv.morocco.type.StoreDescriptionInput
import se.scmv.morocco.type.StoreLocationInput
import se.scmv.morocco.type.UnsaveAdInput
import se.scmv.morocco.type.UnsaveSearchInput
import se.scmv.morocco.type.UpdateMyAccountInfoInput
import se.scmv.morocco.type.UpdateMyStoreInfoInput
import javax.inject.Inject

class AccountRepositoryImpl @Inject constructor(
    private val sessionManager: SessionManager,
    private val apolloClient: ApolloClient,
    private val accountDataStore: DataStore<PbAccount>,
    private val configRepository: ConfigRepository,
    private val appDatabase: AppDatabase
) : AccountRepository {

    private val bookmarkedAdsRefreshTrigger = MutableSharedFlow<Unit>()
    private val bookmarkedSearchRefreshTrigger = MutableSharedFlow<Unit>()

    @OptIn(ExperimentalCoroutinesApi::class)
    override val currentAccount: Flow<Account> =
        sessionManager.currentAccount.mapLatest { account ->
            // We need all this logic to get the categoryName when the user changes the language.
            when (account) {
                is Account.Connected.Shop -> {
                    val category = account.store.category
                    when (val result = configRepository.getCategory(category.id)) {
                        is Resource.Success -> {
                            val newCategory = category.copy(name = result.data.name)
                            account.copy(store = account.store.copy(category = newCategory))
                        }

                        is Resource.Failure -> account
                    }
                }

                else -> account
            }
        }

    override suspend fun updatePrivateAccount(
        name: String,
        email: String?,
        phone: String,
        phoneVisibility: Boolean,
        cityId: String
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = UpdateMyAccountInfoInput(
                    name = name,
                    phone = MyAccountPhoneInput(number = phone, isHidden = phoneVisibility),
                    location = MyAccountLocationInput(cityId),
                    email = Optional.presentIfNotNull(email)
                )
                apolloClient.mutation(UpdateMyAccountInfoMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.updateMyAccountInfo },
            toDomainModel = { data ->
                // Trigger account refresh after update
                accountDataStore.updateData { pbAccount ->
                    val privateAccount = pbAccount.connected?.privateAccount
                        ?: return@updateData pbAccount
                    val newContact = privateAccount.contact.copy(
                        name = data.name,
                        email = data.email,
                        phone = data.phone.number,
                        location = data.location?.city?.let { city ->
                            PbCity(
                                id = city.id,
                                name = city.name,
                                trackingName = city.trackingValue
                            )
                        }
                    )
                    pbAccount.copy(
                        connected = pbAccount.connected?.copy(
                            privateAccount = privateAccount.copy(
                                contact = newContact,
                                isPhoneHidden = data.phone.isHidden
                            )
                        )
                    )
                }
            }
        )
    }

    override suspend fun updateShopAccount(
        accountId: String,
        name: String,
        website: String,
        shortDescription: String,
        longDescription: String,
        address: String,
        phones: List<String>
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = UpdateMyStoreInfoInput(
                    id = accountId,
                    name = name,
                    website = Optional.present(website),
                    description = Optional.present(
                        StoreDescriptionInput(
                            long = Optional.present(shortDescription),
                            short = Optional.present(longDescription)
                        )
                    ),
                    phones = phones.map { MyStorePhoneInput(it) },
                    location = Optional.present(
                        StoreLocationInput(address = Optional.present(address))
                    )
                )
                apolloClient.mutation(UpdateMyStoreInfoMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.updateMyStoreInfo },
            toDomainModel = { data ->
                // Trigger account refresh after update
                accountDataStore.updateData { pbAccount ->
                    val shopAccount = pbAccount.connected?.shopAccount
                        ?: return@updateData pbAccount
                    val newContact = shopAccount.contact.copy(
                        name = data.name,
                        location = shopAccount.contact.location?.copy(address = address)
                    )
                    val newStore = shopAccount.store.copy(
                        website = website,
                        shortDescription = data.description.short,
                        longDescription = data.description.long
                    )
                    pbAccount.copy(
                        connected = pbAccount.connected?.copy(
                            shopAccount = shopAccount.copy(contact = newContact, store = newStore)
                        )
                    )
                }
            }
        )
    }

    override fun getAccountOrders(orderStatus: AccountOrderStatus): Flow<PagingData<AccountOrder>> {
        return Pager(
            config = PagingConfig(pageSize = AccountOrdersPagingSource.PAGE_SIZE),
            pagingSourceFactory = {
                AccountOrdersPagingSource(
                    apolloClient = apolloClient,
                    status = orderStatus.toGraphqlOrderStatus()
                )
            }
        ).flow
    }

    override suspend fun cancelOrder(id: String): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(CancelOrderMutation(id)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.cancelOrder },
            toDomainModel = {}
        )
    }

    override suspend fun getStatistics(range: AccountStatisticsRange): Resource<List<AccountStatisticsMetric>, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    GetAccountStatisticsQuery(range.toMyPerformanceMetricsFixedRangeFilter())
                ).execute()
            },
            toData = { apolloResponse ->
                apolloResponse.data
            },
            toDomainModel = { data ->
                data.getMyPerformanceMetricsFixedRange.map { it.toAccountStatisticsMetric() }
            }
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getBookmarkedSearches(): Flow<Resource<List<BookmarkedSearch>, NetworkAndBackendErrors>> {
        return bookmarkedSearchRefreshTrigger.onStart { emit(Unit) }.mapLatest {
            wrapWithErrorHandling(
                call = {
                    apolloClient.query(GetMySavedSearchesQuery()).execute()
                },
                toData = { apolloResponse ->
                    apolloResponse.data?.getMySavedSearches?.searches
                },
                toDomainModel = { data ->
                    data.map { it.toSavedSearch() }
                }
            )
        }
    }

    override suspend fun bookmarkSearch(query: String): Resource<BookmarkedSearchQuery, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(SaveSearchMutation(SaveSearchInput(query))).execute()
            },
            toData = { apolloResponse ->
                apolloResponse.data?.saveSearch
            },
            toDomainModel = { data ->
                bookmarkedSearchRefreshTrigger.emit(Unit)
                with(data) { BookmarkedSearchQuery(id = id, query = query) }
            }
        )
    }

    override suspend fun unBookmarkSearch(savedSearch: BookmarkedSearchQuery): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = with(savedSearch) { UnsaveSearchInput(id = id, query = query) }
                apolloClient.mutation(UnsaveSearchMutation(input)).execute()
            },
            toData = { apolloResponse ->
                apolloResponse.data?.unsaveSearch?.query
            },
            toDomainModel = {
                bookmarkedSearchRefreshTrigger.emit(Unit)
            }
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getBookmarkedAds(): Flow<PagingData<ListingAd.Published>> {
        return combine(
            bookmarkedAdsRefreshTrigger.onStart { emit(Unit) },
            currentAccount
        ) { refresh, account -> Pair(refresh, account) }.flatMapLatest {
            val account = it.second
            if (account.isLogged()) {
                Pager(
                    config = PagingConfig(
                        pageSize = AccountBookmarkedAdsNetworkPagingSource.PAGE_SIZE,
                    ),
                    initialKey = AccountBookmarkedAdsNetworkPagingSource.START_PAGE,
                    pagingSourceFactory = {
                        AccountBookmarkedAdsNetworkPagingSource(apolloClient = apolloClient)
                    }
                ).flow
            } else {
                AccountBookmarkedAdsLocalPagingSource.getLocalFavoriteAds()
                    .flatMapLatest { localAds ->
                        // TODO Think about handling pagination for local ads.
                        Pager(
                            config = PagingConfig(pageSize = 0),
                            pagingSourceFactory = { AccountBookmarkedAdsLocalPagingSource(localAds) }
                        ).flow
                    }
            }
        }
    }

    override suspend fun bookmarkAd(adRecord: AdRecord): Resource<Unit, NetworkAndBackendErrors> {
        val listId = adRecord.listId ?: return Resource.Failure(
            NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN)
        )
        return if (sessionManager.isLogged().not()) {
            try {
                Realm.getDefaultInstance().use { realm ->
                    realm.executeTransaction {
                        realm.copyToRealm(adRecord)
                    }
                    realm.close()
                }
                Resource.Success(Unit)
            } catch (e: Exception) {
                e.printStackTrace()
                Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
            }
        } else wrapWithErrorHandling(
            call = {
                apolloClient.mutation(
                    AddToFavoritesMutation(SaveAdInput(listId.toString()))
                ).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.saveAd },
            toDomainModel = {
                bookmarkedAdsRefreshTrigger.emit(Unit)
            }
        )
    }

    override suspend fun unBookmarkAd(adRecord: AdRecord): Resource<Unit, NetworkAndBackendErrors> {
        val listId = adRecord.listId ?: return Resource.Failure(
            NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN)
        )
        return if (sessionManager.isLogged().not()) {
            Realm.getDefaultInstance().executeTransactionAwait { realm ->
                val query = realm.where(AdRecord::class.java).equalTo("listId", listId)
                query.findFirst()?.let {
                    it.isDeleted = true
                    it.isFavorite = false
                    try {
                        it.deleteFromRealm()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
            Resource.Success(Unit)
        } else wrapWithErrorHandling(
            call = {
                apolloClient.mutation(
                    RemoveFromFavoritesMutation(UnsaveAdInput(listId.toString()))
                ).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.unsaveAd },
            toDomainModel = {
                bookmarkedAdsRefreshTrigger.emit(Unit)
            }
        )
    }

    override suspend fun getBookmarkedSearchSuggestions(): Flow<List<SearchSuggestion>> {
        return appDatabase.recentSearchDao().observeAll().map { entities ->
            entities.map { it.toSearchSuggestion() }
        }
    }

    override suspend fun bookmarkSearchSuggestion(suggestion: SearchSuggestion): Boolean {
        return try {
            appDatabase.recentSearchDao().insert(suggestion.toRecentSearchEntity())
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    override suspend fun unbookmarkSearchSuggestion(suggestion: SearchSuggestion): Boolean {
        return try {
            appDatabase.recentSearchDao().delete(suggestion.uuid)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    override suspend fun refreshAccountFromRemote(): Resource<Unit, NetworkAndBackendErrors> {
        val account = (currentAccount.firstOrNull() as? Account.Connected)
            ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        return when (account) {
            is Account.Connected.Private -> wrapWithErrorHandling(
                call = {
                    apolloClient.query(GetMyAccountInfoQuery()).execute()
                },
                toData = { apolloResponse -> apolloResponse.data?.getMyAccountInfo },
                toDomainModel = { accountData ->
                    val pbAccount = accountData.toAccount().toPbAccount()
                    accountDataStore.updateData { pbAccount }
                    Unit
                }
            )

            is Account.Connected.Shop -> wrapWithErrorHandling(
                call = {
                    val storeId = account.contact.accountId
                    apolloClient.query(GetMyStoreInfoQuery(storeId)).execute()
                },
                toData = { apolloResponse -> apolloResponse.data?.getMyStoreInfo },
                toDomainModel = { accountData ->
                    val pbAccount = accountData.toAccount(
                        accountId = account.contact.accountId,
                        allowedAccess = account.store.allowedAccess
                    ).toPbAccount()
                    accountDataStore.updateData { pbAccount }
                    Unit
                }
            )
        }
    }

    override suspend fun sync(): Boolean = withContext(Dispatchers.IO) {
        try {
            val realm = Realm.getDefaultInstance()
            val localAdIds = realm.where(AdRecord::class.java)
                .equalTo("isFavorite", true)
                .sort("recordTime", Sort.DESCENDING)
                .findAll()
                .orEmpty()
                .mapNotNull { it.listId }
            realm.close()

            val syncedIds = withContext(Dispatchers.IO) {
                val differs = localAdIds.map { listId ->
                    async {
                        val synced = wrapWithErrorHandling(
                            call = {
                                apolloClient.mutation(
                                    AddToFavoritesMutation(SaveAdInput(listId.toString()))
                                ).execute()
                            },
                            toData = { apolloResponse -> apolloResponse.data?.saveAd },
                            toDomainModel = {}
                        )
                        if (synced is Resource.Success) listId else null
                    }
                }
                differs.awaitAll()
            }
            syncedIds.filterNotNull().forEach { syncedId ->
                try {
                    Realm.getDefaultInstance().use { realm ->
                        realm.executeTransactionAwait { transactionRealm ->
                            transactionRealm.where(AdRecord::class.java)
                                .equalTo("listId", syncedId)
                                .findFirst()?.deleteFromRealm()
                        }
                        realm.close()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            localAdIds == syncedIds
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun onSyncFinished() {
        bookmarkedAdsRefreshTrigger.emit(Unit)
    }
}