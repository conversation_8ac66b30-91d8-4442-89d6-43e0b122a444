package se.scmv.morocco.data.repository

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import se.scmv.morocco.CheckActivationCodeQuery
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.LoginByIdentifierMutation
import se.scmv.morocco.LoginWithGoogleMutation
import se.scmv.morocco.LogoutMutation
import se.scmv.morocco.RegisterAccountMutation
import se.scmv.morocco.RegisterFirebaseTokenMutation
import se.scmv.morocco.RequestAccountRecoveryByEmailMutation
import se.scmv.morocco.RequestAccountRecoveryByPhoneMutation
import se.scmv.morocco.RequestPhoneVerificationByPhoneMutation
import se.scmv.morocco.ResetMyPasswordMutation
import se.scmv.morocco.UpdateMyPasswordMutation
import se.scmv.morocco.data.mappers.toAccount
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.rest.hermes.HermesApi
import se.scmv.morocco.data.session.JwtManager
import se.scmv.morocco.data.session.Session
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.data.session.Token
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.type.CheckActivationCodeInput
import se.scmv.morocco.type.DevicePlatform
import se.scmv.morocco.type.FirebaseTokenInput
import se.scmv.morocco.type.MyAccountPhoneInput
import se.scmv.morocco.type.RegisterAccountInput
import se.scmv.morocco.type.RequestAccountRecoveryInput
import se.scmv.morocco.type.RequestPhoneVerificationCodeInput
import se.scmv.morocco.type.ResetMyPasswordInput
import se.scmv.morocco.type.UpdateMyPasswordInput
import java.util.Locale
import javax.inject.Inject

class AuthenticationRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient,
    private val hermesApi: HermesApi,
    private val sessionManager: SessionManager,
    private val accountRepository: AccountRepository
) : AuthenticationRepository {

    companion object {
        private const val SHOP_CATEGORY_ACTIVITY_MISC = 3804
        private const val SHOP_CATEGORY_ACTIVITY_IMMO = 3802
        private const val SHOP_CATEGORY_ACTIVITY_AUTO = 3801
    }

    override suspend fun signIn(
        emailOrPhone: String,
        password: String
    ): Resource<String, NetworkAndBackendErrors> {
        return loginAndGetAccount {
            wrapWithErrorHandling(
                call = {
                    val mutation = LoginByIdentifierMutation(emailOrPhone, password)
                    apolloClient.mutation(mutation).execute()
                },
                toData = { apolloResponse -> apolloResponse.data?.loginByIdentifier },
                toDomainModel = { data ->
                    Token(accessToken = data.accessToken, refreshToken = data.refreshToken)
                }
            )
        }
    }

    override suspend fun signInWithGoogle(idToken: String): Resource<String, NetworkAndBackendErrors> {
        return loginAndGetAccount {
            wrapWithErrorHandling(
                call = {
                    val mutation = LoginWithGoogleMutation(idToken)
                    apolloClient.mutation(mutation).execute()
                },
                toData = { apolloResponse -> apolloResponse.data?.loginWithGoogle },
                toDomainModel = { data ->
                    Token(accessToken = data.accessToken, refreshToken = data.refreshToken)
                }
            )
        }
    }

    override suspend fun sendSmsVerificationForSignUp(
        phoneNumber: String
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RequestPhoneVerificationCodeInput(
                    phone = phoneNumber,
                    isUserSigningUp = Optional.present(true)
                )
                apolloClient.mutation(RequestPhoneVerificationByPhoneMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestPhoneVerification },
            toDomainModel = { data -> data.phone }
        )
    }

    override suspend fun sendSmsVerificationForPasswordReset(phoneNumber: String): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val mutation = RequestAccountRecoveryByPhoneMutation(phoneNumber)
                apolloClient.mutation(mutation).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestAccountRecoveryByPhone },
            toDomainModel = { data -> data.phone }
        )
    }

    override suspend fun validatePhone(
        phoneNumber: String?,
        code: String
    ): Resource<Boolean, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = CheckActivationCodeInput(
                    code = code,
                    phone = Optional.presentIfNotNull(phoneNumber)
                )
                apolloClient.query(CheckActivationCodeQuery(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.checkActivationCode },
            toDomainModel = { data -> data.isValid }
        )
    }

    override suspend fun sendPasswordResetLink(email: String): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RequestAccountRecoveryInput(email = email)
                apolloClient.mutation(RequestAccountRecoveryByEmailMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestAccountRecovery },
            toDomainModel = { data -> data.email }
        )
    }

    override suspend fun registerAccount(
        phoneNumber: String,
        otpCode: String?,
        fullName: String,
        password: String,
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RegisterAccountInput(
                    name = Optional.present(fullName),
                    email = Optional.absent(),
                    phone = MyAccountPhoneInput(number = phoneNumber, isHidden = false),
                    location = Optional.absent(),
                    password = password,
                    phoneVerificationCode = Optional.presentIfNotNull(otpCode)
                )
                apolloClient.mutation(RegisterAccountMutation(input)).execute()
            },
            toData = { apolloResponse ->
                // TODO check with the Backend why the login is nullable !
                val login = apolloResponse.data?.registerAccount?.login
                val info = apolloResponse.data?.registerAccount?.info
                when {
                    login != null && info != null -> login to info
                    else -> null
                }
            },
            toDomainModel = { data ->
                val token = with(data.first) { Token(accessToken, refreshToken) }
                sessionManager.createSession(token = token, account = data.second.toAccount())
                // TODO we return accessToken to use in AccountToken.java,
                //  remove this once the AccountToken.java is no longer used
                token.accessToken
            }
        )
    }

    override suspend fun registerAccount(
        fullName: String,
        phoneNumber: String,
        email: String,
        shopCategory: ShopCategory,
        shopSubscription: ShopSubscription
    ): Resource<Unit, NetworkAndBackendErrors> {
        val activity = when (shopCategory) {
            ShopCategory.MISC -> SHOP_CATEGORY_ACTIVITY_MISC
            ShopCategory.IMMO -> SHOP_CATEGORY_ACTIVITY_IMMO
            ShopCategory.AUTO -> SHOP_CATEGORY_ACTIVITY_AUTO
        }
        val subscription = shopSubscription.name.lowercase().replaceFirstChar {
            if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString()
        }
        return try {
            val response = hermesApi.registerShopAccount(
                activity = activity,
                membership = subscription,
                elapsedTime = 0,
                phoneNumber = phoneNumber,
                email = email
            )
            if (response.isSuccessful) {
                Resource.Success(Unit)
            } else {
                Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
            }
        } catch (e: Exception) {
            Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun resetPassword(
        code: String,
        newPassword: String
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = ResetMyPasswordInput(code = code, newPassword = newPassword)
                apolloClient.mutation(ResetMyPasswordMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.resetMyPassword },
            toDomainModel = { data -> data.code }
        )
    }

    override suspend fun updatePassword(
        currentPassword: String,
        newPassword: String
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = UpdateMyPasswordInput(
                    currentPassword = Optional.present(currentPassword),
                    newPassword = newPassword
                )
                apolloClient.mutation(UpdateMyPasswordMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.updateMyPassword },
            toDomainModel = { }
        )
    }

    override suspend fun signOut(messagingToken: String?): Resource<Boolean, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                if (messagingToken != null) {
                    val unregistered = wrapWithErrorHandling(
                        call = {
                            val token = FirebaseTokenInput(
                                platform = DevicePlatform.ANDROID,
                                new = Optional.Absent,
                                revoked = Optional.presentIfNotNull(listOf(messagingToken))
                            )
                            apolloClient.mutation(RegisterFirebaseTokenMutation(token)).execute()
                        },
                        toData = { apolloResponse -> apolloResponse.data?.registerFirebaseToken },
                        toDomainModel = { data -> data.success }
                    )
                    if (unregistered is Resource.Failure || (unregistered is Resource.Success && unregistered.data.not())) return unregistered
                }
                apolloClient.mutation(LogoutMutation()).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.logout },
            toDomainModel = { loggedOut ->
                if (loggedOut) {
                    sessionManager.endSession()
                }
                loggedOut
            }
        )
    }

    private suspend inline fun loginAndGetAccount(
        login: () -> Resource<Token, NetworkAndBackendErrors>
    ): Resource<String, NetworkAndBackendErrors> {
        return when (val loginResult = login()) {
            is Resource.Success -> {
                val tokenResult =
                    when (val session = JwtManager.mapToSession(loginResult.data.accessToken, 1)) {
                        is Session.Private -> {
                            // Save the accessToken in prefs so that [AuthorizationInterceptor] adds the Bearer token.
                            sessionManager.setAccessToken(loginResult.data.accessToken)
                            val result = wrapWithErrorHandling(
                                call = {
                                    apolloClient.query(GetMyAccountInfoQuery()).execute()
                                },
                                toData = { apolloResponse -> apolloResponse.data?.getMyAccountInfo },
                                toDomainModel = { accountData ->
                                    sessionManager.createSession(
                                        token = Token(
                                            accessToken = loginResult.data.accessToken,
                                            refreshToken = loginResult.data.refreshToken
                                        ),
                                        account = accountData.toAccount()
                                    )
                                    // TODO we return accessToken to use in AccountToken.java,
                                    //  remove this once the AccountToken.java is no longer used
                                    loginResult.data.accessToken
                                }
                            )
                            if (result is Resource.Failure) {
                                sessionManager.endSession()
                            } else {
                                // TODO Remove this code with favorite local saving.
                                GlobalScope.launch {
                                    accountRepository.sync()
                                    accountRepository.onSyncFinished()
                                }
                            }
                            result
                        }

                        is Session.Shop -> {
                            // Save the accessToken in prefs so that [AuthorizationInterceptor] adds the Bearer token.
                            sessionManager.setAccessToken(loginResult.data.accessToken)
                            val result = wrapWithErrorHandling(
                                call = {
                                    val storeId = session.sessionInfo.accountId.toString()
                                    apolloClient.query(GetMyStoreInfoQuery(storeId)).execute()
                                },
                                toData = { apolloResponse -> apolloResponse.data?.getMyStoreInfo },
                                toDomainModel = { accountData ->
                                    val accountId = session.sessionInfo.accountId.toString()
                                    val allowedAccess = session.allowedAccess
                                    sessionManager.createSession(
                                        token = Token(
                                            accessToken = loginResult.data.accessToken,
                                            refreshToken = loginResult.data.refreshToken
                                        ),
                                        account = accountData.toAccount(
                                            accountId = accountId,
                                            allowedAccess = allowedAccess
                                        )
                                    )
                                    // TODO we return accessToken to use in AccountToken.java,
                                    //  remove this once the AccountToken.java is no longer used
                                    loginResult.data.accessToken
                                }
                            )
                            if (result is Resource.Failure) {
                                sessionManager.endSession()
                            }
                            result
                        }

                        Session.NotLogged -> {
                            Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
                        }
                    }
                tokenResult
            }

            is Resource.Failure -> loginResult
        }
    }
}