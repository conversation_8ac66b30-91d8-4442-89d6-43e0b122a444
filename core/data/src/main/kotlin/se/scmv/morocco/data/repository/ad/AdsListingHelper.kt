package se.scmv.morocco.data.repository.ad

import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetListingAdsCountQuery
import se.scmv.morocco.GetListingAdsQuery
import se.scmv.morocco.data.mappers.toGraphQlAdTypeKey
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringListValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionSelectedKeysValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.type.AdLocationFilter
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.type.AdParamSingleBooleanFilter
import se.scmv.morocco.type.AdParamSingleNumericFilter
import se.scmv.morocco.type.AdParamSingleTextFilter
import se.scmv.morocco.type.AdParamsListMatchFilters
import se.scmv.morocco.type.AdParamsRangeFilter
import se.scmv.morocco.type.AdParamsSingleMatchFilters
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.ListingAdFilter
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.RangeFilter
import se.scmv.morocco.type.SortOrder
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

object AdsListingHelper {
    private const val KEY_CATEGORY = "category"
    private const val KEY_KEYWORD = "keyword"
    private const val KEY_HAS_PRICE = "has_price"
    private const val KEY_HAS_IMAGE = "has_image"
    private const val KEY_CITY = "city"
    private const val KEY_AREA = "area"
    private const val KEY_DELIVERY = "delivery"
    private const val KEY_CITY_DELIVERY = "offers_shipping_within_city"
    private const val KEY_HOT_DEAL = "is_hotDeal"
    private const val KEY_URGENT = "is_urgent"
    private const val KEY_ECOMMERCE = "is_ecommerce"
    private const val KEY_NEW_CONSTRUCTION = "is_immoneuf"
    private const val KEY_VERIFIED_SELLER = "verified_seller"
    private const val KEY_PRICE = "price"
    private const val KEY_SELLER_TYPE = "seller_type"
    private const val VALUE_SELLER_TYPE_STORE = "1"
    private val explicitKeys = listOf(
        KEY_CATEGORY,
        KEY_KEYWORD,
        KEY_HAS_PRICE,
        KEY_HAS_IMAGE,
        KEY_CITY,
        KEY_AREA,
        KEY_DELIVERY,
        KEY_CITY_DELIVERY,
        KEY_HOT_DEAL,
        KEY_URGENT,
        KEY_ECOMMERCE,
        KEY_NEW_CONSTRUCTION,
        KEY_VERIFIED_SELLER,
        KEY_PRICE,
        KEY_SELLER_TYPE
    )

    // TODO this should be stored somewhere remotely.
    private const val CONSTRUCTIONS_CATEGORY_ID_1010 = 1010
    private const val CONSTRUCTIONS_CATEGORY_ID_1020 = 1020
    private const val CONSTRUCTIONS_CATEGORY_ID_1040 = 1040
    private const val CONSTRUCTIONS_CATEGORY_ID_1200 = 1200
    private const val CONSTRUCTIONS_CATEGORY_ID_1050 = 1050
    private const val CONSTRUCTIONS_CATEGORY_ID_1060 = 1060
    private const val CONSTRUCTIONS_CATEGORY_ID_1080 = 1080
    private const val VEHICULES = "VEHICULES"

    fun buildGetListingAdsQuery(
        values: List<OrionBaseComponentValue>,
        isNewScroll: Boolean = false,
        publishedAndNCAdsNextScrollId: String? = null,
        premiumAdsNextScrollId: String? = null,
    ): GetListingAdsQuery {
        val explicitValues = values.filter { it.id in explicitKeys }
        val keyStringValues = explicitValues.filterIsInstance<OrionKeyStringValue>()
        val keyBooleanValues = explicitValues.filterIsInstance<OrionKeyBooleanValue>()
        val implicitValues = values.filterNot { it.id in explicitKeys }
        val categoryValue =
            explicitValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()

        return GetListingAdsQuery(
            adFilter = Optional.present(
                buildAdFilterInput(
                    categoryValue = categoryValue,
                    explicitValues = explicitValues,
                    implicitValues = implicitValues,
                    keyStringValues = keyStringValues,
                    keyBooleanValues = keyBooleanValues
                )
            ),
            includeNewConstructionAds = Optional.presentIfNotNull(
                shouldIncludeNewConstructions(
                    categoryId = categoryValue?.categoryId?.toIntOrNull(),
                    type = categoryValue?.adTypeKey
                )
            ),
            extendPublishedAdsSearchIfNeeded = Optional.present(true),
            startNewScroll = isNewScroll,
            publishedAndNCAdsNextScrollId = Optional.presentIfNotNull(publishedAndNCAdsNextScrollId),
            premiumAdsLatestScrollId = Optional.presentIfNotNull(premiumAdsNextScrollId),
            adProperty = AdSortProperty.LIST_TIME,
            sortOrder = SortOrder.DESC,
            isStore = Optional.presentIfNotNull(
                keyStringValues.firstOrNull {
                    it.id == KEY_SELLER_TYPE
                }?.let { it.value == VALUE_SELLER_TYPE_STORE }
            )
        )
    }

    fun buildGetListingAdsCountQuery(
        values: List<OrionBaseComponentValue>,
    ): GetListingAdsCountQuery {
        val explicitValues = values.filter { it.id in explicitKeys }
        val keyStringValues = explicitValues.filterIsInstance<OrionKeyStringValue>()
        val keyBooleanValues = explicitValues.filterIsInstance<OrionKeyBooleanValue>()
        val implicitValues = values.filterNot { it.id in explicitKeys }
        val categoryValue =
            explicitValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()

        return GetListingAdsCountQuery(
            adFilter = Optional.present(
                buildAdFilterInput(
                    categoryValue = categoryValue,
                    explicitValues = explicitValues,
                    implicitValues = implicitValues,
                    keyStringValues = keyStringValues,
                    keyBooleanValues = keyBooleanValues
                )
            ),
            includeNewConstructionAds = Optional.presentIfNotNull(
                shouldIncludeNewConstructions(
                    categoryId = categoryValue?.categoryId?.toIntOrNull(),
                    type = categoryValue?.adTypeKey
                )
            ),
            extendPublishedAdsSearchIfNeeded = Optional.present(true),
            isStore = Optional.presentIfNotNull(
                keyStringValues.firstOrNull {
                    it.id == KEY_SELLER_TYPE
                }?.let { it.value == VALUE_SELLER_TYPE_STORE }
            )
        )
    }

    fun buildAdFilterInput(
        categoryValue: OrionSingleSelectCategoryDropdownValue?,
        explicitValues: List<OrionBaseComponentValue>,
        implicitValues: List<OrionBaseComponentValue>,
        keyStringValues: List<OrionKeyStringValue>,
        keyBooleanValues: List<OrionKeyBooleanValue>,
    ): ListingAdFilter {

        return ListingAdFilter(
            text = Optional.presentIfNotNull(
                keyStringValues.firstOrNull { it.id == KEY_KEYWORD }?.value
            ),
            categoryId = Optional.presentIfNotNull(categoryValue?.categoryId?.toIntOrNull()),
            type = Optional.presentIfNotNull(categoryValue?.adTypeKey?.toGraphQlAdTypeKey()),
            hasPrice = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_HAS_PRICE }?.value
            ),
            hasImage = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_HAS_IMAGE }?.value
            ),
            price = Optional.presentIfNotNull(buildAdPriceFilterInput(explicitValues)),
            location = Optional.presentIfNotNull(buildAdLocationFilterInput(explicitValues)),
            offersShipping = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_DELIVERY }?.value
            ),
            offersShippingWithinCity = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_CITY_DELIVERY }?.value
            ),
            isHotDeal = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_HOT_DEAL }?.value
            ),
            isUrgent = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_URGENT }?.value
            ),
            isEcommerce = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_ECOMMERCE }?.value
            ),
            isImmoneuf = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_NEW_CONSTRUCTION }?.value
            ),
            isVerifiedSeller = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == KEY_VERIFIED_SELLER }?.value
            ),
            params = Optional.presentIfNotNull(buildListingAdParamsFilterInput(implicitValues)),
        )
    }

    private fun buildAdLocationFilterInput(
        values: List<OrionBaseComponentValue>
    ): AdLocationFilter? {
        val keyStringListValue = values.filterIsInstance<OrionKeyStringListValue>()
        val cityIds = keyStringListValue.firstOrNull {
            it.id == KEY_CITY
        }?.items?.mapNotNull { it.toIntOrNull() }
        val areaIds = keyStringListValue.firstOrNull {
            it.id == KEY_AREA
        }?.items?.mapNotNull { it.toIntOrNull() }
        return if (cityIds != null || areaIds != null) AdLocationFilter(
            cityIds = Optional.presentIfNotEmpty(cityIds),
            areaIds = Optional.presentIfNotEmpty(areaIds)
        ) else null
    }

    private fun buildAdPriceFilterInput(
        values: List<OrionBaseComponentValue>
    ): RangeFilter? {
        val keyRangeValue = values.filterIsInstance<OrionKeyRangeValue>().firstOrNull {
            it.id == KEY_PRICE
        }
        return buildRangeFilterInput(keyRangeValue)
    }

    private fun buildRangeFilterInput(value: OrionKeyRangeValue?): RangeFilter? {
        val min = value?.min?.toDouble()
        val max = value?.max?.toDouble()
        return if (min != null || max != null) RangeFilter(
            greaterThanOrEqual = Optional.presentIfNotNull(min),
            lessThanOrEqual = Optional.presentIfNotNull(max)
        ) else null
    }

    private fun buildListingAdParamsFilterInput(
        values: List<OrionBaseComponentValue>,
    ): ListingAdParamsFilters? {
        val singleMatchText = mutableListOf<AdParamSingleTextFilter>()
        val singleMatchBoolean = mutableListOf<AdParamSingleBooleanFilter>()
        val singleMatchNumeric = mutableListOf<AdParamSingleNumericFilter>()
        val listMatchText = mutableListOf<AdParamListTextFilter>()
        val rangeMatch = mutableListOf<AdParamsRangeFilter>()

        values.forEach {
            when (it) {
                is OrionKeyStringValue -> {
                    singleMatchText.add(AdParamSingleTextFilter(name = it.id, value = it.value))
                }

                is OrionKeyBooleanValue -> {
                    singleMatchBoolean.add(
                        AdParamSingleBooleanFilter(name = it.id, value = it.value)
                    )
                }

                is OrionKeyStringListValue -> {
                    listMatchText.add(AdParamListTextFilter(name = it.id, value = it.items))
                }

                is OrionKeyRangeValue -> {
                    val range = buildRangeFilterInput(value = it)
                    if (range != null) {
                        rangeMatch.add(AdParamsRangeFilter(name = it.id, value = range))
                    }
                }

                is OrionSelectedKeysValue -> {
                    singleMatchNumeric.addAll(
                        it.keys.map { key -> AdParamSingleNumericFilter(name = it.id, value = 1.0) }
                    )
                }
            }
        }
        val singleMatch = if (singleMatchText.isNotEmpty() || singleMatchBoolean.isNotEmpty()) {
            Optional.present(
                AdParamsSingleMatchFilters(
                    text = Optional.presentIfNotEmpty(singleMatchText),
                    numeric = Optional.presentIfNotEmpty(singleMatchNumeric),
                    boolean = Optional.presentIfNotEmpty(singleMatchBoolean)
                )
            )
        } else Optional.absent()

        val listMatch = if (listMatchText.isNotEmpty()) {
            Optional.present(AdParamsListMatchFilters(textList = Optional.present(listMatchText)))
        } else Optional.absent()
        val range = Optional.presentIfNotEmpty(rangeMatch)
        return if (singleMatch.isPresent() || listMatch.isPresent() || range.isPresent()) {
            ListingAdParamsFilters(
                singleMatch = singleMatch,
                listMatch = listMatch,
                rangeMatch = range
            )
        } else null
    }

    fun <T : Any> Optional.Companion.presentIfNotEmpty(value: List<T?>?): Optional<List<T>> {
        val nonNullValue = value?.filterNotNull()
        return if (nonNullValue != null && nonNullValue.isNotEmpty()) {
            Optional.present(nonNullValue)
        } else {
            Optional.absent()
        }
    }

    fun <T : Any> Optional<T>.isPresent(): Boolean = this is Optional.Present

    private fun shouldIncludeNewConstructions(categoryId: Int?, type: DomainAdTypeKey?): Boolean? {
        if (categoryId == null && type == null) return null
        return (
                categoryId == CONSTRUCTIONS_CATEGORY_ID_1010 ||
                        categoryId == CONSTRUCTIONS_CATEGORY_ID_1200 ||
                        categoryId == CONSTRUCTIONS_CATEGORY_ID_1020 ||
                        categoryId == CONSTRUCTIONS_CATEGORY_ID_1040) ||
                categoryId == CONSTRUCTIONS_CATEGORY_ID_1050 ||
                categoryId == CONSTRUCTIONS_CATEGORY_ID_1060 ||
                categoryId == CONSTRUCTIONS_CATEGORY_ID_1080
                && type == DomainAdTypeKey.SELL
    }

    fun buildFiltersValuesFor(searchSuggestion: SearchSuggestion): List<OrionBaseComponentValue> {
        val list = mutableListOf<OrionBaseComponentValue>()
        val categoryId = searchSuggestion.category?.id
        val adTypeKey = searchSuggestion.adType?.key
        if (categoryId != null) {
            list.add(
                OrionSingleSelectCategoryDropdownValue(
                    id = KEY_CATEGORY,
                    categoryId = categoryId,
                    adTypeKey = adTypeKey
                )
            )
        }
        searchSuggestion.keyword?.let { keyword ->
            list.add(OrionKeyStringValue(id = KEY_KEYWORD, value = keyword))
        }
        searchSuggestion.city?.id?.let { cityId ->
            list.add(OrionKeyStringListValue(id = KEY_CITY, items = listOf(cityId)))
        }
        val modelKey = searchSuggestion.modelKey
        val modelId = searchSuggestion.modelId
        if (modelKey != null && modelId != null) {
            list.add(OrionKeyStringListValue(id = modelKey, items = listOf(modelId)))
        }
        val brandKey = searchSuggestion.brandKey
        val brandId = searchSuggestion.brandId
        if (brandKey != null && brandId != null) {
            list.add(OrionKeyStringListValue(id = brandKey, items = listOf(brandId)))
        }
        return list
    }
}