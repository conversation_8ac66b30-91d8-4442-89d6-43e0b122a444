package se.scmv.morocco.data.mappers

import se.scmv.morocco.data.repository.utils.buildAdParamIconUrl
import se.scmv.morocco.data.repository.utils.buildCategoryIconUrl
import se.scmv.morocco.domain.models.AdParams
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.Area
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.PriceChangeType
import se.scmv.morocco.fragment.AdParam
import se.scmv.morocco.fragment.PublishedAdFragment
import se.scmv.morocco.fragment.PublishedAdParam
import se.scmv.morocco.fragment.PublishedAdPrice
import se.scmv.morocco.domain.models.AdParam as DomainAdParam

const val PARAM_TEXT_TYPE = "TextAdParam"
const val PARAM_NUMBER_TYPE = "NumericAdParam"
const val PARAM_BOOLEAN_TYPE = "BooleanAdParam"

fun PublishedAdFragment.toPublishedAd(): ListingAd.Published = ListingAd.Published(
    id = adId,
    listId = listId,
    logo = seller.onStoreProfile?.logo?.defaultPath,
    sellerName = seller.onStoreProfile?.name ?: seller.onPrivateProfile?.name,
    date = listTime,
    defaultImage = media.defaultImage?.paths?.standard,
    price = publishedAdPrice.toAdPrice(),
    imageCount = media.media?.images?.size ?: 0,
    videoCount = media.media?.videos?.size ?: 0,
    videoUrl = media.media?.videos?.firstOrNull()?.defaultPath,
    isStore = seller.onStoreProfile != null,
    isEcommerce = isEcommerce,
    adType = type.name,
    offersShipping = offersShipping,
    isFavorite = isInMyFavorites,
    title = title,
    description = description,
    location = City(
        id = location.city.id,
        name = location.city.name,
        trackingName = location.city.trackingValue,
        area = location.area?.let {
            Area(
                id = it.id,
                name = it.name,
                trackingName = it.trackingValue
            )
        }
    ),
    category = with(category) {
        Category(
            id = id,
            name = name,
            icon = buildCategoryIconUrl(id),
            trackingName = trackingValue
        )
    },
    isHighlighted = isHighlighted,
    discount = discount,
    isHotDeal = isHotDeal ?: false,
    isUrgent = isUrgent ?: false,
    isVerifiedSeller = seller.onStoreProfile?.isVerifiedSeller ?: false,
    params = publishedAdParam.toAdParams()
)

fun PublishedAdParam.toAdParams(): AdParams = AdParams(
    primary = params.primary?.mapNotNull { it?.adParam?.toAdParam() } ?: emptyList(),
    secondary = params.secondary?.mapNotNull { it?.adParam?.toAdParam() } ?: emptyList(),
    extra = params.extra?.mapNotNull { it?.adParam?.toAdParam() } ?: emptyList()
)

fun AdParam.toAdParam(): DomainAdParam? {
    return when (__typename) {
        PARAM_TEXT_TYPE -> onTextAdParam?.toParam()

        PARAM_NUMBER_TYPE -> onNumericAdParam?.toParam()

        PARAM_BOOLEAN_TYPE -> onBooleanAdParam?.toParam()

        else -> null
    }
}

fun AdParam.OnTextAdParam.toParam() = DomainAdParam(
    id = id,
    label = name,
    value = textValue,
    iconUrl = buildAdParamIconUrl(id),
    analyticsName = trackingValue
)

fun AdParam.OnNumericAdParam.toParam() = DomainAdParam(
    id = id,
    label = name,
    value = unit?.let { String.format("%s %s", numericValue, it) } ?: "$numericValue",
    iconUrl = buildAdParamIconUrl(id),
    analyticsName = null
)

fun AdParam.OnBooleanAdParam.toParam() = if (booleanValue) DomainAdParam(
    id = id,
    label = name,
    value = "",
    iconUrl = buildAdParamIconUrl(id),
    analyticsName = null
) else null

fun PublishedAdPrice.toAdPrice(): AdPrice {
    if (price == null) return AdPrice.Unavailable

    val changeType = when {
        oldPrice != null -> {
            if (oldPrice.withoutCurrency > price.withoutCurrency) {
                PriceChangeType.DECREASE
            } else PriceChangeType.INCREASE
        }

        else -> null
    }
    return when {
        (isEcommerce || isHotDeal == true) && discount != null -> {
            with(price) {
                val discountedPrice = withoutCurrency - (withoutCurrency * discount / 100)
                val currency = price.withCurrency.split(" ").last()

                AdPrice.Available(
                    current = discountedPrice,
                    currentWithCurrency = String.format("%s %s", discountedPrice, currency),
                    old = price.withoutCurrency,
                    oldWithCurrency = price.withCurrency,
                    changeType = changeType
                )
            }
        }

        else -> {
            AdPrice.Available(
                current = price.withoutCurrency,
                currentWithCurrency = price.withCurrency,
                old = oldPrice?.withoutCurrency,
                oldWithCurrency = oldPrice?.withCurrency,
                changeType = changeType
            )
        }
    }
}