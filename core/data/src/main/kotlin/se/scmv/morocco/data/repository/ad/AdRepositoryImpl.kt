package se.scmv.morocco.data.repository.ad

import android.content.Context
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.api.toUpload
import dagger.hilt.android.qualifiers.ApplicationContext
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.size
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import se.scmv.morocco.GetAccountAdQuery
import se.scmv.morocco.GetAdForEditQuery
import se.scmv.morocco.GetAdLimitationsQuery
import se.scmv.morocco.GetVasPackageQuery
import se.scmv.morocco.GetVasPackagesQuery
import se.scmv.morocco.InitiateOrderMutation
import se.scmv.morocco.SubmitAdMutation
import se.scmv.morocco.UploadAdMediaMutation
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.data.mappers.toAdLimitations
import se.scmv.morocco.data.mappers.toAdToBoost
import se.scmv.morocco.data.mappers.toGraphQlAdTypeKey
import se.scmv.morocco.data.mappers.toGraphQlApplication
import se.scmv.morocco.data.mappers.toGraphQlPaymentMethod
import se.scmv.morocco.data.mappers.toMediaType
import se.scmv.morocco.data.mappers.toVasPacks
import se.scmv.morocco.data.repository.ad.AdInsertHelper.KEY_AREA
import se.scmv.morocco.data.repository.ad.AdInsertHelper.KEY_CITY
import se.scmv.morocco.data.repository.utils.firstErrorKeyOrNull
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdForEdit
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.AdLimitations
import se.scmv.morocco.domain.models.AdToBoost
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.PaymentInfo
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPacks
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.type.AdMediaUploadInput
import se.scmv.morocco.type.CheckAdLimitationRepo
import se.scmv.morocco.type.GetVasPackageInput
import se.scmv.morocco.type.GetVasPackagesInput
import se.scmv.morocco.type.InitOrderInput
import se.scmv.morocco.type.SelectedVasExecSlotInput
import java.io.File
import javax.inject.Inject
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

class AdRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient,
    private val sessionManager: SessionManager,
    private val configRepository: ConfigRepository,
    private val analyticsHelper: AnalyticsHelper,
    @ApplicationContext private val context: Context,
) : AdRepository {

    override suspend fun upsert(
        adID: String?,
        category: Category?,
        adType: AdTypeKey?,
        isLimit: Boolean,
        values: List<OrionBaseComponentValue>,
        vasPack: VasPack?,
        vasPackage: VasPackage?
    ): Resource<String, NetworkAndBackendErrors> {
        val account = sessionManager.currentAccount.firstOrNull() as? Account.Connected
            ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))

        val input = AdInsertHelper.buildSubmitAdInput(
            adID = adID,
            values = values,
            account = account
        )
        val cityId = values.firstOrNull { it.id == KEY_CITY }?.stringValue()
        val areaId = values.firstOrNull { it.id == KEY_AREA }?.stringValue()
        val city = cityId?.let { id ->
            configRepository.getCityWithArea(cityId = id, areaId)
        }
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(SubmitAdMutation(input)).execute()
            },
            toData = { apolloResponse ->
                AdInsertHelper.trackInsertAdAndVasSelection(
                    analyticsHelper = analyticsHelper,
                    account = account,
                    category = category,
                    adTypeKey = adType,
                    city = city,
                    isLimit = isLimit,
                    errorCode = apolloResponse.firstErrorKeyOrNull(),
                    vasPack = vasPack,
                    vasPackage = vasPackage
                )
                apolloResponse.data?.submitAd
            },
            toDomainModel = { data ->
                AdInsertHelper.trackAdInserted(
                    analyticsHelper = analyticsHelper,
                    eventName = AnalyticsEvent.Types.INSERTED_AD,
                    account = account,
                    category = category,
                    adTypeKey = adType,
                    city = city,
                    isLimit = isLimit,
                )
                data.adId
            }
        )
    }

    override suspend fun getAdForEdit(
        adID: String
    ): Resource<AdForEdit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetAdForEditQuery(adID)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getAdForEdit },
            toDomainModel = { data ->
                AdForEdit(
                    values = AdInsertHelper.buildAdEditValues(data),
                    cityId = data.location.city,
                    areaId = data.location.area,
                    // Normally it should be in onAdEditListKeyParam, but as security we check all types.
                    brandId = data.params.find {
                        it.onAdEditBooleanParam?.id == "brand"
                                || it.onAdEditNumericParam?.id == "brand"
                                || it.onAdEditListKeyParam?.id == "brand"
                    }?.let {
                        it.onAdEditListKeyParam?.keyValue
                            ?: it.onAdEditBooleanParam?.booleanValue?.toString()
                            ?: it.onAdEditNumericParam?.numericValue?.toString()
                    }
                )
            }
        )
    }

    override suspend fun uploadMedia(
        file: File,
        adInsertMediaType: AdInsertMediaType
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val inputFile = when (adInsertMediaType) {
                    AdInsertMediaType.IMAGE -> Compressor.compress(
                        context = context,
                        imageFile = file
                    ) {
                        size(4_194_304.toLong())
                    }

                    AdInsertMediaType.VIDEO -> file
                }.toUpload(adInsertMediaType.mimeType)
                apolloClient.mutation(
                    UploadAdMediaMutation(
                        AdMediaUploadInput(type = adInsertMediaType.toMediaType(), file = inputFile)
                    )
                ).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.uploadAdMedia },
            toDomainModel = { data -> data.id }
        )
    }

    override suspend fun getAdLimitations(
        categoryId: String,
        adTypeKey: DomainAdTypeKey
    ): Resource<AdLimitations, NetworkAndBackendErrors> {
        val account = sessionManager.currentAccount.firstOrNull() as? Account.Connected
            ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))

        return wrapWithErrorHandling(
            call = {
                val input = CheckAdLimitationRepo(
                    category = categoryId,
                    type = Optional.presentIfNotNull(adTypeKey.toGraphQlAdTypeKey()),
                    phone = account.connectedContact().phone.orEmpty()
                )
                apolloClient.query(GetAdLimitationsQuery(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.checkUserAdLimitationStatus },
            toDomainModel = { data -> data.toAdLimitations() }
        )
    }

    override suspend fun getAdForBoost(adID: String): Resource<AdToBoost, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetAccountAdQuery(adID)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getMyAd },
            toDomainModel = { data -> data.toAdToBoost() }
        )
    }

    override suspend fun getVasPacks(
        application: VasPacksApplication,
        adID: String?,
        adCategory: String?,
        adType: DomainAdTypeKey?,
        cityId: String?,
        areaId: String?,
        brandId: String?,
    ): Resource<VasPacks, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = GetVasPackagesInput(
                    application = application.toGraphQlApplication(),
                    adId = Optional.presentIfNotNull(adID),
                    adCategory = Optional.presentIfNotNull(adCategory),
                    adType = Optional.presentIfNotNull(adType?.toGraphQlAdTypeKey()),
                    city = Optional.presentIfNotNull(cityId),
                    area = Optional.presentIfNotNull(areaId),
                    brand = Optional.presentIfNotNull(brandId)
                )
                apolloClient.query(GetVasPackagesQuery(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getVasPackages },
            toDomainModel = { data -> data.vasPackagesFragment.toVasPacks() }
        )
    }

    override suspend fun getVasPackage(
        packId: String,
        adId: String
    ): Resource<VasPacks, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = GetVasPackageInput(packId = packId, adId = adId)
                apolloClient.query(GetVasPackageQuery(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getVasPackage },
            toDomainModel = { data -> data.vasPackagesFragment.toVasPacks() }
        )
    }

    override suspend fun proceedPayment(
        adId: String,
        packId: String,
        application: VasPacksApplication,
        paymentMethodType: PaymentMethodType,
        executionSlotsDay: String?,
        executionSlotsTime: String?
    ): Resource<PaymentInfo, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val executionSlots = if (executionSlotsDay != null && executionSlotsTime != null) {
                    Optional.present(
                        SelectedVasExecSlotInput(
                            packExecDay = executionSlotsDay,
                            packExecTime = executionSlotsTime
                        )
                    )
                } else Optional.absent()
                val input = InitOrderInput(
                    adId = adId,
                    packId = packId,
                    application = application.toGraphQlApplication(),
                    paymentMethod = paymentMethodType.toGraphQlPaymentMethod(),
                    slot = executionSlots
                )
                apolloClient.mutation(InitiateOrderMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.initiateOrder },
            toDomainModel = { data ->
                with(data) {
                    when {
                        !paymentCode.isNullOrBlank() && paymentLink.isNullOrBlank() ->
                            PaymentInfo(paymentCode, expiryDate.orEmpty())

                        paymentCode.isNullOrBlank() && !paymentLink.isNullOrBlank() ->
                            PaymentInfo(paymentLink, expiryDate.orEmpty())

                        // In the case of WALLET payment the backend sends empty code and link.
                        else -> PaymentInfo("", expiryDate.orEmpty())
                    }
                }
            }
        )
    }

    override fun getAds(filters: List<OrionBaseComponentValue>): Flow<PagingData<ListingAd>> {
        return Pager(
            // Configure how data is loaded by passing additional properties to
            // PagingConfig, such as prefetchDistance.
            config = PagingConfig(
                pageSize = AdsListingPagingSource.PAGE_SIZE,
                initialLoadSize = AdsListingPagingSource.PAGE_SIZE,
            ),
            initialKey = AdsListingPagingSource.START_PAGE,
        ) { AdsListingPagingSource(filters = filters, apolloClient = apolloClient) }.flow
    }

    override suspend fun getAdsCount(
        filters: List<OrionBaseComponentValue>
    ): Resource<Int, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val query = AdsListingHelper.buildGetListingAdsCountQuery(values = filters)
                apolloClient.query(query).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getListingAds },
            toDomainModel = { data -> data.count.total }
        )
    }
}