package se.scmv.morocco.data.mappers

import com.apollographql.apollo3.api.Optional
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import se.scmv.morocco.GetAccountOrdersQuery
import se.scmv.morocco.GetAccountStatisticsQuery
import se.scmv.morocco.GetActiveAndPendingReviewAdsQuery
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMyAdsQuery
import se.scmv.morocco.GetMySavedSearchesQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.RegisterAccountMutation
import se.scmv.morocco.data.database.entities.RecentSearchEntity
import se.scmv.morocco.data.repository.utils.buildAdParamIconUrl
import se.scmv.morocco.data.repository.utils.buildCategoryIconUrl
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.AccountAdDeactivationReason
import se.scmv.morocco.domain.models.AccountAdDeactivationSoldOnSiteDuration
import se.scmv.morocco.domain.models.AccountDeactivationReasonInput
import se.scmv.morocco.domain.models.AccountInfo
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.domain.models.AccountOrderProduct
import se.scmv.morocco.domain.models.AccountOrderStatus
import se.scmv.morocco.domain.models.AccountStatisticsMetric
import se.scmv.morocco.domain.models.AccountStatisticsMetricName
import se.scmv.morocco.domain.models.AccountStatisticsPoint
import se.scmv.morocco.domain.models.AccountStatisticsRange
import se.scmv.morocco.domain.models.AdParam
import se.scmv.morocco.domain.models.AdParams
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.AdRecord
import se.scmv.morocco.domain.models.AdType
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.MyAccountAdsFilterInput
import se.scmv.morocco.domain.models.MyAdCategory
import se.scmv.morocco.domain.models.MyAdLabel
import se.scmv.morocco.domain.models.MyAdPerformance
import se.scmv.morocco.domain.models.MyAdType
import se.scmv.morocco.domain.models.MyAdTypeKey
import se.scmv.morocco.domain.models.MyAdVasPack
import se.scmv.morocco.domain.models.MyEditAdStatus
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.StoreInfo
import se.scmv.morocco.domain.models.VasPackages
import se.scmv.morocco.fragment.AdFields
import se.scmv.morocco.fragment.AdFields.LastAppliedVasPack
import se.scmv.morocco.fragment.AdFields.Performance
import se.scmv.morocco.type.AdDeactivationReason
import se.scmv.morocco.type.AdDeactivationSoldOnSiteDuration
import se.scmv.morocco.type.AdStatus
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.DeactivationReasonInput
import se.scmv.morocco.type.EditAdStatus
import se.scmv.morocco.type.MyAdsFilterInput
import se.scmv.morocco.type.MyPerformanceMetricsFixedRangeFilter
import se.scmv.morocco.type.PurchaseOrderStatus
import kotlin.math.roundToInt
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

// ACCOUNT/STORE INFO
fun GetMyAccountInfoQuery.GetMyAccountInfo.toAccount() = Account.Connected.Private(
    contact = AccountInfo(
        accountId = id,
        name = name,
        email = email,
        phone = phone.number,
        location = location?.city?.let {
            City(id = it.id, name = it.name, trackingName = it.trackingValue)
        },
        creationDate = registeredAt
    ),
    isPhoneHidden = phone.isHidden
)

fun GetMyStoreInfoQuery.GetMyStoreInfo.toAccount(
    accountId: String,
    allowedAccess: AllowedAccess,
) = Account.Connected.Shop(
    contact = AccountInfo(
        accountId = accountId,
        name = name,
        email = email,
        phone = phones.firstOrNull()?.number,
        location = locations.firstOrNull()?.let { location ->
            location.city?.let { city ->
                City(
                    id = city.id,
                    name = city.name,
                    address = location.address,
                    trackingName = city.trackingValue
                )
            }
        },
        creationDate = startDate
    ),
    store = StoreInfo(
        logoUrl = logo?.defaultPath,
        points = points.count,
        pointsExpirationDate = LocalDate.parse(points.expiryDate),
        membership = membership.name,
        category = with(category) {
            Category(
                id = id,
                name = name,
                icon = buildCategoryIconUrl(id),
                trackingName = trackingValue
            )
        },
        website = website,
        verified = isVerifiedSeller,
        shortDescription = description.short,
        longDescription = description.short,
        cities = locations.mapNotNull { location ->
            location?.city?.let { city ->
                City(
                    id = city.id,
                    name = city.name,
                    address = location.address,
                    trackingName = city.trackingValue
                )
            }
        },
        phones = phones.mapNotNull { it?.number },
        startDate = LocalDate.parse(startDate),
        expirationDate = LocalDate.parse(expiryDate),
        allowedAccess = allowedAccess
    )
)

fun RegisterAccountMutation.Info.toAccount() = Account.Connected.Private(
    contact = AccountInfo(
        accountId = id,
        name = name,
        email = email,
        phone = phone.number,
        location = location?.city?.let {
            City(id = it.id, name = it.name, trackingName = it.trackingValue)
        },
        creationDate = registeredAt
    ),
    isPhoneHidden = phone.isHidden
)

// ACCOUNT ORDERS
fun AccountOrderStatus.toGraphqlOrderStatus(): PurchaseOrderStatus = when (this) {
    AccountOrderStatus.INITIATED -> PurchaseOrderStatus.INITIATED
    AccountOrderStatus.PREPARING -> PurchaseOrderStatus.PREPARING
    AccountOrderStatus.DELIVERING -> PurchaseOrderStatus.DELIVERING
    AccountOrderStatus.DELIVERED -> PurchaseOrderStatus.DELIVERED
    AccountOrderStatus.CANCELLED -> PurchaseOrderStatus.CANCELLED
}

fun PurchaseOrderStatus.toAccountOrderStatus(): AccountOrderStatus = when (this) {
    PurchaseOrderStatus.INITIATED -> AccountOrderStatus.INITIATED
    PurchaseOrderStatus.PREPARING -> AccountOrderStatus.PREPARING
    PurchaseOrderStatus.DELIVERING -> AccountOrderStatus.DELIVERING
    PurchaseOrderStatus.DELIVERED -> AccountOrderStatus.DELIVERED
    PurchaseOrderStatus.CANCELLED -> AccountOrderStatus.CANCELLED
    // Shouldn't happen, but as security.
    PurchaseOrderStatus.UNKNOWN__ -> AccountOrderStatus.INITIATED
}

fun GetAccountOrdersQuery.Order.toAccountOrder() = AccountOrder(
    id = id,
    product = AccountOrderProduct(
        listId = product.listId,
        name = product.name,
        imageUrl = product.thumbnail,
    ),
    unitsPurchased = unitsPurchased,
    status = status.toAccountOrderStatus(),
    // Format yyyy-MM-dd-Thh:mm:ssZ
    date = Instant.parse(date).toLocalDateTime(TimeZone.currentSystemDefault()),
    total = total,
)

// ACCOUNT STATISTICS
fun GetAccountStatisticsQuery.GetMyPerformanceMetricsFixedRange.toAccountStatisticsMetric(): AccountStatisticsMetric =
    AccountStatisticsMetric(
        name = AccountStatisticsMetricName.valueOf(name.uppercase()),
        data = data.map { point ->
            AccountStatisticsPoint(
                date = LocalDate.parse(input = point.x),
                value = point.y.toInt()
            )
        },
        total = total.roundToInt()
    )

fun AccountStatisticsRange.toMyPerformanceMetricsFixedRangeFilter() = when (this) {
    AccountStatisticsRange.ALL_TIME -> MyPerformanceMetricsFixedRangeFilter.ALLTIME
    AccountStatisticsRange.TODAY -> MyPerformanceMetricsFixedRangeFilter.TODAY
    AccountStatisticsRange.LAST_7_DAYS -> MyPerformanceMetricsFixedRangeFilter.LAST_7_DAYS
    AccountStatisticsRange.LAST_15_DAYS -> MyPerformanceMetricsFixedRangeFilter.LAST_15_DAYS
    AccountStatisticsRange.LAST_30_DAYS -> MyPerformanceMetricsFixedRangeFilter.LAST_30_DAYS
}

// ACCOUNT ADS
fun AdFields.toAccountAd(): AccountAd = AccountAd(
    adId = this.adId,
    listId = this.listId,
    mediaCount = this.vasPackages?.count ?: 0,
    priceWithCurrency = price?.withCurrency,
    publishedAt = this.publishedAt?.toLocalDateTimeOrNull(),
    refusalReason = this.refusalReason,
    myAdType = MyAdType(type.name, type.toAdTypeKey()),
    location = City(this.location.city.id, this.location.city.name, this.location.city.id),
    lastStateTime = this.lastStateTime?.toLocalDateTimeOrNull(),
    myAdStatus = this.status?.toAdStatus(),
    myEditAdStatus = this.editAdStatus?.toEditAdStatus(),
    title = this.title,
    category = MyAdCategory(
        category.id,
        category.name,
        category.trackingValue,
        parent = MyAdCategory(
            category.parent?.id,
            category.parent?.name,
            category.parent?.trackingValue,
            null
        )
    ),
    discount = this.discount,
    performance = this.performance?.toAdPerformance(),
    editLimitCheck = null,
    labels = this.labels?.mapNotNull { label ->
        try {
            MyAdLabel.valueOf(label.rawValue)
        } catch (e: IllegalArgumentException) {
            null // Ignore unknown labels
        }
    } ?: emptyList(),
    imageUrl = this.image?.paths?.smallThumbnail,
    lastAppliedVasPack = this.lastAppliedVasPack?.toAdVasPack(),
    vasPackages = VasPackages(packages = this.vasPackages?.packages?.map {
        MyAdVasPack(
            name = it.name,
            startDate = it.startDate,
            endDate = it.endDate,
            status = it.status,
            category = it.category
        )
    }, count = this.vasPackages?.count)
)

// Wrapper functions for different queries
fun GetMyAdsQuery.Ad.toAccountAd(): AccountAd = adFields.toAccountAd()

fun GetActiveAndPendingReviewAdsQuery.Ad.toAccountAd(): AccountAd = adFields.toAccountAd()

fun GetActiveAndPendingReviewAdsQuery.Ad1.toAccountAd(): AccountAd = adFields.toAccountAd()


fun Performance.toAdPerformance(): MyAdPerformance {
    return MyAdPerformance(this.views, this.conversations, this.phoneViews)
}

fun AdFields.Type.toAdTypeKey(): MyAdTypeKey = when (this.key) {
    AdTypeKey.BUY -> MyAdTypeKey.BUY
    AdTypeKey.RENT -> MyAdTypeKey.RENT
    AdTypeKey.SWAP -> MyAdTypeKey.SWAP
    AdTypeKey.LET -> MyAdTypeKey.LET
    AdTypeKey.CO_RENT -> MyAdTypeKey.CO_RENT
    AdTypeKey.VAC_RENT -> MyAdTypeKey.VAC_RENT
    else -> MyAdTypeKey.SELL
}

fun AdStatus.toAdStatus(): MyAccountAdStatus = when (this) {
    AdStatus.DEACTIVATED -> MyAccountAdStatus.DEACTIVATED
    AdStatus.REFUSED -> MyAccountAdStatus.REFUSED
    AdStatus.DELETED -> MyAccountAdStatus.DELETED
    AdStatus.PENDING_PAYMENT -> MyAccountAdStatus.PENDING_PAYMENT
    AdStatus.PENDING_REVIEW -> MyAccountAdStatus.PENDING_REVIEW
    else -> MyAccountAdStatus.ACTIVE
}

fun EditAdStatus.toEditAdStatus(): MyEditAdStatus = when (this) {
    EditAdStatus.USER_EDIT_REFUSED -> MyEditAdStatus.USER_EDIT_REFUSED
    EditAdStatus.USER_EDIT_PENDING_REVIEW -> MyEditAdStatus.USER_EDIT_PENDING_REVIEW
    else -> MyEditAdStatus.USER_EDIT_ACCEPTED
}

fun MyAccountAdsFilterInput.toGraphqlFiltersStatus(): MyAdsFilterInput =
    MyAdsFilterInput(
        boosted = if (this.myAccountAdStatus == MyAccountAdStatus.BOOSTED_ACTIVE_ADS) Optional.Present(
            true
        ) else Optional.Absent,
        status = this.myAccountAdStatus.toAdStatus()
    )


fun LastAppliedVasPack.toAdVasPack() = MyAdVasPack(
    this.name,
    this.startDate,
    this.endDate,
    this.status,
    this.category
)


// Extension function to map MyAccountAdStatus to AdStatus
fun MyAccountAdStatus.toAdStatus(): AdStatus {
    return when (this) {
        MyAccountAdStatus.DEACTIVATED -> AdStatus.DEACTIVATED
        MyAccountAdStatus.REFUSED -> AdStatus.REFUSED
        MyAccountAdStatus.DELETED -> AdStatus.DELETED
        MyAccountAdStatus.PENDING_PAYMENT -> AdStatus.PENDING_PAYMENT
        MyAccountAdStatus.PENDING_REVIEW -> AdStatus.PENDING_REVIEW
        MyAccountAdStatus.BOOSTED_ACTIVE_ADS -> AdStatus.ACTIVE // Check schema to understand why
        else -> AdStatus.ACTIVE

    }
}

fun AdStatus.toAccountAdStatus(): MyAccountAdStatus = when (this) {
    AdStatus.REFUSED -> MyAccountAdStatus.REFUSED
    AdStatus.DEACTIVATED -> MyAccountAdStatus.DEACTIVATED
    AdStatus.DELETED -> MyAccountAdStatus.DELETED
    AdStatus.PENDING_PAYMENT -> MyAccountAdStatus.PENDING_PAYMENT
    AdStatus.PENDING_REVIEW -> MyAccountAdStatus.PENDING_REVIEW
    AdStatus.BOOSTED_ACTIVE_ADS -> MyAccountAdStatus.BOOSTED_ACTIVE_ADS
    else -> MyAccountAdStatus.ACTIVE
}


fun AccountDeactivationReasonInput.toDeactivationReasonInput(): DeactivationReasonInput =
    DeactivationReasonInput(
        reason = mapReason(this.reason),
        soldOnSiteDuration = this.soldOnSiteDuration?.let {
            Optional.Present(
                mapSoldOnSiteDuration(
                    it
                )
            )
        } ?: Optional.Absent,
        otherReasonText = this.otherReasonText?.let { Optional.Present(it) } ?: Optional.Absent
    )

// Helper function to map AccountDeactivationReasonInput.reason to DeactivationReasonInput.reason
private fun mapReason(reason: AccountAdDeactivationReason): AdDeactivationReason =
    when (reason) {
        AccountAdDeactivationReason.SOLD_ON_SITE -> AdDeactivationReason.SOLD_ON_SITE
        AccountAdDeactivationReason.SOLD_OTHER_MEANS -> AdDeactivationReason.SOLD_OTHER_MEANS
        AccountAdDeactivationReason.EDIT_OR_CHANGE -> AdDeactivationReason.EDIT_OR_CHANGE
        AccountAdDeactivationReason.RENEW_OR_BUMP -> AdDeactivationReason.RENEW_OR_BUMP
        AccountAdDeactivationReason.GIVE_UP -> AdDeactivationReason.GIVE_UP
        AccountAdDeactivationReason.OTHER -> AdDeactivationReason.OTHER
    }

// Helper function to map AccountDeactivationReasonInput.soldOnSiteDuration to DeactivationReasonInput.soldOnSiteDuration
private fun mapSoldOnSiteDuration(duration: AccountAdDeactivationSoldOnSiteDuration): AdDeactivationSoldOnSiteDuration =
    when (duration) {
        AccountAdDeactivationSoldOnSiteDuration.ONE_DAY -> AdDeactivationSoldOnSiteDuration.ONE_DAY
        AccountAdDeactivationSoldOnSiteDuration.ONE_WEEK -> AdDeactivationSoldOnSiteDuration.ONE_WEEK
        AccountAdDeactivationSoldOnSiteDuration.ONE_MONTH -> AdDeactivationSoldOnSiteDuration.ONE_MONTH
        AccountAdDeactivationSoldOnSiteDuration.OVER_ONE_MONTH -> AdDeactivationSoldOnSiteDuration.OVER_ONE_MONTH
        AccountAdDeactivationSoldOnSiteDuration.DONT_REMEMBER -> AdDeactivationSoldOnSiteDuration.DONT_REMEMBER
    }

// ACCOUNT BOOKMARKS
fun GetMySavedSearchesQuery.Search.toSavedSearch(): BookmarkedSearch {
    return BookmarkedSearch(
        id = id,
        label = title,
        imageUrL = buildCategoryIconUrl(getCategoryId(query)),
        // FORMAT yyyy-MM-dd hh:mm:ss
        date = LocalDateTime.parse(savedTime.replace(" ", "T")),
        searchQuery = query
    )
}

private fun getCategoryId(queryString: String): String {
    return queryString.split("&".toRegex()).toTypedArray()
        .firstOrNull {
            it.split("=".toRegex()).toTypedArray().firstOrNull() in listOf("cg", "category")
        }?.split("=".toRegex())?.toTypedArray()?.lastOrNull().orEmpty()
}

// Maybe there are old saved ads in the production which doesn't contains some filed,
// so we use some default values to avoid app crashes.
fun AdRecord.toPublishedAd() = ListingAd.Published(
    id = (adId ?: -1).toString(),
    listId = (listId ?: -1).toString(),
    sellerName = name,
    date = date.orEmpty(),
    defaultImage = images.firstOrNull()?.path,
    imageCount = images.size,
    videoCount = 0,
    isStore = accountType == 1,
    isFavorite = isFavorite,
    title = subject.orEmpty(),
    description = body.orEmpty(),
    location = City(
        id = "$city",
        // FIXME find a solution
        name = "",
        trackingName = ""
    ),
    category = Category(
        id = "$category",
        // FIXME find a solution
        name = "",
        icon = "",
        trackingName = ""
    ),
    price = price?.let {
        AdPrice.Available(
            current = it,
            currentWithCurrency = it.toString(),
            old = null,
            oldWithCurrency = null,
            changeType = null
        )
    } ?: AdPrice.Unavailable,
    isHotDeal = false,
    isUrgent = false,
    isVerifiedSeller = false,
    params = AdParams(
        primary = emptyList(),
        secondary = params.mapNotNull {
            if (it.key == null || it.name == null || it.value == null) {
                return@mapNotNull null
            }
            AdParam(
                id = it.key.orEmpty(),
                label = it.name.orEmpty(),
                value = it.value.orEmpty(),
                iconUrl = it.key?.let { id -> buildAdParamIconUrl(id) }.orEmpty(),
                analyticsName = it.name.orEmpty()
            )
        },
        extra = emptyList()
    ),
    discount = null,
    logo = null,
    isHighlighted = false,
    isEcommerce = false,
    offersShipping = false,
)

fun RecentSearchEntity.toSearchSuggestion(): SearchSuggestion = SearchSuggestion(
    uuid = uuid,
    keyword = keyword,
    category = if (categoryId != null && categoryName != null && categoryTrackingName != null)
        Category(
            id = categoryId,
            name = categoryName,
            trackingName = categoryTrackingName,
            icon = buildCategoryIconUrl(categoryId)
        ) else null,
    adType = if (adTypeKey != null && adTypeName != null)
        AdType(
            key = DomainAdTypeKey.valueOf(adTypeKey.uppercase()),
            name = adTypeName,
            trackingName = adTypeName
        ) else null,
    city = if (cityId != null && cityName != null && cityTrackingName != null)
        City(
            id = cityId,
            name = cityName,
            trackingName = cityTrackingName
        ) else null,
    modelKey = modelKey,
    modelId = modelId,
    modelName = modelName,
    brandKey = brandKey,
    brandId = brandId,
    brandName = brandName,
    isHistory = true
)

fun SearchSuggestion.toRecentSearchEntity(): RecentSearchEntity = RecentSearchEntity(
    uuid = uuid,
    createdAt = Clock.System.now().toEpochMilliseconds(),
    keyword = keyword,
    categoryId = category?.id,
    categoryName = category?.name,
    categoryTrackingName = category?.trackingName,
    adTypeKey = adType?.key?.name,
    adTypeName = adType?.name,
    cityId = city?.id,
    cityName = city?.name,
    cityTrackingName = city?.trackingName,
    modelKey = modelKey,
    modelId = modelId,
    modelName = modelName,
    brandKey = brandKey,
    brandId = brandId,
    brandName = brandName,
)