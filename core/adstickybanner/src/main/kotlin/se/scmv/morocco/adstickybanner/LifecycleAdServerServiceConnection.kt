package se.scmv.morocco.adstickybanner

import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import se.scmv.morocco.adstickybanner.network.Lead
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.network.Tags

class LifecycleAdServerServiceConnection(
    private val context: Context,
    private val serviceClass: Class<out Service>,
    private val onServiceConnected: (AdServerService) -> Unit
): DefaultLifecycleObserver {

    private var service: AdServerService? = null
    private var isBound = false

    private val connection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, binder: IBinder?) {
            val serviceBinder = binder as AdServerService.AdServerServiceBinder
            service = serviceBinder.getService()
            onServiceConnected(service!!)
            isBound = true
            updateCategoryId("home", null)
            updateNotifAds("home", listOf(null))
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            service = null
            isBound = false
        }
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        if (!isBound) {
            bindService()
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        unbindService()
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if (!isBound){
            bindService()
        }
    }

    private fun bindService() {
        if (!isBound) {
            val intent = Intent(context, serviceClass)
            context.bindService(intent, connection, Context.BIND_AUTO_CREATE)
        }
    }

    private fun unbindService() {
        if (isBound) {
            context.unbindService(connection)
            isBound = false
        }
    }

    fun updateCategoryId(categoryId: String, cities: List<Int?>?) {
        if (isBound){
            if (categoryId.isNotEmpty() || categoryId != "1"){
                service?.startImageSlide(categoryId, cities)
            }else{
                service?.startImageSlide("home", cities)
            }
        }
    }

    fun updateNotifAds(categoryId: String, cities: List<Int?>?) {
        if (isBound){
            if (categoryId.isNotEmpty() || categoryId != "1"){
                service?.notifAds(categoryId, cities)
            }else{
                service?.notifAds("home", cities)
            }
        }
    }

    fun recordClick(){
        if (isBound){
            service?.recordClick{}
        }
    }

    fun recordImpression(){
        if (isBound){
            service?.recordImpression()
        }
    }

    fun recordCreativeImpression(){
        if (isBound){
            service?.recordCreativeImpression()
        }
    }

    fun recordCreativeClick(){
        if (isBound){
            service?.recordCreativeClick()
        }
    }

    fun storeLeads(campaign: String,name: String, phone: String, email: String, city: String){
        if (isBound){
            service?.storeLeads(LeadRequest(
                listOf(Lead(
                    platform = "avito",
                    campaign = campaign,
                    tags = Tags(
                        name = name,
                        phone = phone,
                        email = email,
                        city = city
                    )
                ))
            ))
        }
    }

}