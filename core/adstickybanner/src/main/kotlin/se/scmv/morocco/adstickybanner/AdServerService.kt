package se.scmv.morocco.adstickybanner

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.repositories.AdServerFormat
import se.scmv.morocco.adstickybanner.repositories.AdServerRepository
import se.scmv.morocco.adstickybanner.repositories.RecordType
import javax.inject.Inject

@AndroidEntryPoint
class AdServerService: Service() {

    companion object {
        private const val TAG = "ADSERVER"
    }

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val binder = AdServerServiceBinder()
    private val _imageSlideFlow = MutableStateFlow("")
    val imageSlideFlow: StateFlow<String> = _imageSlideFlow.asStateFlow()

    private val _notifAdFlow = MutableStateFlow<NotifAd?>(null)
    val notifAdFlow: StateFlow<NotifAd?> = _notifAdFlow.asStateFlow()
    private val urls = mutableListOf<String>()

    @Inject
    lateinit var adServerRepository: AdServerRepository

    private val adSessionFlow = MutableStateFlow(AdSession())

    // Only one image slide coroutine at a time
    private var imageSlideJob: Job? = null

    override fun onBind(intent: Intent?): IBinder = binder

    inner class AdServerServiceBinder: Binder() {
        fun getService(): AdServerService = this@AdServerService
    }

    fun startImageSlide(categoryId: String, cities: List<Int?>?){
        imageSlideJob?.cancel() // Cancel previous job if running
        imageSlideJob = scope.launch {
            try {
                urls.clear()
                val imageResponse = adServerRepository.getActiveSlideAds(categoryId, cities)
                adSessionFlow.value = adSessionFlow.value.copy(
                    campaignId = imageResponse?.id,
                    adFormats = listOf(AdServerFormat.SLIDE),
                    urlAdToBrowse = imageResponse?.campaignData?.redirectLink
                )
                if (imageResponse != null){
                    urls.addAll(imageResponse.campaignData.creative)
                    var index = 0
                    while (true){
                        _imageSlideFlow.value = urls[index]
                        index = (index + 1) % urls.size
                        delay(5000)
                    }
                }else {
                    _imageSlideFlow.value = ""
                }
            }catch (e: Exception){
                _imageSlideFlow.value = ""
                e.printStackTrace()
            }
        }
    }

    fun recordClick(openWebViewActivity: (String) -> Unit) {
        scope.launch {
            val session = adSessionFlow.value
            val shouldOpenWebView = session.adFormats.firstOrNull { it == AdServerFormat.SLIDE } != null

            session.adFormats.forEach {
                when (it) {
                    AdServerFormat.SLIDE -> record(RecordType.CLICK)
                    AdServerFormat.NOTIF -> record(RecordType.CLICK_BUBBLE_ANDROID)
                }
            }

            if (shouldOpenWebView && !session.urlAdToBrowse.isNullOrEmpty()) {
                openWebViewActivity(session.urlAdToBrowse!!)
            }
        }
    }

    fun recordImpression(){
        val session = adSessionFlow.value
        session.adFormats.forEach {
            when(it){
                AdServerFormat.SLIDE -> record(RecordType.IMPRESSION)
                AdServerFormat.NOTIF -> record(RecordType.VIEW_BUBBLE_ANDROID)
            }
        }
    }

    fun recordCreativeImpression(){
        scope.launch {
            record(RecordType.VIEW_CREA_ANDROID)
        }
    }

    fun recordCreativeClick(){
        scope.launch {
            record(RecordType.CLICK_CREA_ANDROID)
        }
    }

    private fun record(recordType: RecordType){
        val session = adSessionFlow.value
        if (!session.campaignId.isNullOrEmpty()){
            Log.d(TAG, "Attempting to record: campaignId=${session.campaignId}, recordType=$recordType")
            scope.launch {
                Log.d(TAG, "Sending record to server: campaignId=${session.campaignId}, recordType=$recordType")
                adServerRepository.recordAdsClickOrImpression(session.campaignId?: "", recordType)
            }
        }
    }


    fun notifAds(categoryId: String, cities: List<Int?>?){
        scope.launch {
            try {
                val notifAds = adServerRepository.getNotifAds(categoryId, cities)
                adSessionFlow.value = adSessionFlow.value.copy(
                    campaignId = notifAds?.firstOrNull()?.id,
                    adFormats = listOf(AdServerFormat.NOTIF)
                )
                if (!notifAds.isNullOrEmpty()){
                    _notifAdFlow.update { notifAds[0] }
                }else{
                    _notifAdFlow.update { null }
                }
            }catch (e: Exception){
                e.printStackTrace()
                _notifAdFlow.update { null }
            }
        }
    }

    fun storeLeads(leadRequest: LeadRequest){
        scope.launch {
            try {
                adServerRepository.storeLeads(leadRequest)
            }catch (e: Exception){
                e.printStackTrace()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cancel all coroutines to avoid leaks
        scope.cancel()
        imageSlideJob?.cancel()
    }
}

data class AdSession(
    val campaignId: String? = null,
    val adFormats: List<AdServerFormat?> = emptyList(),
    val urlAdToBrowse: String? = null
)