package se.scmv.morocco.adstickybanner.repositories

import android.util.Log
import kotlinx.coroutines.CancellationException
import se.scmv.morocco.adstickybanner.dtos.toNotifAds
import se.scmv.morocco.adstickybanner.dtos.toSlideAds
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.models.SlideAds
import se.scmv.morocco.adstickybanner.network.AdServerAdvertisingApi
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.network.LeadsApiResponse
import se.scmv.morocco.adstickybanner.network.LeadsRequestApi
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdServerRepositoryImpl @Inject constructor(
    private val adServerApi: AdServerAdvertisingApi,
    private val leadsRequestApi: LeadsRequestApi
): AdServerRepository {

    companion object {
        private const val TAG = "ADSERVER"
    }

    /**
     * Fetches the active slide ad matching the given category and cities.
     * @return SlideAds or null if not found or error.
     */
    override suspend fun getActiveSlideAds(categoryId: String, cities: List<Int?>?): SlideAds? {
        return runCatching {
            val response = adServerApi.getActiveSlideAds()
            if (!response.isSuccessful) {
                Log.e(TAG, "API error: ${response.code()} - ${response.message()}")
                return null
            }
            val ads = response.body()?.ads ?: run {
                Log.d(TAG, "Empty ads response from server")
                return null
            }
            val nonNullCities = cities?.filterNotNull()?.toSet() ?: emptySet()
            // Find ad matching targeting criteria
            ads.findLast { ad ->
                val categoryMatch = ad.targeting.categories.any { it.value == categoryId }
                if (!categoryMatch) {
                    false
                } else if (nonNullCities.isEmpty()) {
                    true
                } else {
                    ad.targeting.cities.any { city ->
                        val cityInt = city.value.toIntOrNull()
                        if (cityInt == null) {
                            Log.w(TAG, "Invalid city value format: ${city.value}")
                            false
                        } else {
                            cityInt in nonNullCities
                        }
                    }
                }
            }?.toSlideAds()
        }.onFailure {
            if (it !is CancellationException) {
                Log.e(TAG, "Error fetching slide ads", it)
            }
        }.getOrNull()
    }

    /**
     * Fetches notification ads matching the given category and cities.
     * @return List of NotifAd or null if not found or error.
     */
    override suspend fun getNotifAds(categoryId: String, cities: List<Int?>?): List<NotifAd>? {
        return runCatching {
            val response = adServerApi.getNotifAds()
            if (!response.isSuccessful) {
                Log.e(TAG, "API error: ${response.code()} - ${response.message()}")
                return null
            }
            val ads = response.body()?.ads ?: run {
                Log.d(TAG, "Empty notification ads response from server")
                return null
            }
            val nonNullCities = cities?.filterNotNull()?.toSet() ?: emptySet()
            ads.filter { ad ->
                val isPlatformAndroid = ad.platforms.any { it == "android" }
                val categoryMatch = ad.targeting.categories.any { it.value == categoryId }
                if (!isPlatformAndroid || !categoryMatch) {
                    return@filter false
                }
                if (nonNullCities.isEmpty()) {
                    true
                } else {
                    ad.targeting.cities.any { city ->
                        val cityInt = city.value.toIntOrNull()
                        if (cityInt == null) {
                            Log.e(TAG, "Invalid city value format in ad: ${ad.id}")
                            false
                        } else {
                            cityInt in nonNullCities
                        }
                    }
                }
            }.toNotifAds()
        }.onFailure {
            if (it !is CancellationException) {
                Log.e(TAG, "Error fetching notification ads", it)
            }
        }.getOrNull()
    }

    /**
     * Records a click or impression for the given campaign and record type.
     */
    override suspend fun recordAdsClickOrImpression(campaignId: String, recordType: RecordType) {
        try {
            val response = adServerApi.recordAds(campaignId, recordType.value)
            if (!response.isSuccessful){
                Log.d(TAG, "Failed to record: ${response.errorBody()?.string() ?: response.message()}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception in recordAdsClickOrImpression", e)
        }
    }

    /**
     * Stores a lead request and returns the API response.
     * @throws Exception if storing the lead fails.
     */
    override fun storeLeads(leadRequest: LeadRequest): LeadsApiResponse {
        return leadsRequestApi.storeLeads(request = leadRequest).execute()
            .let { response ->
                if (response.isSuccessful) {
                    response.body()!!
                } else {
                    Log.d(TAG, response.errorBody()?.string() ?: response.message())
                    throw Exception("Failed to store leads")
                }
            }
    }
}