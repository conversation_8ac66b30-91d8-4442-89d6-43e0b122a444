package se.scmv.morocco.adstickybanner.presentation

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import se.scmv.morocco.adstickybanner.models.NotifAd
import kotlin.math.roundToInt


//NotifAd
@Composable
fun BubbleOverlayAd(
    modifier: Modifier = Modifier,
    notifAd: NotifAd,
    onDismiss: () -> Unit = {},
    onNotifAdClick: (String) -> Unit = {},
    onFormSubmit: (String, String, String, String) -> Unit = { _, _, _, _ -> },
    onActionClick: (String) -> Unit = { },
) {
    var isExpanded by remember { mutableStateOf(false) }


    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp
    val density = LocalDensity.current

    var offsetX by remember { mutableFloatStateOf(
        with(density) {(screenWidth - 90.dp).toPx()}
    ) }
    var offsetY by remember { mutableFloatStateOf(
        with(density) {(screenHeight * 0.3f).toPx()}
    ) }

    var isDragging by remember { mutableStateOf(false) }

    val dismissZoneHeight = screenHeight * 0.2f
    val dismissZoneY = screenHeight - dismissZoneHeight

    val current = LocalDensity.current

    val bubbleSize by animateFloatAsState(
        targetValue = if (isExpanded) 0f else 70f,
        label = "bubbleSize",
        animationSpec = tween(300)
    )

    var isBubbleVisible by remember { mutableStateOf(true) }

    var isAnimatingDismissal by remember { mutableStateOf(false) }
    var dismissProgress by remember { mutableFloatStateOf(1f) }

    LaunchedEffect(isAnimatingDismissal) {
        if (isAnimatingDismissal) {
            animate(
                initialValue = 1f,
                targetValue = 0f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) { value, _ ->
                dismissProgress = value
                if (value <= 0f) {

                    isBubbleVisible = false
                    onDismiss()
                }
            }
        }
    }


    if (!isBubbleVisible) {
        return
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .zIndex(10f)
    ) {
        if (!isExpanded) {
            Box(
                modifier = Modifier
                    .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                    .clip(CircleShape)
                    .padding(10.dp)
                    .size(bubbleSize.dp)
                    .scale(dismissProgress)
                    .alpha(dismissProgress)
                    .clickable(enabled = !isDragging) {
                        isExpanded = true
                        onNotifAdClick(notifAd.id)
                    }
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { isDragging = true },
                            onDragEnd = {
                                isDragging = false

                                val bubbleY = offsetY + bubbleSize
                                val dismissThreshold = with(current) {
                                    dismissZoneY.toPx()
                                }

                                if (bubbleY > dismissThreshold) {
                                    isAnimatingDismissal = true
                                }
                            },
                            onDragCancel = { isDragging = false },
                            onDrag = { change, dragAmount ->
                                change.consume()
                                offsetX += dragAmount.x
                                offsetY += dragAmount.y
                            }
                        )
                    },
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(color = notifAd.backgroundColor.hexToColor())
                        .border(5.dp, notifAd.backgroundColor.hexToColor(), CircleShape)
                        .padding(5.dp)
                ) {
                    AsyncImage(
                        model = notifAd.thumbnailCreative,
                        contentDescription = "",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.White),
                    )
                }

                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(22.dp)
                        .background(Color.Red, CircleShape)
                        .border(1.5.dp, Color.White, CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "1",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                }
            }


            if (isDragging) {
                val pulseAnimation by rememberInfiniteTransition(label = "pulse").animateFloat(
                    initialValue = 0.9f,
                    targetValue = 1.1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "pulse"
                )

                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 20.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(50.dp)
                            .scale(pulseAnimation)
                            .clip(CircleShape)
                            .background(Color.Red.copy(alpha = 0.6f))
                            .border(1.dp, Color.White, CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Dismiss",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }

        AnimatedVisibility(
            visible = isExpanded,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.4f))
                    .clickable(enabled = false) { },
                contentAlignment = Alignment.Center
            ) {
                    ExpandedAdContent(
                        notifAd = notifAd,
                        cta = notifAd.cta ?: "",
                        isFormType = notifAd.type != "crea",
                        onFormSubmit = { name, phone, email, city ->
                            isExpanded = false
                            onFormSubmit(name, phone, email, city)
                        },
                        onDismiss = {
                            isExpanded = false
                            onDismiss()
                        },
                        onActionClick = { url ->
                            isExpanded = false
                            onActionClick(url)
                        },
                        rowBackgroundColor = notifAd.backgroundColor
                    )
            }
        }
    }
}

@Composable
fun ExpandedAdContent(
    notifAd: NotifAd,
    isFormType: Boolean = false,
    cta: String,
    onDismiss: () -> Unit,
    onFormSubmit: (String, String, String, String) -> Unit = { _, _, _, _ -> },
    onActionClick: (String) -> Unit = { },
    rowBackgroundColor: String
) {
    val infiniteTransition = rememberInfiniteTransition(label = "")

    val rotation by infiniteTransition.animateFloat(
        initialValue = -5f,
        targetValue = 5f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 300, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ), label = ""
    )
    Card(
        modifier = Modifier
            .fillMaxSize(),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
            Column(
                modifier = Modifier
                    .background(notifAd.backgroundColor.hexToColor())
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(rowBackgroundColor.hexToColor())
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = notifAd.thumbnailCreative ,
                        contentDescription = "",
                        modifier = Modifier
                            .size(40.dp)
                            .background(notifAd.backgroundColor.hexToColor())
                            .clip(RoundedCornerShape(8.dp))
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = notifAd.clientName,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        Text(
                            text = notifAd.title,
                            color = Color.LightGray,
                            fontSize = 12.sp
                        )
                    }
                    IconButton(onClick = {
                        onDismiss()
                    }) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                if (isFormType){
                    val viewModel: LeadFormViewModel = hiltViewModel()
                    val cities by viewModel.cities.collectAsState()
                    var selectedCity by remember { mutableStateOf<se.scmv.morocco.domain.models.City?>(null) }
                    if (cities.isEmpty()) {
                        CircularProgressIndicator()
                    } else {
                        CreaForm(
                            heading = notifAd.heading,
                            subheading = notifAd.subHeading,
                            creativeUrl = notifAd.bigCreative,
                            cities = cities,
                            selectedCity = selectedCity,
                            onCitySelected = { selectedCity = it },
                            cityError = null,
                            onSubmit = { name, phone, email, city ->
                                onFormSubmit(name, phone, email, city.id)
                            }
                        )
                    }
                }else{
                    AsyncImage(
                        model = notifAd.bigCreative,
                        contentDescription = "Ad Banner",
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(500.dp),
                        contentScale = ContentScale.Fit
                    )
                }


                TextButton(
                    modifier = Modifier
                        .clip(RoundedCornerShape(10.dp))
                        .padding(10.dp)
                        .graphicsLayer(rotationZ = rotation),
                    onClick = {
                        onActionClick(notifAd.redirectLink)
                    }) {
                    Text(
                        text = cta,
                        color = Color.White,
                        fontSize = 20.sp,
                        modifier = Modifier
                            .background(Color.Black.copy(alpha = 100f))
                            .padding(10.dp)
                    )
                }
            }

    }
}

fun String.hexToColor(): Color {
    try {
        val cleanHex = this.removePrefix("#")
        val colorLong = cleanHex.toLong(16) or 0xFF000000

        return Color(colorLong)
    }catch (e: Exception){
        return Color.Gray
    }
}

fun String.openUrl(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(this))
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context.startActivity(intent)
}