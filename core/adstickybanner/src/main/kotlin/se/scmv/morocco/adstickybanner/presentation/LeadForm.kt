package se.scmv.morocco.adstickybanner.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.Checkbox
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import coil.compose.AsyncImage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class LeadFormViewModel @Inject constructor(
    private val configRepository: ConfigRepository
) : ViewModel() {
    private val _cities = MutableStateFlow<List<City>>(emptyList())
    val cities: StateFlow<List<City>> = _cities.asStateFlow()

    init {
        viewModelScope.launch {
            _cities.value = configRepository.getCities()
        }
    }
}

@Composable
fun CreaForm(
    heading: String,
    subheading: String,
    creativeUrl: String,
    cities: List<City>,
    selectedCity: City?,
    onCitySelected: (City) -> Unit,
    onSubmit: (String, String, String, City) -> Unit,
    cityError: String? = null,
    onTermsClicked: () -> Unit = {}
) {
    var name by remember { mutableStateOf("") }
    var phone by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var acceptTerms by remember { mutableStateOf(false) }
    var acceptTermsError by remember { mutableStateOf<String?>(null) }
    var receiveUpdates by remember { mutableStateOf(true) }
    var city by remember { mutableStateOf(selectedCity) }
    var cityErrorState by remember { mutableStateOf(cityError) }
    var emailError by remember { mutableStateOf<String?>(null) }

    val scrollState = rememberScrollState()
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current

    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .imePadding()
    ) {
        val isTablet = maxWidth > 600.dp
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(horizontal = if (isTablet) 64.dp else 16.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Banner
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(if (isTablet) 300.dp else 180.dp),
                shape = RoundedCornerShape(16.dp)
            ) {
                AsyncImage(
                    model = creativeUrl,
                    contentDescription = heading,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )
            }
            Spacer(Modifier.height(24.dp))
            Text(
                text = heading,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = subheading,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(Modifier.height(16.dp))
            if (isTablet) {
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(16.dp)) {
                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { Text("Nom & Prénom *") },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedLabelColor = MaterialTheme.colorScheme.primary
                        )
                    )
                    OutlinedTextField(
                        value = phone,
                        onValueChange = { phone = it },
                        label = { Text("Téléphone *") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedLabelColor = MaterialTheme.colorScheme.primary
                        )
                    )
                }
                Spacer(Modifier.height(8.dp))
                OutlinedTextField(
                    value = email,
                    onValueChange = {
                        email = it
                        emailError = null
                    },
                    label = { Text("Email *") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    isError = emailError != null,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedLabelColor = MaterialTheme.colorScheme.primary
                    ),
                    supportingText = emailError?.let { err -> { Text(err, color = MaterialTheme.colorScheme.error) } }
                )
            } else {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Nom & Prénom *") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedLabelColor = MaterialTheme.colorScheme.primary
                    )
                )
                Spacer(Modifier.height(8.dp))
                OutlinedTextField(
                    value = phone,
                    onValueChange = { phone = it },
                    label = { Text("Téléphone *") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedLabelColor = MaterialTheme.colorScheme.primary
                    )
                )
                Spacer(Modifier.height(8.dp))
                OutlinedTextField(
                    value = email,
                    onValueChange = {
                        email = it
                        emailError = null
                    },
                    label = { Text("Email *") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    isError = emailError != null,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedLabelColor = MaterialTheme.colorScheme.primary
                    ),
                    supportingText = emailError?.let { err -> { Text(err, color = MaterialTheme.colorScheme.error) } }
                )
            }
            Spacer(Modifier.height(8.dp))

            var cityInput by remember { mutableStateOf("") }
            var cityMenuExpanded by remember { mutableStateOf(false) }
            val filteredCities = if (cityInput.isBlank()) cities else cities.filter { it.name.contains(cityInput, ignoreCase = true) }.take(4)

            Box {
                OutlinedTextField(
                    value = cityInput,
                    onValueChange = {
                        cityInput = it
                        cityMenuExpanded = true
                        // Never hide the keyboard here
                    },
                    label = { Text("Ville") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = cityErrorState != null,
                    readOnly = false,
                    keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = { /* Do nothing, keep keyboard open */ }
                    ),
                    trailingIcon = {
                        IconButton(onClick = { cityMenuExpanded = true }) {
                            Icon(Icons.Filled.ArrowDropDown, contentDescription = "Show suggestions")
                        }
                    }
                )
                DropdownMenu(
                    expanded = cityMenuExpanded && filteredCities.isNotEmpty(),
                    onDismissRequest = { cityMenuExpanded = false },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    filteredCities.forEach { cityItem ->
                        DropdownMenuItem(
                            text = { Text(cityItem.name) },
                            onClick = {
                                cityInput = cityItem.name
                                city = cityItem
                                cityErrorState = null
                                cityMenuExpanded = false
                                onCitySelected(cityItem)
                                focusManager.clearFocus()
                                keyboardController?.hide()
                            }
                        )
                    }
                }
            }

            Spacer(Modifier.height(8.dp))

            // Align checkboxes with other fields
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 4.dp),
                horizontalAlignment = Alignment.Start
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Checkbox(
                        checked = acceptTerms,
                        onCheckedChange = {
                            acceptTerms = it
                            if (it) acceptTermsError = null
                        }
                    )
                    Text("J'accepte ", style = MaterialTheme.typography.bodyMedium)
                    val context = LocalContext.current
                    Text(
                        text = "les conditions générales d'utilisation",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = MaterialTheme.colorScheme.primary,
                            textDecoration = TextDecoration.Underline
                        ),
                        modifier = Modifier.clickable {
                            "https://aide.avito.ma/cgu/".openUrl(context)
                        }
                    )
                }
                if (acceptTermsError != null) {
                    Text(
                        text = acceptTermsError!!,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                    )
                }
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Checkbox(
                        checked = receiveUpdates,
                        onCheckedChange = { receiveUpdates = it }
                    )
                    Text("Je veux être informé(e) des nouveautés")
                }
            }
            Spacer(Modifier.weight(1f))
           
            Button(
                onClick = {
                    if (!acceptTerms) {
                        acceptTermsError = "Veuillez accepter les conditions générales d'utilisation."
                    } else if (city == null) {
                        cityErrorState = "Veuillez sélectionner une ville."
                    } else if (email.isBlank()) {
                        emailError = "Veuillez saisir un email."
                    } else if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                        emailError = "Email invalide."
                    } else {
                        onSubmit(name, phone, email, city!!)
                    }
                },
                enabled = acceptTerms && city != null && email.isNotBlank(),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text("J'EN PROFITE", style = MaterialTheme.typography.labelLarge, fontWeight = FontWeight.Bold)
            }
        }
    }
}

// Theme options for preview parameters
enum class ThemeType {
    LIGHT, DARK
}

// Preview parameter provider for different themes
class ThemeParameterProvider : PreviewParameterProvider<ThemeType> {
    override val values = sequenceOf(
        ThemeType.LIGHT,
        ThemeType.DARK
    )
}

@Preview(
    name = "Peugeot 3008 Contact Form",
    showBackground = true,
    showSystemUi = true
)
@Composable
fun Peugeot3008ContactFormPreview() {
    CreaForm(
        heading = "Peugeot 3008",
        subheading = "Un design athlétique qui couple le souffle",
        creativeUrl = "",
        cities = emptyList(),
        selectedCity = null,
        onCitySelected = {},
        onSubmit = { name, phone, email, city ->
            // Handle form submission
        }
    )
}

@Preview(
    name = "Peugeot 3008 Contact Form - Light/Dark",
    showBackground = true,
    widthDp = 400,
    heightDp = 800,
    group = "Themes"
)
@Composable
fun Peugeot3008ContactFormThemePreview(
    @PreviewParameter(ThemeParameterProvider::class) themeType: ThemeType
) {
    AvitoTheme {
        CreaForm(
            heading = "Peugeot 3008",
            subheading = "Un design athlétique qui couple le souffle",
            creativeUrl = "",
            cities = emptyList(),
            selectedCity = null,
            onCitySelected = {},
            onSubmit = { name, phone, email, city ->
                // Handle form submission
            }
        )
    }
}
