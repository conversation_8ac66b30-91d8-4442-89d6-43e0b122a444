package se.scmv.morocco.adstickybanner.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import se.scmv.morocco.adstickybanner.models.NotifAd

@Keep
data class NotifAdResponse(
    @SerializedName("ads") val ads: List<Ad>
)

@Keep
data class Ad(
    @SerializedName("_id") val id: String,
    @SerializedName("isInactive") val isInactive: <PERSON><PERSON><PERSON>,
    @SerializedName("type") val type: String,
    @SerializedName("name") val name: String,
    @SerializedName("client") val client: String,
    @SerializedName("platforms") val platforms: List<String>,
    @SerializedName("active") val active: Boolean,
    @SerializedName("campaignData") val campaignData: CampaignData,
    @SerializedName("activityPeriod") val activityPeriod: ActivityPeriod,
    @SerializedName("targeting") val targeting: Targeting,
    @SerializedName("stats") val stats: Stats,
    @SerializedName("dateCreated") val dateCreated: String
)
@Keep
data class CampaignData(
    @SerializedName("type") val type: String,
    @SerializedName("message") val message: String,
    @SerializedName("title") val title: String,
    @SerializedName("heading") val heading: String?,
    @SerializedName("subHeading") val subHeading: String?,
    @SerializedName("thumbnailCreative") val thumbnailCreative: String,
    @SerializedName("backgroundColor") val backgroundColor: String,
    @SerializedName("bigCreative") val bigCreative: String,
    @SerializedName("leadsforceCampaign") val leadsforceCampaign: String? = "",
    @SerializedName("formCreative") val formCreative: String,
    @SerializedName("redirectLink") val redirectLink: String? = null,
    @SerializedName("formCta") val formCta: String,
    @SerializedName("creaCta") val cta: String
)
@Keep
data class ActivityPeriod(
    @SerializedName("from") val from: String,
    @SerializedName("to") val to: String
)
@Keep
data class Targeting(
    @SerializedName("cities") val cities: List<City>,
    @SerializedName("categories") val categories: List<Category>
)
@Keep
data class City(
    @SerializedName("label") val label: String,
    @SerializedName("value") val value: String
)
@Keep
data class Category(
    @SerializedName("label") val label: String,
    @SerializedName("value") val value: String
)
@Keep
data class Stats(
    @SerializedName("impression_bubble_msite") val impressionBubbleMsite: Map<String, Int>?,
    @SerializedName("click_bubble_msite") val clickBubbleMsite: Map<String, Int>?,
    @SerializedName("impression_crea_msite") val impressionCreaMsite: Map<String, Int>?,
    @SerializedName("click_crea_msite") val clickCreaMsite: Map<String, Int>?
)

fun List<Ad>.toNotifAds() = this.map { ad ->
    NotifAd(
        id = ad.id,
        message = ad.campaignData.message,
        clientName = ad.client,
        title = ad.campaignData.title,
        type = ad.campaignData.type, //(crea, form)
        heading = ad.campaignData.heading ?: "",
        subHeading = ad.campaignData.subHeading ?: "",
        thumbnailCreative = ad.campaignData.thumbnailCreative,
        backgroundColor = ad.campaignData.backgroundColor,
        bigCreative = if(ad.campaignData.type == "crea") {
            ad.campaignData.bigCreative
        } else {
            ad.campaignData.formCreative
        },
        redirectLink = ad.campaignData.redirectLink ?: "",
        cta = if (ad.campaignData.type == "crea") {
            ad.campaignData.cta
        } else {
            ad.campaignData.formCta
        },
        leadsforceCampaign = ad.campaignData.leadsforceCampaign?: ""
    )
}

