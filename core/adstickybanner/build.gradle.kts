plugins {
    id("avito.android.library")
    id("avito.android.hilt")
    alias(libs.plugins.kotlin.kapt)
    id("avito.android.feature")
}

android {
    namespace = "se.scmv.morocco.adstickybanner"

    buildFeatures {
        buildConfig = true
    }

    buildTypes {
        debug {
            buildConfigField("String", "LEADS_FORCE_API_TOKEN", "\"d6b4ac2a5e7dd8b17bf1035753795d6d79926ea8\"")
            buildConfigField("String", "RESELLER", "\"2bd38a3852c6948146b5a31f3673841d\"")
        }
        release {
            buildConfigField("String", "LEADS_FORCE_API_TOKEN", "\"d6b4ac2a5e7dd8b17bf1035753795d6d79926ea8\"")
            buildConfigField("String", "RESELLER", "\"2bd38a3852c6948146b5a31f3673841d\"")
        }
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.com.google.material)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    //Retrofit
    implementation(libs.com.squareup.retrofit2.retrofit)
    implementation(libs.com.squareup.retrofit2.retrofit.mock)
    implementation(libs.com.squareup.retrofit2.converter.gson)
    implementation(platform(libs.com.squareup.okhttp3.okhttpBom))
    implementation(libs.com.squareup.okhttp3.okhttp)
    implementation(libs.com.squareup.okhttp3.logging.interceptor)
    implementation(libs.com.squareup.okhttp3.mockwebserver)

    //Compose
    implementation(libs.androidx.compose.ui)
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.compose.ui.tooling)
    implementation(libs.androidx.compose.runtime)

    kapt (libs.androidx.hilt.compiler)

    implementation(project(":core:domain"))
    implementation(project(":core:common"))
}