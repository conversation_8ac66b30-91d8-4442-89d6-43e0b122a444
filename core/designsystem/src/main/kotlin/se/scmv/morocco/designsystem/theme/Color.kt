package se.scmv.morocco.designsystem.theme

import androidx.compose.ui.graphics.Color

val Blue900 = Color(0xFF212B36)
val Blue800 = Color(0xFF085CBF)
val Blue600 = Color(0xFF4285F4)
val Blue500 = Color(0xFF2D89F6)
val Blue300 = Color(0xFFD0F2FF)
val Blue50 = Color(0xFFEAF0FF)

val Black = Color(0xFF000000)
val Black900 = Color(0xFF272728)
val Black700 = Color(0xFF575758)
val Black400 = Color(0xFF9F9F9F)

val White = Color(0xFFFFFFFF)
val White600 = Color(0xFFF9F9F9)
val White500 = Color(0XFFF8F8F8)

val Gray600 = Color(0xFF637381)
val Gray500 = Color(0xFF868687)
val Gray400 = Color(0xFF9F9F9F)
val Gray300 = Color(0xFFB7B7B8)
val Gray200 = Color(0xFFCFCFCF)
val Gray150 = Color(0xFFDBDBDB)
val Gray100 = Color(0xFFFFFFFF)
val Gray50 = Color(0xFFFCFCFD)

val Red700 = Color(0xFFD2181F)
val Red500 = Color(0xFFEC5A5F)
val Red200 = Color(0xFFF9C8C9)

val Green700 = Color(0xFF379743)
val Green500 = Color(0XFF2EC966)
val Green400 = Color(0xFF82D18C)
val Green300 = Color(0xFFA0DCA7)
val Green100 = Color(0xFFDBF2DE)

val Brown100 = Color(0xFFFCEBDB)
val Brown300 = Color(0xFFF5C291)
val Brown400 = Color(0xFFF1AE6B)
val Brown500 = Color(0xFFFFA500)
val Brown800 = Color(0xFF9C540E)

// TODO Either remove these colors and use the above palette or rename them to follow the above naming.
val GreenLight = Color(0xFFE1FEEC)
val StatusValid = Color(0xFF2ec966)
val ImageCardColor = Color(red = 234, green = 240, blue = 255)
val AvitoRead = Color(0Xffff4c59)
val AvitoGray = Color(0xFF9EA9BB)
