package se.scmv.morocco.designsystem.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Black
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.White600
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun AvPrimaryButton(
    modifier: Modifier = Modifier,
    text: String,
    leadingIcon: @Composable () -> Unit = {},
    trailingIcon: @Composable () -> Unit = {},
    colors: ButtonColors = ButtonDefaults.buttonColors(
        disabledContainerColor = Gray500,
    ),
    enabled: Boolean = true,
    loading: Boolean = false,
    onClick: () -> Unit,
) {
    Button(
        modifier = modifier,
        shape = MaterialTheme.shapes.small,
        colors = colors,
        onClick = onClick,
        enabled = enabled && loading.not(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            if (loading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    trackColor = MaterialTheme.colorScheme.background,
                )
            }
            leadingIcon()
            Text(
                style = MaterialTheme.typography.titleSmall,
                text = text,
            )
            trailingIcon()
        }
    }
}

@Preview
@Composable
private fun AvPrimaryButtonPreview() {
    AvitoTheme {
        Column {
            AvPrimaryButton(
                text = "Enabled Button",
                onClick = { }
            )
            AvPrimaryButton(
                text = "Disabled Button",
                enabled = false,
                onClick = { }
            )
            AvPrimaryButton(
                text = "Loading Button",
                loading = true,
                onClick = { }
            )
        }
    }
}

@Composable
fun AvSecondaryButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    colors: ButtonColors = ButtonDefaults.buttonColors(
        containerColor = White600,
        contentColor = Black
    ),
    onClick: () -> Unit,
) {
    Button(
        modifier = modifier,
        shape = MaterialTheme.shapes.small,
        enabled = enabled,
        colors = colors,
        onClick = onClick
    ) {
        Text(
            style = MaterialTheme.typography.titleSmall,
            text = text,
        )
    }
}

@Preview
@Composable
private fun AvSecondaryButtonPreview() {
    AvitoTheme {
        Column {
            AvSecondaryButton(
                text = "Button text",
                onClick = { }
            )
            AvButtonWithTextAndIcon(
                icon = R.drawable.decrease_green,
                buttonTitle = stringResource(R.string.common_call),
            ) {}
        }
    }
}

@Composable
fun AvIconButton(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int,
    colors: IconButtonColors = IconButtonDefaults.filledIconButtonColors(),
    onClick: () -> Unit
) {
    FilledIconButton(
        modifier = modifier,
        colors = colors,
        onClick = onClick
    ) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = null
        )
    }
}

@Composable
fun AvIconButton(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    colors: IconButtonColors = IconButtonDefaults.filledIconButtonColors(),
    onClick: () -> Unit
) {
    FilledIconButton(
        modifier = modifier,
        colors = colors,
        onClick = onClick
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null
        )
    }
}

@Composable
fun AvButtonWithTextAndIcon(
    modifier: Modifier = Modifier,
    shape: Shape = ButtonDefaults.shape,
    @DrawableRes icon: Int,
    buttonTitle: String,
    onClick: () -> Unit
) {
    Button(
        shape = shape,
        modifier = modifier,
        onClick = {
            onClick()
        },
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                modifier = Modifier
                    .size(30.dp)
                    .padding(end = MaterialTheme.dimens.medium),
                painter = painterResource(id = icon),
                contentDescription = null,
                tint = Color.White
            )
            Text(
                text = buttonTitle,
                fontSize = 16.sp,
            )
        }
    }
}

@Preview
@Composable
private fun AvIconButtonPreview() {
    AvitoTheme {
        Row {
            AvIconButton(
                icon = R.drawable.ic_time,
                onClick = {}
            )
            AvIconButton(
                icon = Icons.AutoMirrored.Filled.ArrowBack,
                onClick = {}
            )
        }
    }
}