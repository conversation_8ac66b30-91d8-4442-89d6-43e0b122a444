package se.scmv.morocco.designsystem.components

import android.content.res.Configuration
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Checkbox
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun RedirectionItem(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    @DrawableRes icon: Int,
    iconTint: Color? = null,
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = MaterialTheme.dimens.large, horizontal = MaterialTheme.dimens.medium),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .background(Gray100, CircleShape)
                    .padding(MaterialTheme.dimens.small)
                    .size(32.dp),
                contentAlignment = Alignment.Center
            ){
                Icon(
                    painter = painterResource(id = icon),
                    contentDescription = null,
                    tint = iconTint ?: Color.Unspecified
                )
            }
            Text(
                text = stringResource(id = title).lowercase()
                    .replaceFirstChar { it.uppercaseChar() },
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
            )
        }
        Icon(
            painter = painterResource(id = R.drawable.ic_arrow_right),
            contentDescription = null,
        )
    }
}

@Preview(showSystemUi = true)
@Composable
private fun RedirectionItemPreview() {
    AvitoTheme {
        Surface(
            modifier = Modifier.padding(horizontal = 12.dp)
        ) {
            RedirectionItem(
                modifier = Modifier
                    .clip(MaterialTheme.shapes.small)
                    .background(MaterialTheme.colorScheme.background),
                title = R.string.account_master_screen_redirection_password_update,
                icon = R.drawable.ic_change_password,
                iconTint = null,
                onClick = {}
            )
        }
    }
}

@Composable
fun AvTopAppBar(
    modifier: Modifier = Modifier,
    navigationIcon: ImageVector = Icons.AutoMirrored.Filled.ArrowBack,
    onNavigationIconClicked: () -> Unit,
    @StringRes titleRes: Int? = null,
    vararg titleArgs: Any, // A vararg to accept dynamic arguments for the string
    actions: @Composable (RowScope.() -> Unit) = {}
) {
    AvTopAppBar(
        modifier = modifier,
        navigationIcon = navigationIcon,
        onNavigationIconClicked,
        title = titleRes?.let { stringResource(id = it, *titleArgs) },
        actions = actions
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvTopAppBar(
    modifier: Modifier = Modifier,
    navigationIcon: ImageVector = Icons.AutoMirrored.Filled.ArrowBack,
    onNavigationIconClicked: () -> Unit,
    title: String? = null,
    actions: @Composable (RowScope.() -> Unit) = {}
) {
    TopAppBar(
        modifier = modifier,
        title = {
            title?.let {
                Text(text = title, style = MaterialTheme.typography.labelLarge)
            }
        },
        navigationIcon = {
            FilledIconButton(
                onClick = onNavigationIconClicked,
                colors = IconButtonDefaults.filledIconButtonColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                )
            ) {
                Icon(
                    imageVector = navigationIcon,
                    contentDescription = null
                )
            }
        },
        actions = actions,
    )
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL)
@Preview
@Composable
private fun AuthScreenHeaderPreview() {
    AvitoTheme {
        Scaffold(topBar = {
            AvTopAppBar(
                modifier = Modifier.shadow(1.dp),
                onNavigationIconClicked = {},
                titleRes = R.string.otp_validation_screen_title
            )
        }) { Modifier.padding(it) }
    }
}

const val TEST_TAG_TOS_AND_PP = "test_tag_tos_and_pp"

@Composable
fun AvTermOfServicesAndPrivacyPolicy(
    modifier: Modifier = Modifier,
    checked: Boolean = true,
    @StringRes error: Int? = null,
    testTag: String = TEST_TAG_TOS_AND_PP,
    onCheckedChange: (Boolean) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit
) {
    val context = LocalContext.current
    Column(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.Top,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            CompositionLocalProvider(
                LocalMinimumInteractiveComponentSize provides Dp.Unspecified
            ) {
                Checkbox(
                    modifier = Modifier.testTag(testTag),
                    checked = checked,
                    onCheckedChange = onCheckedChange,
                )
            }

            val text1 = stringResource(R.string.common_tos_and_pp_text1)
            val tos = stringResource(R.string.common_tos_and_pp_tos)
            val text2 = stringResource(R.string.common_tos_and_pp_text2)
            val pp = stringResource(R.string.common_tos_and_pp_pp)
            val annotatedString = buildAnnotatedString {
                append(text1)
                append(" ")
                withLink(
                    LinkAnnotation.Clickable(
                        tag = tos,
                        styles = TextLinkStyles(
                            style = SpanStyle(textDecoration = TextDecoration.Underline)
                        ),
                        linkInteractionListener = {
                            navigateToWebViewScreen(
                                context.getString(R.string.common_term_of_services),
                                 context.getString(R.string.url_term_of_services)
                            )
                        }
                    )
                ) {
                    append(tos)
                }
                append(" ")
                append(text2)
                append(" ")
                withLink(
                    LinkAnnotation.Clickable(
                        tag = pp,
                        styles = TextLinkStyles(
                            style = SpanStyle(textDecoration = TextDecoration.Underline)
                        ),
                        linkInteractionListener = {
                            navigateToWebViewScreen(
                                context.getString(R.string.common_privacy_policy),
                                 context.getString(R.string.url_privacy_policy)
                            )
                        }
                    )) {
                    append(pp)
                }
            }
            Text(
                text = annotatedString,
                textAlign = TextAlign.Start,
                style = MaterialTheme.typography.labelLarge
            )
        }
        error?.let {
            Text(text = stringResource(it), color = MaterialTheme.colorScheme.error)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AvTermOfServicesAndPrivacyPolicyPreview() {
    AvitoTheme {
        Column {
            var checked by remember { mutableStateOf(true) }
            AvTermOfServicesAndPrivacyPolicy(
                checked = checked,
                onCheckedChange = { checked = checked.not() },
                navigateToWebViewScreen = {_, _ ->}
            )
            Spacer(Modifier.height(20.dp))
            AvTermOfServicesAndPrivacyPolicy(
                checked = false,
                error = R.string.common_tos_and_pp_field_required,
                onCheckedChange = { checked = checked.not() },
                navigateToWebViewScreen = {_, _ ->}
            )
        }
    }
}