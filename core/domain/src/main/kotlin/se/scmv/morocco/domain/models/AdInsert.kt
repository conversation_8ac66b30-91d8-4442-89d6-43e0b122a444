package se.scmv.morocco.domain.models

import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

data class AdInsertStep(
    val title: String,
    val subtitle: String,
    val tipsTitle: String,
    val tipsContent: List<String>,
    val infoUrl: String,
    val components: List<OrionBaseComponent>
)

enum class AdInsertMediaType(val mimeType: String) {
    IMAGE("image/*"),
    VIDEO("video/*")
}

data class AdForEdit(
    val values: List<OrionBaseComponentValue>,
    val cityId: String? = null,
    val areaId: String? = null,
    val brandId: String? = null,
)