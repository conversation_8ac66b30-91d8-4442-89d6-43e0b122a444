package se.scmv.morocco.domain.models.orion

// COMPONENTS
data class OrionImageUploader(
    override val baseData: OrionBaseComponentData,
    val maximum: Int
) : OrionBaseComponent

data class OrionVideoUploader(
    override val baseData: OrionBaseComponentData,
    val maximum: Int
) : OrionBaseComponent

// VALUES
data class OrionMediaUploaderValue(
    override val id: String,
    val medias: List<OrionKeyStringItem>
) : OrionBaseComponentValue {

    override fun stringValue(): String = medias.toString()
}