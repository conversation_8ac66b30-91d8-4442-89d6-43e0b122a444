package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.domain.models.AccountOrderStatus.CANCELLED
import se.scmv.morocco.domain.models.AccountOrderStatus.DELIVERED
import se.scmv.morocco.domain.models.AccountOrderStatus.DELIVERING
import se.scmv.morocco.domain.models.AccountOrderStatus.INITIATED
import se.scmv.morocco.domain.models.AccountOrderStatus.PREPARING

data class AccountAd(
    // Common properties from MyAdInterface
    val adId: String,
    // TODO Use Domain Category as type
    val category: MyAdCategory,
    val listId: String?,
    val mediaCount: Int,
    val priceWithCurrency: String?,
    val discount: Int?,
    val publishedAt: LocalDateTime?,
    val refusalReason: String?,
    val myAdType: MyAdType,
    val location: City,
    val lastStateTime: LocalDateTime?,
    val myAdStatus: MyAccountAdStatus?,
    val myEditAdStatus: MyEditAdStatus?,
    val title: String,
    val performance: MyAdPerformance?,
    val editLimitCheck: AdEditLimitCheckResponse?,
    val labels: List<MyAdLabel> = emptyList(),

    // Special fields for MyAdSimple
    val imageUrl: String?,
    val lastAppliedVasPack: MyAdVasPack?,
    val vasPackages: VasPackages?
)

data class MyAccountAdsFilterInput(
    val myAccountAdStatus: MyAccountAdStatus,  // Required field
    val boosted: Boolean? = null // Optional field
)


data class CountStatus(
    val status: MyAccountAdStatus, // Required field of type AdStatus
    val count: Int // Required field representing the count of ads with this status
)

enum class MyAccountAdStatus {
    ACTIVE,
    BOOSTED_ACTIVE_ADS,
    REFUSED,
    DEACTIVATED,
    PENDING_PAYMENT,
    DELETED,
    PENDING_REVIEW;

    fun getAnalyticsName() = when (this) {
        ACTIVE -> "active"
        BOOSTED_ACTIVE_ADS -> "boosted"
        REFUSED -> "refused"
        DEACTIVATED -> "deactivated"
        PENDING_PAYMENT -> "pending_payment"
        DELETED -> "deleted"
        PENDING_REVIEW -> "moderation"
    }
}

enum class MyEditAdStatus {
    USER_EDIT_PENDING_REVIEW,
    USER_EDIT_ACCEPTED,
    USER_EDIT_REFUSED
}

data class MyAdCategory(
    val id: String?,
    val name: String?,
    val trackingValue: String?,
    val parent: MyAdCategory?,
)

enum class MyAdTypeKey {
    SELL,
    BUY,
    SWAP,
    LET,
    RENT,
    CO_RENT,
    VAC_RENT,
}

data class MyAdType(
    val name: String,
    val key: MyAdTypeKey?,
)

data class AdImage(
    val url: String // Assuming images have URLs
)

data class MyAdPerformance(
    val views: Int,
    val conversations: Int,
    val phoneViews: Int
)

data class AdEditLimitCheckResponse(
    val isEditable: Boolean,
    val remainingEdits: Int
)

enum class MyAdLabel {
    HOTDEAL,
    URGENT
}

data class MyAdVasPack(
    val name: String,
    val startDate: String?,
    val endDate: String?,
    val status: String?,
    val category: String?
)

data class VasPackages(
    val count: Int?,
    val packages: List<MyAdVasPack>? = emptyList()
)

enum class AccountAdDeactivationReason {
    SOLD_ON_SITE,
    SOLD_OTHER_MEANS,
    EDIT_OR_CHANGE,
    RENEW_OR_BUMP,
    GIVE_UP,
    OTHER
}

enum class AccountAdDeactivationSoldOnSiteDuration {
    ONE_DAY,
    ONE_WEEK,
    ONE_MONTH,
    OVER_ONE_MONTH,
    DONT_REMEMBER
}






