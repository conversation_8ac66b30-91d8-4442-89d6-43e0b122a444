package se.scmv.morocco.domain.models

import kotlinx.datetime.Clock
import kotlinx.datetime.LocalTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import java.math.RoundingMode

data class AdToBoost(
    val title: String,
    val description: String,
    val imageUrl: String?,
    val categoryName: String,
    val city: City,
    val params: List<AdParam>,
)

data class VasPacks(
    val packs: List<VasPack>,
    val executionSlots: VasPackageExecutionSlots?
)

data class VasPack(
    val key: String,
    val title: String,
    val description: String,
    val image: String,
    val vasLabels: List<String>,
    val packages: List<VasPackage>,
    val showBestSellerBadge: Boolean,
    val requiresImage: Boolean
)

data class VasPackage(
    val id: String,
    val durationDays: Int,
    val price: Int,
    val priceUnit: PriceUnit,
)

data class VasPackageExecutionSlots(
    val days: List<String>,
    val times: List<VasPackageExecutionSlotsTime>
)

data class VasPackageExecutionSlotsTime(
    val id: String,
    val startAt: String,
    val endAt: String,
) {

    companion object {
        fun findCurrentOrFirstTimeSlot(
            times: List<VasPackageExecutionSlotsTime>
        ): VasPackageExecutionSlotsTime? {
            val currentTime =
                Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).time
            return times.firstOrNull {
                it.startAtLocalized() <= currentTime && currentTime <= it.edAtLocalized()
            } ?: times.firstOrNull()
        }
    }

    fun displayedTime() = String.format(
        "%s ~ %s",
        convertTimeUTCToLocalTime(startAt),
        convertTimeUTCToLocalTime(endAt)
    )


    fun startAtLocalized() = convertTimeUTCToLocalTime(startAt)

    fun edAtLocalized() = convertTimeUTCToLocalTime(endAt)

    /**
     * Converts a time string from UTC to the local timezone.
     *
     * @param time The time string in UTC format (e.g., "HH:mm").
     * @return The time LocalTime converted to the local timezone.
     */
    private fun convertTimeUTCToLocalTime(time: String): LocalTime {
        return try {
            val utcTime = LocalTime.parse(time)
            val utcDateTime = kotlinx.datetime.LocalDateTime(
                Clock.System.now().toLocalDateTime(TimeZone.UTC).date,
                utcTime
            )
            val instant = utcDateTime.toInstant(TimeZone.UTC)
            val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
            localDateTime.time
        } catch (e: Exception) {
            e.printStackTrace()
            LocalTime.parse(time)
        }
    }
}

enum class VasPacksApplication {
    AD_LISTING,
    AD_INSERT,
}

data class BankApp(
    val frLabel: String,
    val arLabel: String,
    val iconUrl: String
)

data class PaymentInfo(
    val codeOrLink: String,
    val expirationDate: String,
)

enum class PriceUnit {
    DHS,
    POINTS,
    AVITO_TOKEN,
}

enum class PaymentMethodType(val trackingName: String) {
    CREDIT_CARD("CC"),
    TASSHILATE("tasshilate"),
    CASH_PLUS("cash plus"),
    BANK_APP("banking app"),
    AVITOKENS("AviToken")
}

fun Double.roundOffDecimal(numberOfDecimalsAfterComma: Int = 2) = toBigDecimal().setScale(
    numberOfDecimalsAfterComma,
    if (this > 0) RoundingMode.UP else RoundingMode.DOWN
).toDouble()
