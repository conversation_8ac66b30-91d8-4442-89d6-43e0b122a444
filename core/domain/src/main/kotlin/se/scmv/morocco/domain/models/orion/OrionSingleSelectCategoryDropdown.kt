package se.scmv.morocco.domain.models.orion

import se.scmv.morocco.domain.models.AdTypeKey

// COMPONENTS
data class OrionSingleSelectCategoryDropdown(
    override val baseData: OrionBaseComponentData,
    val allowParentSelection: Boolean,
    val enabled: Boolean = true,
) : OrionBaseComponent

// VALUES
data class OrionSingleSelectCategoryDropdownValue(
    override val id: String,
    val categoryId: String,
    val adTypeKey: AdTypeKey?
) : OrionBaseComponentValue {
    override fun stringValue(): String = toString()
}