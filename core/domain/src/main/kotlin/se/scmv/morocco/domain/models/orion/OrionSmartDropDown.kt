package se.scmv.morocco.domain.models.orion

data class OrionSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
    val vasAction: VasAction? = null
): OrionBaseComponent {

    enum class VasAction {
        REFRESH_VAS_PACKAGES_BY_CITY_AREA, REFRESH_VAS_PACKAGES_BY_BRAND
    }
}

data class OrionMultipleSelectSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
): OrionBaseComponent

data class OrionSmartDropDownItem(
    val id: String,
    val name: String,
    val trackingName: String,
    val children: List<OrionSmartDropDownItem>
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as OrionSmartDropDownItem

        if (id != other.id) return false
        if (name != other.name) return false
        if (trackingName != other.trackingName) return false
        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + trackingName.hashCode()
        return result
    }
}

data class OrionSmartDropDownChildData(
    val id: String,
    val title: String,
    val required: Boolean
)
