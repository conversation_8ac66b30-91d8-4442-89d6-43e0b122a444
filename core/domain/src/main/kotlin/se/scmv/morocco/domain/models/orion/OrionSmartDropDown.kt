package se.scmv.morocco.domain.models.orion

data class OrionSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
    val vasAction: VasAction? = null
): OrionBaseComponent {

    enum class VasAction {
        REFRESH_VAS_PACKAGES_BY_CITY_AREA, REFRESH_VAS_PACKAGES_BY_BRAND
    }
}

data class OrionMultipleSelectSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
): OrionBaseComponent

data class OrionSmartDropDownItem(
    val id: String,
    val name: String,
    val trackingName: String,
    val children: List<OrionSmartDropDownItem>
)

data class OrionSmartDropDownChildData(
    val id: String,
    val title: String,
    val required: <PERSON>olean
)
