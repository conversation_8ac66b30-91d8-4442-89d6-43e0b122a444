package se.scmv.morocco.domain.models

import io.realm.RealmList

sealed interface ListingAd {
    data class ExtendedSearch(val types: List<ExtendedSearchType>): ListingAd

    data object ExtendedDelivery: ListingAd

    data class Published(
        val id: String,
        val listId: String,
        val logo: String?,
        val sellerName: String?,
        val date: String,
        val isHighlighted: Boolean,
        val defaultImage: String?,
        val imageCount: Int,
        val videoCount: Int,
        val videoUrl : String? = null,
        val isStore: Boolean,
        val isEcommerce: Boolean,
        val offersShipping: Boolean,
        val isFavorite: Boolean,
        val title: String,
        val description: String,
        val location: City,
        val category: Category,
        val price: AdPrice,
        val discount: Int?,
        val isHotDeal: Boolean,
        val isUrgent: Boolean,
        val isVerifiedSeller : Boolean,
        val params: AdParams,
        val adType : String = ""
        ) : ListingAd

    data class Premium(
        val id: String,
        val listId: String,
        val logo: String?,
        val sellerName: String?,
        val date: String,
        val defaultImage: String?,
        val imageCount: Int,
        val videoCount: Int,
        val videoUrl : String?,
        val isStore: Boolean,
        val isEcommerce: Boolean,
        val offersShipping: Boolean,
        val title: String,
        val category: Category,
        val description: String,
        val location: City,
        val adType: String? = "",
        val price: AdPrice,
        val discount: Int?,
        val isVerifiedSeller : Boolean,
        val params: AdParams,
        val isHotDeal: Boolean?,
        val isUrgent: Boolean?,
        ) : ListingAd

    data class NewConstruction(
        val title: String,
        val location: City,
        val price: AdPrice?,
        val defaultImage: String?,
        val imagesCount: Int,
        val externalLink: String,
    ) : ListingAd

    data class DfpBanner(
        val uuid: String,
        val subject: String? = null,
        val paramsValues: Map<String, String> = emptyMap(),
    ) : ListingAd
}

enum class ExtendedSearchType {
    TO_WHOLE_CITY, TO_NEARBY_CITIES, TO_WHOLE_COUNTRY, TO_BIGGER_PRICE_RANGE
}

fun ListingAd.Published.toAdRecord() = AdRecord().apply {
    adId = id.toIntOrNull()
    listId = <EMAIL>()
    isFavorite = true
    name = sellerName
    subject = title
    accountType = if (isStore) 1 else 0
    price = <EMAIL>()
    body = description
    phoneHidden = 0
    images = RealmList<AdPictureRecord>().apply {
        for (i in 0 until imageCount) {
            add(
                AdPictureRecord().apply {
                    path = defaultImage
                    width = 947
                    height = 1876
                }
            )
        }
    }
    date = <EMAIL>
    saved = 1
    city = location.id.toIntOrNull()
    region = location.area?.id?.toIntOrNull()
    category = <EMAIL>()
    params = RealmList<AdParamRecord>().apply {
        addAll(
            <EMAIL> { param ->
                AdParamRecord().apply {
                    key = param.id
                    value = param.value
                    name = param.label
                }
            }
        )
    }
    recordTime = System.currentTimeMillis()
}

fun createMockListingAds(count: Int = 10): List<ListingAd.Published> {
    return List(count) {
        ListingAd.Published(
            id = "$it",
            listId = "$it",
            title = "Modern Apartment for Sale",
            description = "Spacious 3-bedroom apartment with a stunning city view and modern amenities.",
            date = "2024-10-24T18:49:09Z",
            price = AdPrice.Available(
                current = 350000,
                currentWithCurrency = "350000 dh",
                old = 300000,
                oldWithCurrency = "300000 dh",
                changeType = PriceChangeType.INCREASE
            ),
            discount = 50,
            logo = "https://example.com/image1.jpg",
            defaultImage = "https://example.com/image1.jpg",
            imageCount = 5,
            videoCount = 1,
            location = City(
                id = "1",
                name = "New York",
                trackingName = "new_york",
                area = Area(
                    id = "1",
                    name = "Manhattan",
                    trackingName = "manhattan"
                )
            ),
            category = Category(
                id = "100",
                name = "Real Estate",
                icon = "",
                trackingName = "real_estate",
            ),
            isVerifiedSeller = true,
            sellerName = "John Doe",
            isUrgent = true,
            isFavorite = false,
            isHighlighted = false,
            isStore = false,
            isEcommerce = false,
            offersShipping = false,
            isHotDeal = true,
            params = createMockListingAdParams(),
            adType = ""
        )
    }
}

enum class PriceChangeType {
    DECREASE, INCREASE
}

sealed interface AdPrice {
    data object Unavailable : AdPrice
    data class Available(
        val current: Int = 0,
        val currentWithCurrency: String,
        val old: Int?,
        val oldWithCurrency: String?,
        val changeType: PriceChangeType?
    ): AdPrice

    fun getCurrentPrice(): Int? {
        return when(this) {
            is Available -> current
            Unavailable -> null
        }
    }

    fun getCurrentPriceWithCurrency(): String? {
        return when (this) {
            is Available -> currentWithCurrency
            Unavailable -> null
        }
    }
    fun getOldPrice(): Int? {
        return when (this) {
            is Available -> old
            Unavailable -> null
        }
    }

    fun getOldPriceWithCurrency(): String? {
        return when (this) {
            is Available -> oldWithCurrency
            Unavailable -> null
        }
    }
}

data class AdParams(
    val primary: List<AdParam>,
    val secondary: List<AdParam>,
    val extra: List<AdParam>,
) {
    fun getAll(): List<AdParam> = listOf(primary, secondary, extra).flatten()
}

data class AdParam(
    val id: String,
    val label: String,
    val value: String,
    val iconUrl: String,
    val analyticsName: String?,
)

fun createMockListingAdParams(): AdParams {
    val primaryParams = listOf(
        AdParam(
            id = "1",
            label = "Bedrooms",
            value = "3 Bedrooms",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "bedroom_count"
        ),
        AdParam(
            id = "2",
            label = "Bathrooms",
            value = "3 Bedrooms",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "bathroom_count"
        )
    )

    // Sample secondary listing parameters
    val secondaryParams = listOf(
        AdParam(
            id = "3",
            label = "sq",
            value = "1500 sq",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "area_size"
        ),
        AdParam(
            id = "4",
            label = "Balcony",
            value = "",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "balcony_presence"
        )
    )

    // Sample extra listing parameters
    val extraParams = listOf(
        AdParam(
            id = "5",
            label = "Garage",
            value = "",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "garage_availability"
        ),
        AdParam(
            id = "6",
            label = "Near Central Park",
            value = "500m",
            iconUrl = "https://assets.avito.ma/icons/svg/adparam_mileage.svg",
            analyticsName = "park_proximity"
        )
    )

    return AdParams(
        primary = primaryParams,
        secondary = secondaryParams,
        extra = extraParams
    )
}