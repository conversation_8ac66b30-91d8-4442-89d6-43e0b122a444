package se.scmv.morocco.domain.models

import io.realm.RealmList
import io.realm.RealmObject
import io.realm.annotations.PrimaryKey
import io.realm.annotations.RealmModule


@RealmModule(library = true, classes = [AdRecord::class, AdPictureRecord::class, AdParamRecord::class])
class DomainRealmModule

/**
 * Created by amine on 27/02/15.
 */
open class AdRecord : RealmObject {
        @PrimaryKey
        var adId: Int? = null
        var listId: Int? = null
        var region: Int? = null
        var city: Int? = null
        var category: Int? = null
        var name: String? = null
        var accountType: Int? = null
        var phoneHidden: Int? = null
        var subject: String? = null
        var price: Int? = null
        var body: String? = null
        var lang: String? = null
        var type: String? = null
        var images = RealmList<AdPictureRecord>()
        var date: String? = null
        var views: Int? = null
        var params = RealmList<AdParamRecord>()
        var saved: Int? = null
        var accountId: Int? = null
        var isFavorite = false
        var isDeleted = false
        var recordTime: Long = 0
        var isOpened = false

        /*
        private long recordTime;

        private String date;
        private String title;
        private int categoryId;
        private String type;
        private String thumb;
        private String price;
        private boolean isFavorite;
        private AdDetailRecord adDetail;
        private boolean isOpened;*/
        constructor() : super()
        constructor(id: Int) : super() {
                adId = id
        }

        companion object {
                var AD_THUMB_BASE_URL = ""
                var AD_IMAGE_BASE_URL = ""
                const val vpath = "vi"
        }
}

/**
 * Created by amine on 27/02/15.
 */
open class AdParamRecord : RealmObject() {
        var name: String? = null
        var value: String? = null
        var key: String? = null
        var valueLabel: String? = null
}

/**
 * Created by amine on 27/02/15.
 */
open class AdPictureRecord : RealmObject() {
        var name: String? = null
        var width: Int? = null
        var height: Int? = null
        var path: String? = null
        var default: Boolean? = null
        var order: Int? = null
        val thumbnailPath: String
                get() = AdRecord.AD_THUMB_BASE_URL + path
        val fullImagePath: String
                get() = AdRecord.AD_IMAGE_BASE_URL + path
}