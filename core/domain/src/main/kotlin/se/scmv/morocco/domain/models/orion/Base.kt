package se.scmv.morocco.domain.models.orion

import androidx.compose.ui.util.fastJoinToString

// COMPONENTS
sealed interface OrionBaseComponent {
    val baseData: OrionBaseComponentData
}

data class OrionBaseComponentData(
    val id: String,
    val title: String,
    val required: Boolean,
    val iconUrl: String,
    val validations: List<OrionComponentValidation>,
    val dependencies: List<OrionComponentDependency> = emptyList()
)

data class OrionComponentValidation(
    val errorMessage: String,
    val regex: String
)

data class OrionComponentDependency(
    val dependsOn: String,
    val condition: Type
) {
    enum class Type { NOT_EMPTY, EQUALS }
}

data class OrionKeyStringItem(
    val id: String,
    val name: String
)

// VALUES
interface OrionBaseComponentValue {
    val id: String

    fun stringValue(): String
}

data class OrionKeyStringValue(
    override val id: String,
    val value: String
) : OrionBaseComponentValue {
    override fun stringValue(): String = value
}

data class OrionKeyBooleanValue(
    override val id: String,
    val value: Boolean
) : OrionBaseComponentValue {
    override fun stringValue(): String = value.toString()
}

data class OrionKeyNumberValue(
    override val id: String,
    val value: Int
) : OrionBaseComponentValue {
    override fun stringValue(): String = value.toString()
}

data class OrionKeyRangeValue(
    override val id: String,
    val min: Int,
    val max: Int,
) : OrionBaseComponentValue {
    override fun stringValue(): String = "$min-$max"
}

data class OrionKeyStringListValue(
    override val id: String,
    val items: List<String>
) : OrionBaseComponentValue {
    override fun stringValue(): String = items.fastJoinToString()
}

data class OrionSelectedKeysValue(
    override val id: String,
    val keys: List<String>
) : OrionBaseComponentValue {
    override fun stringValue(): String = keys.fastJoinToString()
}
