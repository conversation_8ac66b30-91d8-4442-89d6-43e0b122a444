package se.scmv.morocco.domain.repositories

import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.domain.models.AdForEdit
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.AdLimitations
import se.scmv.morocco.domain.models.AdToBoost
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.PaymentInfo
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPacks
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import java.io.File

interface AdRepository {
    suspend fun upsert(
        adID: String?,
        category: Category?,
        adType: AdTypeKey?,
        isLimit: Boolean,
        values: List<OrionBaseComponentValue>,
        vasPack: VasPack?,
        vasPackage: VasPackage?
    ): Resource<String, NetworkAndBackendErrors>

    suspend fun getAdForEdit(
        adID: String
    ): Resource<AdForEdit, NetworkAndBackendErrors>

    suspend fun uploadMedia(
        file: File,
        adInsertMediaType: AdInsertMediaType
    ): Resource<String, NetworkAndBackendErrors>

    suspend fun getAdLimitations(
        categoryId: String,
        adTypeKey: AdTypeKey,
    ): Resource<AdLimitations, NetworkAndBackendErrors>

    suspend fun getAdForBoost(
        adID: String,
    ): Resource<AdToBoost, NetworkAndBackendErrors>

    suspend fun getVasPacks(
        application: VasPacksApplication,
        adID: String? = null,
        adCategory: String? = null,
        adType: AdTypeKey? = null,
        cityId: String? = null,
        areaId: String? = null,
        brandId: String? = null,
    ): Resource<VasPacks, NetworkAndBackendErrors>

    suspend fun getVasPackage(packId: String, adId: String): Resource<VasPacks, NetworkAndBackendErrors>

    suspend fun proceedPayment(
        adId: String,
        packId: String,
        application: VasPacksApplication,
        paymentMethodType: PaymentMethodType,
        executionSlotsDay: String?,
        executionSlotsTime: String?
    ): Resource<PaymentInfo, NetworkAndBackendErrors>

    fun getAds(filters: List<OrionBaseComponentValue>): Flow<PagingData<ListingAd>>

    suspend fun getAdsCount(
        filters: List<OrionBaseComponentValue>
    ): Resource<Int, NetworkAndBackendErrors>
}