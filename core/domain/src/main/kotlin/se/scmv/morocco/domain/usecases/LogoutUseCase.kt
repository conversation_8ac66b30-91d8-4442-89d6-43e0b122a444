package se.scmv.morocco.domain.usecases

import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.content.edit
import com.google.firebase.messaging.FirebaseMessagingService
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import javax.inject.Inject

class LogoutUseCase @Inject constructor(
    @ApplicationContext private val context: Context,
    private val authenticationRepository: AuthenticationRepository,
    private val analyticsHelper: AnalyticsHelper
) {

    companion object {
        private const val NOTIFICATION_ID = 101
        private const val APP_PREFS = "avito_prefs"
        private const val ACCOUNT_PREF = "account"
        private const val TOKEN_KEY = "account_token"
        private const val FIREBASE_MESSAGING_TOKEN = "firebase_messaging_token"
        private const val APP_FLAVOR = "appFlavor"
    }

    suspend operator fun invoke(): Resource<Boolean, NetworkAndBackendErrors> {
        val fmToken = getFirebaseMessagingToken()
        return authenticationRepository.signOut(fmToken).also { result->
            if (result is Resource.Success) {
                clearMessagingNotification()
                clearPreferences()
                analyticsHelper.clearUserData()
            }
        }
    }

    private fun clearMessagingNotification() {
        (context.getSystemService(
            FirebaseMessagingService.NOTIFICATION_SERVICE
        ) as? NotificationManager)?.let { manager ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // For API 23 and above
                manager.activeNotifications?.forEach { notification ->
                    if (notification.id == NOTIFICATION_ID) {
                        manager.cancel(notification.tag, notification.id)
                    }
                }
            } else {
                // For API levels below 23, cancel the notification directly
                manager.cancel(NOTIFICATION_ID)
            }
        }
    }

    private fun getFirebaseMessagingToken(): String? {
        return context.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE)
            .getString(FIREBASE_MESSAGING_TOKEN, null)
    }

    private fun clearPreferences() {
        context.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE).edit(commit = true) {
            remove(ACCOUNT_PREF)
            remove(TOKEN_KEY)
            remove(FIREBASE_MESSAGING_TOKEN)
            remove(APP_FLAVOR)
        }
    }
}