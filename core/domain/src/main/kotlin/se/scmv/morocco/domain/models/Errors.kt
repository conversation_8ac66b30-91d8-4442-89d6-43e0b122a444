package se.scmv.morocco.domain.models

interface Error

enum class NetworkErrors {
    NO_INTERNET,
    UNKNOWN
}

sealed interface NetworkAndBackendErrors : <PERSON>rror {
    data class Network(val error: NetworkErrors) : NetworkAndBackendErrors
    data class Backend(val message: String) : NetworkAndBackendErrors
}

sealed interface UpdatePasswordErrors : Error {
    data class NetworkOrBackend(val error: NetworkAndBackendErrors) : UpdatePasswordErrors
    data object NeedToReLogin: UpdatePasswordErrors
}

sealed class NetworkExceptions(override val message: String) : Exception() {
    data class Backend(override val message: String) : NetworkExceptions(message)
    class NoInternet : NetworkExceptions("")
    class Unknown : NetworkExceptions("")
}