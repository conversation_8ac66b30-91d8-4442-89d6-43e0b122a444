package se.scmv.morocco.domain.models.orion

data class OrionTextField(
    override val baseData: OrionBaseComponentData,
    val enabled: Boolean,
    val suffix: String?,
    val isLarge: Boolean,
    val inputType: InputType,
    val potentialValue: String? = null,
    val notifyLapTitle: <PERSON>ole<PERSON> = false,
    val notifyLapDescription: Boolean = false,
) : OrionBaseComponent {

    enum class InputType {
        TEXT, NUMBER, PHONE
    }
}