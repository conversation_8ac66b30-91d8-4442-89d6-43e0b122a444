package se.scmv.morocco.common.lang

import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat

object LocaleManager {

    private const val LANG_AR = "ar"
    private const val LANG_FR = "fr"

    @JvmStatic
    fun changeToArabic() {
        updateResources(LANG_AR)
    }

    // In our case the default lang is en but it contains fr strings.
    @JvmStatic
    fun changeToFrench() {
        updateResources(LANG_FR)
    }

    @JvmStatic
    fun switchLanguage() {
        if (isFr()) {
            changeToArabic()
        } else {
            changeToFrench()
        }
    }

    @JvmStatic
    private fun updateResources(tag: String) {
        val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(tag)
        // Call this on the main thread as it may require Activity.restart()
        AppCompatDelegate.setApplicationLocales(appLocale)
    }

    @JvmStatic
    fun getCurrentLanguage(): String {
        return AppCompatDelegate.getApplicationLocales().get(0)?.language ?: LANG_FR
    }

    fun isFr() = getCurrentLanguage() == LANG_FR

    fun isAr() = getCurrentLanguage() == LANG_AR
}