package se.scmv.morocco.ad.listing

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.domain.coroutines.executeInParallel
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.toAdRecord
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class AdsListingViewModel @Inject constructor(
    private val adRepository: AdRepository,
    private val accountRepository: AccountRepository,
    private val configRepository: ConfigRepository
) : ViewModel() {

    private val listingUiEvents = MutableSharedFlow<UiEvent.ListingUiEvents>()
    private var categoriesCash = emptyList<CategoryTree>()
    private var filtersValuesCash = emptyList<OrionBaseComponentValue>()

    private val _viewState = MutableStateFlow(AdsListingViewState())
    val viewState = _viewState.asStateFlow()

    private val _favoriteStates = MutableStateFlow<MutableList<String>>(mutableListOf())
    val favoriteStates = _favoriteStates.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<AdsListingOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    @OptIn(ExperimentalCoroutinesApi::class)
    val ads: Flow<PagingData<ListingAd>> = listingUiEvents
        .onStart { emit(UiEvent.ListingUiEvents.OnRefreshClicked) }
        .filterNot {
            it is UiEvent.ListingUiEvents.OnFilterChanged && it.filtersValues == filtersValuesCash
        }
        .flatMapLatest { event ->
            val query = when (event) {
                is UiEvent.ListingUiEvents.OnFilterChanged -> {
                    event.filtersValues
                        .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                        .firstOrNull()?.let {
                            refreshChildrenCategoriesAndFilters(
                                categoryId = it.categoryId,
                                adTypeKey = it.adTypeKey
                            )
                        }
                    filtersValuesCash = event.filtersValues
                    event.filtersValues
                }

                is UiEvent.ListingUiEvents.OnRefreshClicked -> filtersValuesCash
                UiEvent.ListingUiEvents.OnExtendedSearchKeepFiltersClicked -> filtersValuesCash.filter {
                    // TODO remove the extend search filter
                    true
                }
            }
            adRepository.getAds(query)
        }.cachedIn(viewModelScope)

    init {
        getPopularCategories()
    }

    fun onEvent(event: UiEvent) {
        when (event) {
            is UiEvent.ListingUiEvents -> viewModelScope.launch { listingUiEvents.emit(event) }
            is UiEvent.OnAdFavoriteClicked -> updateAdFavoriteStatus(event.ad)
            is UiEvent.OnCategoryClicked -> onCategoryClicked(event.category)
        }
    }

    private fun getPopularCategories() {
        viewModelScope.launch {
            when (
                val result = executeInParallel(
                    firstCall = { configRepository.getListingCategories() },
                    secondCall = { configRepository.getFiltersCategories() }
                )
            ) {
                is Resource.Success -> {
                    val popularCategories = result.data.first.mapNotNull {
                        CategoryTree.findTree(
                            trees = result.data.second,
                            categoryId = it,
                            adTypeKey = null
                        )
                    }.toPopularCategories().toPersistentList()
                    categoriesCash = result.data.second
                    _viewState.update { state -> state.copy(popularCategories = popularCategories) }
                }

                is Resource.Failure -> Unit
            }
        }
    }

    private fun updateAdFavoriteStatus(ad: ListingAd.Published) {
        // TODO remove the AdRecord use and re-implement the favorite management logic.
        viewModelScope.launch {
            val adRecord = ad.toAdRecord()
            val result = if (ad.isFavorite) {
                _favoriteStates.update { currentMap ->
                    currentMap.minus(ad.id).toMutableList()
                }
                accountRepository.unBookmarkAd(adRecord)
            } else {
                _favoriteStates.update { currentMap ->
                    currentMap.plus(ad.id).toMutableList()
                }
                accountRepository.bookmarkAd(adRecord)
            }
        }
    }

    private fun onCategoryClicked(category: PopularCategory) {
        viewModelScope.launch {
            refreshChildrenCategoriesAndFilters(
                categoryId = category.id,
                adTypeKey = category.adTypeKey?.let { AdTypeKey.safeValueOf(it) }
            )
            _oneTimeEvents.emit(
                AdsListingOneTimeEvents.NotifyCategoryChanged(
                    categoryId = category.id,
                    adTypeKey = category.adTypeKey
                )
            )
        }
    }

    private suspend fun refreshChildrenCategoriesAndFilters(categoryId: String, adTypeKey: AdTypeKey?) {
        if (categoryId == "0" && adTypeKey == AdTypeKey.ALL) {
            _viewState.update { state ->
                state.copy(
                    childrenCategories = persistentListOf(),
                    filters = persistentListOf()
                )
            }
        } else {
            val filters = getCategoryFilters(categoryId, adTypeKey)
            val tree = CategoryTree.findTree(
                trees = categoriesCash,
                categoryId = categoryId,
                adTypeKey = adTypeKey
            )
            if (tree != null) {
                val children = tree.children.toPopularCategories()
                if (children.isNotEmpty()) {
                    _viewState.update { state ->
                        state.copy(
                            childrenCategories = children.toPersistentList(),
                            filters = filters
                        )
                    }
                } else {
                    val parentTree = CategoryTree.findParentTree(
                        trees = categoriesCash,
                        categoryId = categoryId,
                        adTypeKey = adTypeKey
                    )
                    val parentTreeChildren = parentTree?.children?.toPopularCategories()?.map {
                        it.copy(enabled = it.id != categoryId)
                    }
                    if (!parentTreeChildren.isNullOrEmpty()) {
                        _viewState.update { state ->
                            state.copy(
                                childrenCategories = parentTreeChildren.toPersistentList(),
                                filters = filters
                            )
                        }
                    }
                }
            }
        }
    }

    private suspend fun getCategoryFilters(
        categoryId: String,
        adTypeKey: AdTypeKey?
    ): PersistentList<ChipData> {
        return when (
            val result = configRepository.getFilters(
                categoryId = categoryId,
                adTypeKey = adTypeKey ?: AdTypeKey.SELL
            )
        ) {
            is Resource.Success -> {
                result.data.map {
                    with(it.baseData) {
                        ChipData(id = id, name = title, selected = false)
                    }
                }.toPersistentList()
            }

            is Resource.Failure -> persistentListOf()
        }
    }

    private fun getCategoryChildrenByCategoryName(categoryName: String): PersistentList<PopularCategory> {
        return CategoryTree.findChildren(
            trees = categoriesCash,
            categoryName = categoryName
        ).toPopularCategories().toPersistentList()
    }

    private fun List<CategoryTree>.toPopularCategories() = map { it.toPopularCategory() }

    private fun CategoryTree.toPopularCategory() = PopularCategory(
        id = category.id,
        name = category.name,
        icon = category.icon,
        adTypeKey = adTypes.firstOrNull()?.key?.name
    )
}