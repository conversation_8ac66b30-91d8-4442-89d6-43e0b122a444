package se.scmv.morocco.ad.vas.master

import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.datetime.LocalDate
import kotlinx.datetime.format
import kotlinx.datetime.format.char
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.coroutines.executeSequentially
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.PriceUnit
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.ui.asUiText
import javax.inject.Inject

class BuildVasMasterViewStateUseCase @Inject constructor(
    private val adRepository: AdRepository,
    private val accountRepository: AccountRepository
) {

    suspend operator fun invoke(
        from: VasRoute.From,
        adId: String,
        application: VasPacksApplication,
        categoryId: String? = null,
        adTypeKey: AdTypeKey? = null,
        packId: String? = null
    ): VasMasterViewState {
        val account = accountRepository.currentAccount.firstOrNull() as? Account.Connected
            ?: return VasMasterViewState.Error(UiText.FromRes(R.string.common_unexpected_error_verify_and_try_later))

        val vasPacksResult = executeSequentially(
            firstCall = { adRepository.getAdForBoost(adID = adId) },
            secondCall = { adForBoost ->
                when {
                    from == VasRoute.From.NOTIFICATION && !packId.isNullOrEmpty() -> {
                        adRepository.getVasPackage(packId = packId, adId = adId)
                    }

                    else -> adRepository.getVasPacks(
                        application = application,
                        adID = adId,
                        adCategory = categoryId,
                        adType = adTypeKey,
                        cityId = adForBoost.city.id,
                        areaId = adForBoost.city.area?.id
                    )
                }
            }
        )
        return when (vasPacksResult) {
            is Resource.Success -> {
                val shopAccountInfo = (account as? Account.Connected.Shop)?.toShopAccountInfo()
                val paymentMethods = getPaymentMethods(account)

                VasMasterViewState.Success(
                    step1State = ChooseVasPackageViewState(
                        adToBoost = vasPacksResult.data.first,
                        vasPacks = vasPacksResult.data.second,
                        showPrivatePaymentProviders = account.isPrivate()
                    ),
                    step2State = ChoosePaymentMethodViewState(
                        shopAccountInfo = shopAccountInfo,
                        paymentMethods = paymentMethods
                    ),
                    step3State = null,
                    chosenPaymentMethod = if (paymentMethods.size == 1) {
                        paymentMethods.first().type
                    } else null
                )
            }

            is Resource.Failure -> VasMasterViewState.Error(vasPacksResult.error.asUiText())
        }
    }

    private fun getPaymentMethods(account: Account.Connected): ImmutableList<PaymentMethod> {
        return when (account) {
            is Account.Connected.Private -> persistentListOf(
                PaymentMethod(
                    title = R.string.payment_method_credit_card_title,
                    description = R.string.payment_method_credit_card_description,
                    icon = R.drawable.img_payment_method_cb,
                    type = PaymentMethodType.CREDIT_CARD
                ),
                PaymentMethod(
                    title = R.string.payment_method_cash_title,
                    description = R.string.payment_method_cash_description,
                    icon = R.drawable.img_payment_method_tasshilat,
                    type = PaymentMethodType.TASSHILATE
                ),
                PaymentMethod(
                    title = R.string.payment_method_cash_plus_title,
                    description = R.string.payment_method_cash_plus_description,
                    icon = R.drawable.img_payment_method_cash_plus,
                    type = PaymentMethodType.CASH_PLUS
                ),
                PaymentMethod(
                    title = R.string.payment_method_bank_app_title,
                    description = R.string.payment_method_bank_app_description,
                    icon = R.drawable.img_payment_method_banking_app,
                    type = PaymentMethodType.BANK_APP
                )
            )

            is Account.Connected.Shop -> {
                val avitoTokenAllowed = account.store.allowedAccess.avitoTokenAllowed
                persistentListOf(
                    PaymentMethod(
                        title = if (avitoTokenAllowed) {
                            R.string.payment_method_avitokens_title
                        } else R.string.payment_method_points_title,
                        description = if (avitoTokenAllowed) {
                            R.string.payment_method_avitokens_description
                        } else R.string.payment_method_points_description,
                        icon = R.drawable.img_payment_method_avitokens,
                        type = PaymentMethodType.AVITOKENS
                    )
                )
            }
        }
    }

    private fun Account.Connected.Shop.toShopAccountInfo() = ShopAccountInfo(
        imageUrl = store.logoUrl,
        name = contact.name,
        isVerified = store.verified,
        dates = listOfNotNull(
            store.pointsExpirationDate?.let {
                createDisplayedDate(
                    R.string.choose_vas_payment_method_screen_shop_sold_expiration_date,
                    it
                )
            },
            store.startDate?.let {
                createDisplayedDate(
                    R.string.choose_vas_payment_method_screen_shop_store_creation_date,
                    it
                )
            },
            store.expirationDate?.let {
                createDisplayedDate(
                    R.string.choose_vas_payment_method_screen_shop_store_expiration_date,
                    it
                )
            }
        ).toPersistentList(),
        storePoints = store.points,
        storeSoldUnit = if (store.allowedAccess.avitoTokenAllowed) {
            PriceUnit.AVITO_TOKEN
        } else PriceUnit.POINTS,
        storeMembership = store.membership,
    )

    private fun createDisplayedDate(
        prefix: Int,
        date: LocalDate
    ): Pair<Int, String> = prefix to date.format(
        LocalDate.Format {
            dayOfMonth()
            char('-')
            monthNumber()
            char('-')
            year()
        }
    )
}