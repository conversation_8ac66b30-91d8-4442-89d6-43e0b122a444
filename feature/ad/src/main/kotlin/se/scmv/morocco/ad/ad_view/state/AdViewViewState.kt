package se.scmv.morocco.ad.ad_view.state

import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdTouchingPoint


sealed interface AdViewViewState {
    data object Loading : AdViewViewState
    data class Success(
        val details: AdDetails,
    ) : AdViewViewState

    data class Error(val message: UiText) : AdViewViewState
}


sealed interface AdViewOneTimeEvents {
    data class ShowHideProgress(val isLoading: Boolean) : AdViewOneTimeEvents
    data class ShowHideProgressAdReport(val isLoading: Boolean) : AdViewOneTimeEvents

    data class OpenLogin(val requestCode: Int) : AdViewOneTimeEvents
    data class OpenEcommerce(val ad: AdDetails.Details, val addUuid: String) : AdViewOneTimeEvents
    data class ShowFirstConversationBtmSheet(val ad: AdDetails.Details) : AdViewOneTimeEvents
    data class ShowConversationBtmSheet(val conversationId: String) : AdViewOneTimeEvents
    data class UpdateFavoriteStatus(
        val adId: String,
        val isFavorite: Boolean,
        val updated: Boolean
    ) : AdViewOneTimeEvents
    data object CarCheckSuccess : AdViewOneTimeEvents

}

sealed interface AdViewUiEvents {
    data class OnTouchingPointClicked(val touchingPoint: AdTouchingPoint) : AdViewUiEvents
    data object OnFavoriteBtnClicked : AdViewUiEvents
    data object OnMessagingBtnClicked : AdViewUiEvents
    data object OnCallBtnClicked : AdViewUiEvents
    data object OnWhatsappBtnClicked : AdViewUiEvents
    data object ShowPhone : AdViewUiEvents
    data object OnShopBtnClicked : AdViewUiEvents
    data object OnBoostBtnClicked : AdViewUiEvents
    data object OnShareBtnClicked : AdViewUiEvents
    data object OnReportAd : AdViewUiEvents
    data object OnCarCheckSubmit : AdViewUiEvents
    data object OnCarInspectionCtaClick : AdViewUiEvents
    data object OnDownloadInspectionReport : AdViewUiEvents
    data object OnPreviewInspectionReport : AdViewUiEvents
    data object OnCarInspectionReportDisplayed : AdViewUiEvents
    data object OnCarInspectionCtaDisplayed : AdViewUiEvents
    data class UserConnected(val loginType: LoginType, val token: String) : AdViewUiEvents
    data object OnRefreshClicked : AdViewUiEvents
}
