package se.scmv.morocco.ad.ad_view

import android.app.Activity
import android.app.DownloadManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Environment
import android.util.Log
import android.widget.Toast
import com.google.android.gms.tasks.Task
import com.google.firebase.dynamiclinks.DynamicLink.AndroidParameters
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.ShortDynamicLink
import io.realm.RealmList
import se.scmv.morocco.ad.R
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdParamRecord
import se.scmv.morocco.domain.models.AdPictureRecord
import se.scmv.morocco.domain.models.AdRecord

internal fun openWhatsApp(activity: Activity, ad: AdDetails.Details) {
    logInstalledApps(activity)
    if (isWhatsappInstalled(activity)) {
        val phone = ad.seller?.getNullablePhone()?.number?.let { number ->
            "212${number.substring(1)}"
        }

        FirebaseDynamicLinks
            .getInstance()
            .createDynamicLink()
            .setLink(Uri.parse("https://m.avito.ma/ad?id=${ad.listId}&utm_source=avito-android-share"))
            .setDomainUriPrefix("scmv.page.link")
            .setAndroidParameters(
                AndroidParameters.Builder().build()
            )
            .buildShortDynamicLink()
            .addOnCompleteListener(activity) { task: Task<ShortDynamicLink> ->
                val text = activity.getResources()
                    .getText(R.string.take_look_share_text_whatsapp)
                    .toString() + " " + ad.title?.replace(
                    "&",
                    "%26"
                ) + " " + if (task.isSuccessful) task.result.shortLink else getUrl(
                    ad.listId!!
                )
                val i = Intent(
                    Intent.ACTION_VIEW, Uri.parse(
                        "https://wa.me/$phone/?text=$text"
                    )
                )
                activity.startActivity(i)
            }
    } else {
        Toast.makeText(
            activity,
            activity.getString(R.string.customer_services_whatsapp_not_installed_title),
            Toast.LENGTH_SHORT
        ).show()
    }
}

private fun isWhatsappInstalled(context: Context): Boolean {
    return try {
        val info = context.packageManager.getPackageInfo("com.whatsapp", 0)
        Log.d("isWhatsappInstalled", "WhatsApp package found: ${info.packageName}")
        true
    } catch (e: PackageManager.NameNotFoundException) {
        Log.e("isWhatsappInstalled", "WhatsApp package not found", e)
        false
    }
}

private fun getUrl(listId: String): String {
    return "https://m.avito.ma/ad?id=$listId&utm_source=avito-android-share"
}

fun logInstalledApps(context: Context) {
    val packageManager = context.packageManager
    val installedPackages = packageManager.getInstalledPackages(0)

    installedPackages.forEach { packageInfo ->
        Log.d("isWhatsApp", "Installed Package: ${packageInfo.packageName}")
    }
}

fun shareAd(adDetails: AdDetails.Details, context: Context) {
    val sendIntent = Intent()
    sendIntent.action = Intent.ACTION_SEND
    sendIntent.putExtra(
        Intent.EXTRA_SUBJECT,
        context.resources.getText(R.string.share_text)

    )
    sendIntent.type = "text/plain"
    FirebaseDynamicLinks
        .getInstance()
        .createDynamicLink()
        .setLink(Uri.parse("https://m.avito.ma/ad?id=${adDetails.listId}&utm_source=avito-android-share"))
        .setDomainUriPrefix("scmv.page.link")
        .setAndroidParameters(
            AndroidParameters.Builder().build()
        )
        .buildShortDynamicLink()
        .addOnCompleteListener((context as Activity)) { task: Task<ShortDynamicLink> ->
            sendIntent.putExtra(
                Intent.EXTRA_TEXT,
                context.resources.getText(R.string.take_look_share_text)
                    .toString() + " " + adDetails.title + " " + if (task.isSuccessful) task.result.shortLink else getUrl(
                    adDetails.listId!!
                )
            )
            context.startActivity(
                Intent.createChooser(
                    sendIntent,
                    context.resources.getText(R.string.share_title)
                )
            )
        }
//    AnalyticsManager.instance
//        ?.logFirebase(
//            "share_ad",
//            AnalyticsManager.getPublishedAdPropertiesForFirebase(adDetails).toMutableMap()
//        )
}

fun openUrl(website : String, context : Context){
    var url = website
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "https://$url"
    }
    val browserIntent = Intent(
        Intent.ACTION_VIEW,
        Uri.parse(url)
    )
    context.startActivity(
        browserIntent
    )
}

fun downloadPdf(context: Context, pdfUrl: String) {
    try {
        val request = DownloadManager.Request(Uri.parse(pdfUrl))
            .setTitle("Rapport d'Inspection du Véhicule")
            .setDescription("Téléchargement du rapport PDF en cours...")
            .setDestinationInExternalPublicDir(
                Environment.DIRECTORY_DOWNLOADS,
                "rapport_inspection.pdf"
            )
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            .setMimeType("application/pdf")

        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        downloadManager.enqueue(request)

        Toast.makeText(
            context,
            "Le téléchargement a commencé. Vérifiez votre dossier Téléchargements.",
            Toast.LENGTH_LONG
        ).show() // French toast message
    } catch (e: Exception) {
        Toast.makeText(
            context,
            "Erreur lors du démarrage du téléchargement : ${e.localizedMessage}",
            Toast.LENGTH_LONG
        ).show() // French error message
        e.printStackTrace()
    }
}
fun AdDetails.Details.toAdRecord() = AdRecord().apply {
    adId = <EMAIL>?.toIntOrNull()
    listId = <EMAIL>?.toIntOrNull()
    isFavorite = <EMAIL> == true
    name = seller?.getNullableName()
    subject = title
    accountType = if (seller?.isShop() == true) 1 else 0
    price = <EMAIL>()
    body = description
    phoneHidden = 0
    images = RealmList<AdPictureRecord>().apply {
        val size: Int? = media?.media?.images?.size
        if (size != null)
            for (i in 0 until size) {
                add(
                    AdPictureRecord().apply {
                        path = media?.media?.images?.get(i)?.paths?.standard
                        width = 947
                        height = 1876
                    }
                )
            }
    }
    date = <EMAIL>
    saved = 1
    city = cityArea?.id?.toIntOrNull()
    region = cityArea?.area?.id?.toIntOrNull()
    category = <EMAIL>?.id?.toIntOrNull()
    params = RealmList<AdParamRecord>().apply {
        <EMAIL>?.secondary?.let {
            it.map { param ->
                AdParamRecord().apply {
                    key = param.id
                    value = param.value
                    name = param.label
                }
            }.let { it1 ->
                addAll(
                    it1
                )
            }
        }
    }
    recordTime = System.currentTimeMillis()
}

