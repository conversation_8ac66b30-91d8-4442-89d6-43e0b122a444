package se.scmv.morocco.ad.ad_view

import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.android.exoplayer2.ExoPlayer
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.ad_view.components.AdDescription
import se.scmv.morocco.ad.ad_view.components.AdImagesSlider
import se.scmv.morocco.ad.ad_view.components.AdParamsGrid
import se.scmv.morocco.ad.ad_view.components.AdTitlePriceLocationDate
import se.scmv.morocco.ad.ad_view.components.AvitoDisclaimer
import se.scmv.morocco.ad.ad_view.components.CalendarScreen
import se.scmv.morocco.ad.ad_view.components.CarInspectedScreen
import se.scmv.morocco.ad.ad_view.components.CarInspectionLeadContent
import se.scmv.morocco.ad.ad_view.components.CarInspectionScreen
import se.scmv.morocco.ad.ad_view.components.CategoryRow
import se.scmv.morocco.ad.ad_view.components.CircularIconButton
import se.scmv.morocco.ad.ad_view.components.DeliveryItem
import se.scmv.morocco.ad.ad_view.components.DfpBannerAdView
import se.scmv.morocco.ad.ad_view.components.EcommerceRow
import se.scmv.morocco.ad.ad_view.components.EnchereItem
import se.scmv.morocco.ad.ad_view.components.FirstMessageBottomSheet
import se.scmv.morocco.ad.ad_view.components.FullScreenImageSlider
import se.scmv.morocco.ad.ad_view.components.LoanSimulatorUI
import se.scmv.morocco.ad.ad_view.components.OrderBottomSheet
import se.scmv.morocco.ad.ad_view.components.ReportAdContent
import se.scmv.morocco.ad.ad_view.components.SellerCard
import se.scmv.morocco.ad.ad_view.components.SellerNameVerifiedRow
import se.scmv.morocco.ad.ad_view.components.ShareButton
import se.scmv.morocco.ad.ad_view.components.SimilarAdsSection
import se.scmv.morocco.ad.ad_view.components.TopBarButton
import se.scmv.morocco.ad.ad_view.components.TouchingPointAdItem
import se.scmv.morocco.ad.ad_view.state.AdViewOneTimeEvents
import se.scmv.morocco.ad.ad_view.state.AdViewUiEvents
import se.scmv.morocco.ad.ad_view.state.AdViewViewState
import se.scmv.morocco.designsystem.components.ShowWarningPhoneCallDialog
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdDetails.Details.ImagePaths
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.usecases.CalculateLoanUseCase
import kotlin.math.min

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdViewScreen(
    viewModel: AdViewViewModel = hiltViewModel(),
    navigateBack: () -> Unit,
    navigateToSimilarAd: (
        newAdId: String,
        imageUrl: String?,
        title: String?,
        date: LocalDateTime?,
        imageCount: Int,
        videoCount: Int,
        videoUrl: String?,
        isStore: Boolean,
        price: AdPrice?,
        location: String?,
        category: String?,
        categoryIcon: String?,
        isUrgent: Boolean?,
        isHotDeal: Boolean?,
        discount: Int?
    ) -> Unit,
    onOpenPdf: (pdfUrl: String) -> Unit,
    onShopPage: (id: String) -> Unit,
    onBuyEcommerceProduct: (ad: AdDetails.Details, sellerId: String) -> Unit,
    navigateToAuthentication: () -> Unit,
    showMessageAfterAuth: Boolean,
    onBoostClick: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    navigateToWebViewScreen: (String?, String) -> Unit,
) {

    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val cachedAdDetails by viewModel.cachedAdDetails.collectAsStateWithLifecycle()
    val touchingPoints by viewModel.touchingPoints.collectAsStateWithLifecycle()

    val loanConfig: Pair<LoanSimulatorConfig, Int>? by viewModel.loanConfig.collectAsStateWithLifecycle()
    val carCheckConfig by viewModel.carCheckConfig.collectAsStateWithLifecycle()

    val oneTimeEvents = viewModel.oneTimeEvents.collectAsState(initial = null)
    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior()
    val scrollState = rememberScrollState()

    val imageHeightDp = 400.dp
    val imageHeightPx = with(LocalDensity.current) { imageHeightDp.toPx() }

    val titleAlpha: Float by remember {
        derivedStateOf {
            min(1f, scrollState.value / imageHeightPx) // Adjusted divisor
        }
    }

    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    var showCarInspectionDialog by remember { mutableStateOf(false) }
    var showFirstMessageDialog by remember { mutableStateOf(showMessageAfterAuth) }
    var showCallDialog by remember { mutableStateOf(false) }
    var showCarInspectionSent by remember { mutableStateOf(false) }

    var showReportDialog by remember { mutableStateOf(false) }
    var showHideProgressAdReport by remember { mutableStateOf(false) }

    var showOrderBottomSheet by remember { mutableStateOf(false) }

    val backgroundAlpha: Float by remember {
        derivedStateOf {
            min(1f, scrollState.value / imageHeightPx) // Adjusted divisor
        }
    }

    var showFullScreenSlider by remember { mutableStateOf(false) }
    var initialFullScreenPageIndex by remember { mutableIntStateOf(0) }
    var fullScreenImages: List<AdDetails.Details.Image?>? by remember { mutableStateOf(null) }
    var videoUrlFullScreen: String? by remember { mutableStateOf(null) }
    var isTouchingPointsVisibleOnScreen by remember { mutableStateOf(false) }

    val context = LocalContext.current

    // Cache painter resources to avoid repeated loading during recomposition
    val favoriteOnPainter = painterResource(R.drawable.ic_favorite_tab_on_red)
    val favoriteOffPainter = painterResource(R.drawable.circum_heart)
    val shoppingBagPainter = painterResource(R.drawable.shopping_bag)
    val shopIconPainter = painterResource(R.drawable.ic_shop)
    val rocketPainter = painterResource(R.drawable.outline_rocket_launch_16)
    val shoppingCartPainter = painterResource(R.drawable.ic_shoping_cart)
    val whatsappPainter = painterResource(R.drawable.ic_whatsapp)
    val messagePainter = painterResource(R.drawable.ic_message)
    val callPainter = painterResource(R.drawable.ic_call_grid)

    // Initialize the ExoPlayer
    val exoPlayer = remember { ExoPlayer.Builder(context).build() }
    val exoPlayerFullScreen = remember { ExoPlayer.Builder(context).build() }

    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
            exoPlayerFullScreen.release()
        }
    }

    if (showFullScreenSlider && fullScreenImages != null) {
        FullScreenImageSlider(
            images = fullScreenImages!!,
            initialPageIndex = initialFullScreenPageIndex,
            videoUrl = videoUrlFullScreen,
            onDismiss = { currentIndex ->
                initialFullScreenPageIndex = currentIndex
                exoPlayerFullScreen.release()
                showFullScreenSlider = false
            },
            exoPlayerFullScreen
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text =
                        (if (viewState is AdViewViewState.Success)
                            (viewState as AdViewViewState.Success).details.ad.title.orEmpty()
                        else
                            cachedAdDetails?.title.orEmpty()).toString(),
                        maxLines = 1,
                        fontWeight = FontWeight.Bold,
                        overflow = TextOverflow.Ellipsis,
                        style = TextStyle(fontSize = 14.sp),
                        modifier = Modifier
                            .padding(start = 8.dp, end = 8.dp)
                            .alpha(titleAlpha)

                    )
                },
                navigationIcon = {
                    TopBarButton(
                        onClick = navigateBack,
                        icon = {
                            Icon(
                                Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                            )
                        },
                        modifier = Modifier
                            .padding(start = MaterialTheme.dimens.regular)
                            .size(38.dp)
                    )

                },
                actions = {
                    Row {

                        ShareButton(
                            modifier = Modifier,
                            onClick = {
                                if (viewState is AdViewViewState.Success) {
                                    val adDetails = (viewState as AdViewViewState.Success).details
                                    shareAd(adDetails = adDetails.ad, context as Activity)
                                    viewModel.onEvent(AdViewUiEvents.OnShareBtnClicked)
                                }
                            }
                        )


                        if (!viewModel.isConnectedShop.value) {
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))

                            TopBarButton(
                                onClick = {
                                    viewModel.onEvent(AdViewUiEvents.OnFavoriteBtnClicked)
                                },

                                icon = {
                                    val isFavorite = when (viewState) {
                                        is AdViewViewState.Success -> (viewState as AdViewViewState.Success).details.ad.isInMyFavorites
                                        else -> cachedAdDetails?.isInMyFavorites ?: false
                                    }

                                    Icon(
                                        painter = if (isFavorite == true) favoriteOnPainter else favoriteOffPainter,
                                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                                        contentDescription = if (isFavorite == true) "Unfavorite" else "Favorite"
                                    )
                                }
                            )


                        }

                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = backgroundAlpha) // Apply background alpha
                ),
                scrollBehavior = scrollBehavior,
                modifier = Modifier.shadow(
                    elevation = MaterialTheme.dimens.none,
                    ambientColor = Color.Black.copy(alpha = backgroundAlpha),
                    spotColor = Color.Black.copy(alpha = backgroundAlpha)
                )
            )
        },
        bottomBar = {
            Column {
                if (touchingPoints.isNotEmpty() && !isTouchingPointsVisibleOnScreen) {
                    val pagerState = rememberPagerState(
                        initialPage = 0,
                        pageCount = { touchingPoints.size }
                    )
                    val scope = rememberCoroutineScope()

                    LaunchedEffect(key1 = pagerState.currentPage) {
                        launch {
                            delay(5000)

                            scope.launch {
                                pagerState.animateScrollToPage(
                                    page = (pagerState.currentPage + 1).mod(pagerState.pageCount)
                                )
                            }
                        }
                    }

                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier
                            .fillMaxWidth(),
                        contentPadding = PaddingValues(horizontal = MaterialTheme.dimens.none),
                        userScrollEnabled = true
                    ) { page ->
                        TouchingPointAdItem(
                            ad = touchingPoints[page],
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            viewModel.onEvent(AdViewUiEvents.OnTouchingPointClicked(it))
                        }
                    }
                }
                if (viewState is AdViewViewState.Success) {
                    val adDetails = (viewState as AdViewViewState.Success).details
                    BottomAppBar(
                        containerColor = MaterialTheme.colorScheme.surface,
                        modifier = Modifier
                            .height(56.dp)
                            .shadow(
                                elevation = MaterialTheme.dimens.regular,
                                ambientColor = Color.Black,
                                spotColor = Color.Black,
                                shape = RectangleShape
                            )
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = MaterialTheme.dimens.default),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Row(
                                modifier = Modifier.weight(1f),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Store Name & Verified Icon
                                Column(
                                    verticalArrangement = Arrangement.Center
                                ) {
                                    if (adDetails.ad.seller?.isShop() == true) {
                                        if ((adDetails.ad.seller as? AdDetails.Seller.Store)?.isEcommerce == true) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically,
                                                modifier = Modifier
                                                    .height(IntrinsicSize.Min)
                                                    .padding(
                                                        horizontal = MaterialTheme.dimens.none,
                                                        vertical = MaterialTheme.dimens.none
                                                    )
                                            ) {
                                                Icon(
                                                    painter = shoppingBagPainter,
                                                    contentDescription = "Verified Shop",
                                                    tint = Color(0xFFff4c59),
                                                    modifier = Modifier
                                                        .size(MaterialTheme.dimens.default)
                                                        .padding(MaterialTheme.dimens.none)
                                                )

                                                Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                                                if ((adDetails.ad.stock ?: 0) > 0)
                                                    Text(
                                                        text = String.format(
                                                            "%s %s",
                                                            adDetails.ad.stock,
                                                            stringResource(R.string.stock_articles)
                                                        ),
                                                        style = TextStyle(
                                                            fontSize = 10.sp,
                                                            color = Color.White,
                                                            lineHeight = 14.sp
                                                        ),
                                                        fontSize = 10.sp,
                                                        color = Color(0xFF6B7280),
                                                        modifier = Modifier
                                                            .padding(MaterialTheme.dimens.tiny)
                                                            .height(IntrinsicSize.Min)

                                                    )
                                                else
                                                    Text(
                                                        text = stringResource(R.string.stock_epuise),
                                                        style = TextStyle(
                                                            fontSize = 10.sp,
                                                            color = Color.White,
                                                            lineHeight = 14.sp
                                                        ),
                                                        fontSize = 10.sp,
                                                        color = Color(0xFF6B7280),
                                                        modifier = Modifier
                                                            .padding(MaterialTheme.dimens.tiny)
                                                            .height(IntrinsicSize.Min)

                                                    )

                                            }
                                        } else {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically,
                                                modifier = Modifier
                                                    .height(IntrinsicSize.Min)
                                                    .background(
                                                        color = Color(0xFFfc942d),
                                                        shape = RoundedCornerShape(MaterialTheme.dimens.small)
                                                    )
                                                    .padding(
                                                        horizontal = MaterialTheme.dimens.small,
                                                        vertical = MaterialTheme.dimens.none
                                                    ) // Smaller padding
                                            ) {
                                                Icon(
                                                    painter = shopIconPainter,
                                                    contentDescription = "Verified Shop",
                                                    tint = Color.White,
                                                    modifier = Modifier
                                                        .size(MaterialTheme.dimens.default)
                                                        .padding(1.dp) // Smaller icon
                                                )
                                                Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                                                Text(
                                                    text = stringResource(R.string.store_lable),
                                                    style = TextStyle(
                                                        fontSize = 10.sp,
                                                        color = Color.White,
                                                        lineHeight = 14.sp // Adjust the line height to manage vertical padding
                                                    ),
                                                    fontSize = 10.sp, // Smaller text
                                                    color = Color.White,
                                                    modifier = Modifier
                                                        .padding(MaterialTheme.dimens.tiny)
                                                        .height(IntrinsicSize.Min) // Ensures the row is only as tall as needed

                                                )
                                            }
                                        }
                                    }

                                    if (adDetails.ad.seller?.isShop() == true) {
                                        if ((adDetails.ad.seller as? AdDetails.Seller.Store)?.isEcommerce == true) {
                                            EcommerceRow(
                                                adDetails.ad.price.getCurrentPriceWithCurrency()
                                                    .orEmpty()
                                            )
                                        } else {
                                            SellerNameVerifiedRow(
                                                adDetails.ad.seller?.getNullableName().orEmpty(),
                                                adDetails.ad.seller?.isVerified() == true
                                            )
                                        }
                                    } else {
                                        SellerNameVerifiedRow(
                                            adDetails.ad.seller?.getNullableName().orEmpty(),
                                            adDetails.ad.seller?.isVerified() == true
                                        )
                                    }


                                }
                            }
                            if (viewModel.isAccountAd.value) {
                                Button(
                                    onClick = {
                                        onBoostClick(
                                            adDetails.ad.adId.orEmpty(),
                                            adDetails.ad.category?.id.orEmpty(),
                                            adDetails.ad.type?.key?.name.orEmpty(),
                                            VasPacksApplication.AD_LISTING
                                        )
                                        viewModel.onEvent(AdViewUiEvents.OnBoostBtnClicked)
                                    },
                                    modifier = Modifier
                                        .weight(1f) // Equal width
                                        .fillMaxWidth(), // Ensures button fills the weight allocation
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(
                                            0xFFFC942D
                                        )
                                    ), // Background color
                                    shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                                    contentPadding = PaddingValues(
                                        horizontal = MaterialTheme.dimens.default,
                                        vertical = MaterialTheme.dimens.regular
                                    )
                                ) {

                                    Icon(
                                        painter = rocketPainter,
                                        contentDescription = "Time Icon",
                                        modifier = Modifier.size(MaterialTheme.dimens.default),
                                    )


                                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                                    Text(
                                        text = stringResource(R.string.boost_Ad),
                                        color = Color.White,
                                        style = MaterialTheme.typography.bodyMedium,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis
                                    )
                                }
                            } else {
                                // Action Buttons (WhatsApp, Chat, Call)
                                if (adDetails.ad.seller?.isShop() == true && (adDetails.ad.seller as? AdDetails.Seller.Store)?.isEcommerce == true) {
                                    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                                        Button(
                                            onClick = {
                                                viewModel.onEvent(AdViewUiEvents.OnShopBtnClicked)
                                            },
                                            enabled = (adDetails.ad.stock ?: 0) > 0,
                                            modifier = Modifier
                                                .height(38.dp)
                                                .wrapContentWidth(),
                                            shape = RoundedCornerShape(8.dp),
                                            colors = ButtonDefaults.buttonColors()
                                                .copy(containerColor = Color(0xFFff4c59)),
                                            contentPadding = PaddingValues(
                                                horizontal = MaterialTheme.dimens.default,
                                                vertical = 8.dp
                                            )
                                        ) {
                                            Icon(
                                                painter = shoppingCartPainter,
                                                contentDescription = "Call",
                                                tint = Color.White,
                                                modifier = Modifier.size(MaterialTheme.dimens.big)
                                            )
                                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                                            Text(
                                                text = stringResource(R.string.buy_order),
                                                color = Color.White,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.SemiBold
                                            )
                                        }

                                    }
                                } else {

                                    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                                        if (adDetails.ad.seller?.phoneStartWith05() == true && adDetails.ad.seller?.hasPhone() == true)
                                            CircularIconButton(
                                                icon = whatsappPainter,
                                                iconColor = Color(0xFF64c571),
                                                backgroundColor = Color(0xFFedf9f0),
                                                onClick = {
                                                    openWhatsApp(context as Activity, adDetails.ad)
                                                    viewModel.onEvent(AdViewUiEvents.OnWhatsappBtnClicked)
                                                }
                                            )

                                        CircularIconButton(
                                            icon = messagePainter,
                                            iconColor = Color(0xFF2563EB),
                                            backgroundColor = Color(0xFFEAF0FF),
                                            onClick = {
                                                viewModel.onEvent(AdViewUiEvents.OnMessagingBtnClicked)
                                            }
                                        )
                                        if (adDetails.ad.seller?.hasPhone() == true)
                                            CircularIconButton(
                                                icon = callPainter,
                                                iconColor = Color.White,
                                                backgroundColor = Color(0xFF2563EB),
                                                onClick = {
                                                    showCallDialog = true
                                                }
                                            )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }


    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(bottom = 52.dp)
                .verticalScroll(scrollState)
        ) {
            when (viewState) {
                is AdViewViewState.Success -> {
                    val adDetails = (viewState as AdViewViewState.Success).details

                    Column(
                        Modifier
                            .fillMaxSize()
                    ) {
                        AdImagesSlider(
                            adDetails.ad.media?.media?.images ?: emptyList(),
                            adDetails.ad.media?.media?.videos?.firstOrNull()?.defaultPath,
                            adDetails.ad.seller is AdDetails.Seller.Store,
                            adDetails.ad.media?.mediaCount,
                            onImageClick = { index, imagesList, video ->
                                initialFullScreenPageIndex = index
                                fullScreenImages = imagesList
                                videoUrlFullScreen = video
                                showFullScreenSlider = true
                            },
                            initialFullScreenPageIndex,
                            exoPlayer = exoPlayer

                        )
                        if (adDetails.ad.offersShipping == true)
                            DeliveryItem {
                                showOrderBottomSheet = true
                            }

                        if (viewModel.isEnchere(adDetails.ad.category?.id))
                            EnchereItem {}

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                        ) {
                            AdTitlePriceLocationDate(
                                adDetails.ad.title.orEmpty(),
                                adDetails.ad.price,
                                if (adDetails.ad.cityArea?.area?.name.isNullOrEmpty()) adDetails.ad.cityArea?.name.orEmpty() else adDetails.ad.cityArea?.area?.name.orEmpty() + ", " + adDetails.ad.cityArea?.name.orEmpty(),
                                adDetails.ad.listTime,
                                adDetails.ad.isUrgent,
                                adDetails.ad.isHotDeal == true,
                                adDetails.ad.discount
                            )

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            AvitoDisclaimer {
                                showReportDialog = true
                            }

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            CategoryRow(
                                categoryName = adDetails.ad.category?.name.orEmpty(),
                                categoryType = adDetails.ad.type?.name.orEmpty(),
                                categoryId = adDetails.ad.category?.id,
                            )

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            adDetails.ad.params?.let { params ->
                                AdParamsGrid(params.secondary + params.primary)
                            }

                            if (carCheckConfig != null) {
                                val reportLink =
                                    adDetails.ad.params?.extra?.find { it.id == "car_inspection_report_link" }?.value
                                val reportTime =
                                    adDetails.ad.params?.extra?.find { it.id == "car_inspection_report_time" }?.value
                                if (!reportLink.isNullOrEmpty() && !reportTime.isNullOrEmpty()) {
                                    LaunchedEffect(key1 = reportLink + reportTime) {
                                        viewModel.onEvent(AdViewUiEvents.OnCarInspectionReportDisplayed)
                                    }
                                    CarInspectedScreen(
                                        onOpenOrDownloadPdf = { download ->
                                            if (download) {
                                                viewModel.onEvent(AdViewUiEvents.OnDownloadInspectionReport)
                                                // download pdf from url
                                                downloadPdf(context, reportLink)

                                            } else {
                                                viewModel.onEvent(AdViewUiEvents.OnPreviewInspectionReport)
                                                onOpenPdf(reportLink)
                                            }
                                        },
                                        carCheckConfig!!,
                                        reportTime
                                    )
                                } else {
                                    CarInspectionScreen(
                                        onInspectClicked = {
                                            viewModel.onEvent(AdViewUiEvents.OnCarInspectionCtaClick)
                                            showCarInspectionDialog = true
                                        },
                                        carCheckConfig!!
                                    )
                                }
                            }
                            if (showOrderBottomSheet) {
                                ModalBottomSheet(
                                    onDismissRequest = {
                                        showOrderBottomSheet = false
                                    },
                                    sheetState = sheetState,

                                    modifier = Modifier.fillMaxSize(),
                                    shape = RoundedCornerShape(MaterialTheme.dimens.none),
                                    contentWindowInsets = {
                                        WindowInsets.navigationBars.only(
                                            WindowInsetsSides.Horizontal
                                        )
                                    }


                                ) {
                                    OrderBottomSheet(
                                        imageUrl = adDetails.ad.media?.defaultImage?.paths?.standard.orEmpty(),
                                        title = adDetails.ad.title.orEmpty(),
                                        price = adDetails.ad.price.getCurrentPriceWithCurrency()
                                            ?: stringResource(R.string.price_not_specified),
                                        total = (adDetails.ad.price.getCurrentPrice() ?: 0) + 30,
                                        onConfirm = {
                                            navigateToWebViewScreen(
                                                "",
                                                "https://www.avito.ma/delivery?list_id=${adDetails.ad.listId}"
                                            )
                                        }
                                    )
                                }
                            }
                            if (showReportDialog) {
                                ModalBottomSheet(
                                    onDismissRequest = {
                                        scope.launch {
                                            sheetState.hide()
                                            showReportDialog = false
                                        }
                                    },
                                    sheetState = sheetState
                                ) {
                                    ReportAdContent(
                                        onDismissRequest = {
                                            scope.launch {
                                                sheetState.hide()
                                                showReportDialog = false
                                            }
                                        },
                                        onSendReport = { email, reason, message ->
                                            adDetails.ad.listId?.let {
                                                viewModel.reportAd(
                                                    it,
                                                    email,
                                                    reason,
                                                    message
                                                )
                                            }
                                            viewModel.onEvent(AdViewUiEvents.OnReportAd)
                                        },
                                        showHideProgressAdReport
                                    )
                                }
                            }
                            if (showCarInspectionDialog) {
                                ModalBottomSheet(
                                    onDismissRequest = { showCarInspectionDialog = false },
                                    sheetState = sheetState
                                ) {
                                    carCheckConfig?.let {
                                        CarInspectionLeadContent(
                                            onDismissRequest = {
                                                scope.launch {
                                                    sheetState.hide()
                                                    showCarInspectionDialog = false
                                                }
                                            },
                                            onTermsClicked = { url ->
                                                navigateToWebViewScreen(null, url)
                                            },
                                            onSendLead = { name, fullPhoneNumber, _, checkbox2 ->
                                                viewModel.sendLead(
                                                    resellerId = "2bd38a3852c6948146b5a31f3673841d",
                                                    campaignId = "2e2efc6d961655cae68349e6a1c47fea",
                                                    name = name,
                                                    phoneNumber = fullPhoneNumber,
                                                    option1 = "adview url",
                                                    option2 = checkbox2
                                                )
                                            },
                                            carCheckConfig = it,
                                            showCarInspectionSent = showCarInspectionSent
                                        )
                                    }
                                }
                            }

                            if (showFirstMessageDialog) {
                                ModalBottomSheet(
                                    onDismissRequest = { showFirstMessageDialog = false },
                                    sheetState = sheetState
                                ) {
                                    FirstMessageBottomSheet(
                                        sellerName = adDetails.ad.seller?.getNullableName(),
                                        adImageUrl = adDetails.ad.media?.defaultImage?.paths?.standard,
                                        adTitle = adDetails.ad.title.orEmpty(),
                                        adPrice = adDetails.ad.price.getCurrentPriceWithCurrency(),
                                        onMessageSend = { messageText ->
                                            viewModel.sendFirstMessage(
                                                adDetails.ad.adId,
                                                text = messageText
                                            )
                                            showFirstMessageDialog = false
                                        }
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            AdDescription(
                                stringResource(R.string.description_label),
                                adDetails.ad.description.orEmpty()
                            )
                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
                            if (viewModel.isVacationLocation(adDetails.ad.category?.id)) {
                                CalendarScreen(
                                    reservedDates = adDetails.ad.reservedDays,
                                    price = adDetails.ad.price.getCurrentPrice(),
                                    cityName = adDetails.ad.cityArea?.name.orEmpty()
                                )
                            }
                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
                            DfpBannerAdView(
                                adUnitId = stringResource(id = R.string.dfp_adunit_ad_view),
                                adTitle = adDetails.ad.title.orEmpty(),
                                categoryId = adDetails.ad.category?.id.orEmpty(),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                                    .align(Alignment.CenterHorizontally)
                            )

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            Box(
                                modifier = Modifier
                                    .onGloballyPositioned { layoutCoordinates: LayoutCoordinates ->
                                        val layoutHeight = layoutCoordinates.size.height
                                        val thresholdHeight = layoutHeight * 100 / 100
                                        val layoutTop = layoutCoordinates.positionInRoot().y
                                        val layoutBottom = layoutTop + layoutHeight

                                        val parent = layoutCoordinates.parentLayoutCoordinates

                                        parent
                                            ?.boundsInRoot()
                                            ?.let { rect ->
                                                val parentTop = rect.top
                                                val parentBottom = rect.bottom

                                                isTouchingPointsVisibleOnScreen =
                                                    parentBottom - layoutTop > thresholdHeight &&
                                                            (parentTop < layoutBottom - thresholdHeight)
                                            }
                                    }
                                    .fillMaxWidth()
                                    .wrapContentHeight(),
                                contentAlignment = Alignment.Center
                            ) {
                                Column(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalArrangement = Arrangement.spacedBy(8.dp) // Adds spacing between items
                                ) {
                                    touchingPoints.forEach { ad ->
                                        TouchingPointAdItem(
                                            ad = ad,
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            viewModel.onEvent(
                                                AdViewUiEvents.OnTouchingPointClicked(
                                                    it
                                                )
                                            )
                                        }
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            if (loanConfig != null && !adDetails.ad.price.getCurrentPriceWithCurrency()
                                    .isNullOrEmpty()
                            ) {
                                LoanSimulatorUI(
                                    calculateLoanUseCase = CalculateLoanUseCase(loanConfig!!.first),
                                    config = loanConfig!!.first,
                                    adPrice = loanConfig!!.second.toDouble(),
                                    onPreApprovalClick = { url ->
                                        navigateToWebViewScreen(null, url)
                                    }
                                )
                            }

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            adDetails.ad.params?.let { params ->
                                val filteredParams = params.extra.filter { param ->
                                    param.id != "car_inspection_report_link" && param.id != "car_inspection_report_time"
                                }

                                if (filteredParams.isNotEmpty()) {
                                    Column(
                                        Modifier.fillMaxWidth()
                                    ) {
                                        Text(
                                            text = stringResource(R.string.equipments),
                                            fontSize = 16.sp,
                                            modifier = Modifier.padding(horizontal = MaterialTheme.dimens.default),
                                            fontWeight = FontWeight.SemiBold
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        AdParamsGrid(filteredParams) // Pass only filtered params
                                    }
                                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
                                }
                            }

                            SimilarAdsSection(
                                adDetails.similarAds,
                                adDetails.ad.seller?.isShop() == true,
                                isConnectedShop = viewModel.isConnectedShop.value,
                                navigateToSimilarAd
                            )

                            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                            SellerCard(
                                sellerName = adDetails.ad.seller?.getNullableName() ?: "",
                                isVerified = adDetails.ad.seller?.isVerified() ?: false,
                                sellerLogo = adDetails.ad.seller?.getSellerLogo(),
                                website = adDetails.ad.seller?.getSellerWebsite(),
                                isShop = adDetails.ad.seller?.isShop() == true,
                                isAccountAd = viewModel.isAccountAd.value,
                                memberSince = adDetails.ad.seller?.getSellerRegistrationDay(),
                                location = adDetails.ad.seller?.getSellerLocation(),
                                whatsAppAllowed = adDetails.ad.seller?.phoneStartWith05() ?: false,
                                hasPhone = adDetails.ad.seller?.hasPhone() ?: false,
                                onWhatsAppClick = {
                                    openWhatsApp(context as Activity, adDetails.ad)
                                    viewModel.onEvent(AdViewUiEvents.OnWhatsappBtnClicked)
                                },
                                onChatClick = {
                                    viewModel.onEvent(AdViewUiEvents.OnMessagingBtnClicked)
                                },
                                onCallClick = {
                                    showCallDialog = true
                                },
                                onWebsiteClick = {
                                    openUrl(
                                        adDetails.ad.seller?.getSellerWebsite().orEmpty(),
                                        context as Activity
                                    )
                                },
                                onShowStoreClick = {
                                    adDetails.ad.seller?.let { onShopPage(it.getSellerAccountId()) }
                                },
                                onBoostClick = {
                                    onBoostClick(
                                        adDetails.ad.adId.orEmpty(),
                                        adDetails.ad.category?.id.orEmpty(),
                                        adDetails.ad.type?.key?.name.orEmpty(),
                                        VasPacksApplication.AD_LISTING
                                    )
                                    viewModel.onEvent(AdViewUiEvents.OnBoostBtnClicked)
                                }
                            )

                            Spacer(modifier = Modifier.height(60.dp))


                        }
                    }



                    if (showCallDialog && adDetails.ad.seller?.getNullablePhone() != null) {
                        ShowWarningPhoneCallDialog(
                            vendorName = adDetails.ad.seller?.getNullableName().orEmpty(),
                            phoneNumber = adDetails.ad.seller!!.getNullablePhone()!!.number!!,
                            onDismissRequest = {
                                showCallDialog = false
                            }
                        ) {
                            showCallDialog = false
                            val phone = adDetails.ad.seller?.getNullablePhone()?.number

                            phone?.let {
                                var formattedPhone = it.trim()

                                if (!formattedPhone.startsWith("00212")) {
                                    if (formattedPhone.startsWith("0")) {
                                        formattedPhone = formattedPhone.replaceFirst("0", "00212")
                                    } else if (!formattedPhone.startsWith("+212")) {
                                        formattedPhone = "00212$formattedPhone"
                                    }
                                }
                                val phoneUri = Uri.parse("tel:$formattedPhone")
                                val callIntent = Intent(Intent.ACTION_DIAL, phoneUri)
                                context.startActivity(callIntent)
                            }
                            viewModel.onEvent(AdViewUiEvents.OnCallBtnClicked)
                        }
                        viewModel.onEvent(AdViewUiEvents.ShowPhone)
                    }

                }

                is AdViewViewState.Error, AdViewViewState.Loading -> {
                    if (cachedAdDetails != null)
                        Column(
                            Modifier
                                .fillMaxSize()
                        ) {
                            AdImagesSlider(
                                if (cachedAdDetails!!.imageUrl.isNullOrEmpty())
                                    emptyList()
                                else
                                    listOf(
                                        AdDetails.Details.Image(
                                            ImagePaths(
                                                cachedAdDetails!!.imageUrl,
                                                cachedAdDetails!!.imageUrl,
                                                cachedAdDetails!!.imageUrl,
                                                cachedAdDetails!!.imageUrl
                                            )
                                        )
                                    ),
                                cachedAdDetails!!.videoUrl,
                                cachedAdDetails!!.isStore,
                                defaultImageCount = cachedAdDetails!!.imageCount,
                                { index, imagesList, video ->
                                    initialFullScreenPageIndex = index
                                    fullScreenImages = imagesList
                                    videoUrlFullScreen = video
                                    showFullScreenSlider = true
                                },
                                initialFullScreenPageIndex,
                                exoPlayer = exoPlayer
                            )

                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                            ) {
                                AdTitlePriceLocationDate(
                                    cachedAdDetails!!.title.orEmpty(),
                                    cachedAdDetails!!.price,
                                    cachedAdDetails!!.location.orEmpty(),
                                    cachedAdDetails!!.date,
                                    cachedAdDetails!!.isUrgent,
                                    cachedAdDetails!!.isHotDeal == true,
                                    cachedAdDetails!!.discount
                                )


                                Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                                AvitoDisclaimer {
                                }

                                Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                                CategoryRow(
                                    categoryName = cachedAdDetails!!.category.orEmpty(),
                                    categoryType = "",
                                    categoryId = cachedAdDetails!!.categoryIcon,
                                )
                            }
                            // This lock to show an error and retry button
                            if (viewState is AdViewViewState.Error) {
                                Spacer(modifier = Modifier.height(MaterialTheme.dimens.bigger))
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = MaterialTheme.dimens.default),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = (viewState as AdViewViewState.Error).message.getValue(
                                            context
                                        ),
                                        style = MaterialTheme.typography.bodyMedium.copy(color = Color.Red)
                                    )
                                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
                                    Button(
                                        onClick = { viewModel.onEvent(AdViewUiEvents.OnFavoriteBtnClicked) },
                                        modifier = Modifier.align(Alignment.CenterHorizontally)
                                    ) {
                                        Text(stringResource(R.string.common_refresh))
                                    }
                                }
                            }
                        }
                }
            }
        }
        HandleOneTimeEvents(oneTimeEvents.value)
        LaunchedEffect(Unit) {
            viewModel.oneTimeEvents.collectLatest { event ->
                when (event) {
                    is AdViewOneTimeEvents.OpenLogin -> navigateToAuthentication()
                    is AdViewOneTimeEvents.CarCheckSuccess -> showCarInspectionSent = true
                    is AdViewOneTimeEvents.ShowHideProgressAdReport -> {
                        showHideProgressAdReport = event.isLoading
                        showReportDialog = event.isLoading
                    }

                    is AdViewOneTimeEvents.OpenEcommerce -> onBuyEcommerceProduct(
                        event.ad,
                        event.addUuid
                    )

                    is AdViewOneTimeEvents.ShowFirstConversationBtmSheet -> showFirstMessageDialog =
                        true

                    else -> {

                    }

                }
            }
        }
    }
}

@Composable
fun HandleOneTimeEvents(event: AdViewOneTimeEvents?) {
    if (event is AdViewOneTimeEvents.ShowHideProgress && event.isLoading) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = MaterialTheme.dimens.default),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier
                    .size(MaterialTheme.dimens.mediumBig)
                    .padding(bottom = 32.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}


