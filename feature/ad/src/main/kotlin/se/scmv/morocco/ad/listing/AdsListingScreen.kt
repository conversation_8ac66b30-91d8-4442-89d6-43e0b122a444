package se.scmv.morocco.ad.listing

import android.annotation.SuppressLint
import android.view.View
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemContentType
import com.google.ads.mediation.admob.AdMobAdapter
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.admanager.AdManagerAdRequest
import com.google.android.gms.ads.admanager.AdManagerAdView
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.ad.R
import se.scmv.morocco.common.extensions.toBundle
import se.scmv.morocco.designsystem.components.AvHorizontalChipGroup
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.designsystem.components.NewConstructionAdCard
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.components.listing.ExtendedDedNotification
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.IconUrlOrDrawable
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun AdsListingRoute(
    filtersFlow: StateFlow<List<OrionBaseComponentValue>>,
    navigateToFilters: (String?) -> Unit,
    notifyCategoryChanged: (String, String?) -> Unit,
    newItemsLoaded: () -> Unit,
    onCancelExtendedDeliveryClicked: () -> Unit,
    openNewConstruction: (String) -> Unit,
    openAdView: (ListingAd) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: AdsListingViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    val adItems = viewModel.ads.collectAsLazyPagingItems()
    val favoriteStatus = viewModel.favoriteStates.collectAsStateWithLifecycle()

    PullToRefreshBox(
        modifier = modifier.fillMaxSize(),
        isRefreshing = adItems.isLoading(),
        onRefresh = {
            viewModel.onEvent(UiEvent.ListingUiEvents.OnRefreshClicked)
        }
    ) {
        when {
            adItems.isEmpty() -> ScreenEmptyState(
                modifier = Modifier.fillMaxSize(),
                title = R.string.common_oups,
                description = R.string.listing_screen_empty_state_description,
                actionText = R.string.common_search,
                image = R.drawable.img_no_result_illustration,
                onActionClicked = { navigateToFilters(null) }
            )

            adItems.isError() -> ScreenErrorState(
                modifier = Modifier.fillMaxSize(),
                title = stringResource(R.string.common_oups),
                description = adItems.getErrorAsUiText().getValue(context),
                actionText = stringResource(R.string.common_refresh),
            ) {
                viewModel.onEvent(UiEvent.ListingUiEvents.OnRefreshClicked)
            }

            else -> LazyColumn(modifier = Modifier.fillMaxSize()) {
                item {
                    val categoriesListState = rememberLazyListState()
                    HorizontalCategoryItems(
                        items = viewState.childrenCategories.ifEmpty { viewState.popularCategories },
                        lazyListState = categoriesListState,
                        onCategoryItemClick = {
                            viewModel.onEvent(event = UiEvent.OnCategoryClicked(category = it))
                        }
                    )
                    LaunchedEffect(viewState.popularCategories) {
                        categoriesListState.scrollToItem(0)
                    }
                }
                stickyHeader {
                    val filtersListState = rememberLazyListState()
                    if (viewState.filters.isNotEmpty()) {
                        Box(modifier = Modifier.background(MaterialTheme.colorScheme.background)) {
                            AvHorizontalChipGroup(
                                modifier = Modifier.fillMaxWidth(),
                                state = filtersListState,
                                chips = viewState.filters,
                                onChipClicked = { navigateToFilters(it.id) },
                                staticChip = ChipData(
                                    id = "static",
                                    name = stringResource(R.string.common_filter),
                                    selected = false,
                                    icon = IconUrlOrDrawable.Drawable(R.drawable.ic_filter)
                                ),
                                onStaticChipClicked = { navigateToFilters(null) }
                            )
                        }
                    }
                    LaunchedEffect(viewState.popularCategories) {
                        filtersListState.scrollToItem(0)
                    }
                }
                item {
                    TopBannerAd()
                }
                items(
                    count = adItems.itemCount,
                    contentType = adItems.itemContentType()
                ) { index ->
                    when (val ad = adItems[index]) {
                        is ListingAd.DfpBanner -> BannerAd(ad = ad)

                        is ListingAd.ExtendedDelivery -> ExtendedDedNotification(
                            message = stringResource(R.string.search_extended_to_delivery),
                            buttonText = R.string.cancel,
                            onClick = {
                                onCancelExtendedDeliveryClicked()
                            }
                        )

                        is ListingAd.ExtendedSearch -> ExtendedDedNotification(
                            message = ad.types.joinToString("\n") {
                                context.getString(it.toMessage())
                            },
                            buttonText = R.string.extended_search_keep_current_search
                        ) {
                            viewModel.onEvent(UiEvent.ListingUiEvents.OnExtendedSearchKeepFiltersClicked)
                        }

                        is ListingAd.NewConstruction -> NewConstructionAdCard(
                            listingAd = ad,
                        ) {
                            openNewConstruction(ad.externalLink)
                        }

                        is ListingAd.Premium -> ListingCard(
                            listingAd = ad,
                            // TODO use local composition provider
                            isShopSession = false,
                            onFavoriteClick = { ad, _ ->
                                viewModel.onEvent(UiEvent.OnAdFavoriteClicked(ad))
                            }
                        ) {
                            openAdView(ad)
                        }

                        is ListingAd.Published -> ListingCard(
                            listingAd = ad,
                            // TODO use local composition provider
                            isShopSession = false,
                            isInFavorites = favoriteStatus.value.any { it == ad.id },
                            onFavoriteClick = { publishedAd, _ ->
                                viewModel.onEvent(UiEvent.OnAdFavoriteClicked(publishedAd))
                            }
                        ) {
                            openAdView(ad)
                        }

                        else -> {}
                    }
                }
            }
        }
    }
    LaunchedEffect(Unit) {
        filtersFlow.collect { filters ->
            viewModel.onEvent(UiEvent.ListingUiEvents.OnFilterChanged(filters))
        }
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                is AdsListingOneTimeEvents.NotifyCategoryChanged -> notifyCategoryChanged(
                    event.categoryId, event.adTypeKey
                )
            }
        }
    }
}


@SuppressLint("MissingPermission")
@Composable
fun TopBannerAd() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight() // Match FrameLayout's behavior
            .padding(top = MaterialTheme.dimens.small)
    ) {
        AndroidView(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            factory = { context ->
                AdManagerAdView(context).apply {
                    adUnitId = context.getString(R.string.dfp_adunit_listing_large)
                    setAdSizes(AdSize.LARGE_BANNER)

                    val adRequest: AdManagerAdRequest = AdManagerAdRequest.Builder().build()

                    adListener = object : AdListener() {
                        override fun onAdLoaded() {
                            <EMAIL> = View.VISIBLE
                        }

                        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                            <EMAIL> = View.GONE
                        }

                        override fun onAdClosed() {
                            <EMAIL> = View.GONE
                        }
                    }

                    loadAd(adRequest)
                }
            },
            update = {}
        )
    }
}

@SuppressLint("MissingPermission")
@Composable
fun BannerAd(
    ad: ListingAd.DfpBanner,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(
                top = MaterialTheme.dimens.small,
                start = MaterialTheme.dimens.medium,
                end = MaterialTheme.dimens.medium
            )
    ) {
        AndroidView(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            factory = { context ->
                AdManagerAdView(context).apply {
                    adUnitId = context.getString(R.string.dfp_adunit_listing)
                    setAdSizes(AdSize.MEDIUM_RECTANGLE, AdSize.FLUID)
                    val adRequest: AdManagerAdRequest = AdManagerAdRequest.Builder()
                        .addNetworkExtrasBundle(
                            AdMobAdapter::class.java,
                            ad.paramsValues.toBundle()
                        )
                        .build()

                    adListener = object : AdListener() {
                        override fun onAdLoaded() {
                            <EMAIL> = View.VISIBLE
                        }

                        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                            <EMAIL> = View.GONE
                        }

                        override fun onAdClosed() {
                            <EMAIL> = View.GONE
                        }
                    }
                    loadAd(adRequest)
                }
            },
            update = {}
        )
    }
}