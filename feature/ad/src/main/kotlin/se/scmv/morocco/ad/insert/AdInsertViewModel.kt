package se.scmv.morocco.ad.insert

import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastMap
import androidx.compose.ui.util.fastMapIndexed
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.NavigateToInsertResultScreen
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.NavigateToVas
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ScrollCurrentStepToIndex
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ScrollToStep
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ShowCloseConfirmationDialog
import se.scmv.morocco.ad.navigation.AdInsertRoute
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.coroutines.executeInParallel
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdInsertStep
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionTextField
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.usecases.GetAdInsertInitialDataUseCase
import se.scmv.morocco.domain.usecases.GetAdInsertInitialDataUseCase.Companion.DEFAULT_AD_TYPE_KEY
import se.scmv.morocco.domain.usecases.GetAdInsertInitialDataUseCase.Companion.DEFAULT_CATEGORY_ID
import se.scmv.morocco.orion.components.AdInsertLapViewState
import se.scmv.morocco.orion.components.OptionalItemsListener
import se.scmv.morocco.orion.components.VasAction
import se.scmv.morocco.orion.components.media.OrionUiComponentMedia
import se.scmv.morocco.orion.components.toOrionUiComponent
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.asUiText
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class AdInsertViewModel @Inject constructor(
    private val configRepository: ConfigRepository,
    private val adRepository: AdRepository,
    private val getAdInsertInitialDataUseCase: GetAdInsertInitialDataUseCase,
    private val getAccountAdLimitationsUseCase: GetAccountAdLimitationsUseCase,
    private val analyticsHelper: AnalyticsHelper,
    savedStateHandle: SavedStateHandle,
) : ViewModel(), OptionalItemsListener {

    val route = savedStateHandle.toRoute<AdInsertRoute>()

    private val _viewState = MutableStateFlow(AdInsertViewState())
    val viewState = _viewState.asStateFlow()
    private val lapViewState = MutableStateFlow(AdInsertLapViewState())

    private val _oneTimeEvents = MutableSharedFlow<AdInsertOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private lateinit var account: Account.Connected
    private val adId: String? = route.adId
    private var goToImageStep: Boolean = route.goToImageStep
    private var adEditValues = emptyList<OrionBaseComponentValue>()
    private var currentStepIndex = 0
    private var selectedCategoryId = DEFAULT_CATEGORY_ID
    private var selectedAdTypeKey = DEFAULT_AD_TYPE_KEY
    private var selectedCityId: String? = null
    private var selectedAreaId: String? = null
    private var selectedBrandId: String? = null
    private var categories = emptyList<CategoryTree>()
    private var selectedVasPack: VasPack? = null
    private var selectedVasPackage: VasPackage? = null
    private var selectedExeSlotsDay: String? = null
    private var selectedExeSlotsTime: String? = null
    private var isLimit = false

    init {
        getInitialData()
    }

    // This function should be called at the creation of the viewModel.
    fun setAccount(account: Account.Connected) {
        this.account = account
    }

    fun onRefreshClicked() {
        getInitialData()
    }

    fun onBackClicked() {
        viewModelScope.launch {
            val event = if (currentStepIndex > 0) {
                currentStepIndex -= 1
                ScrollToStep(currentStepIndex)
            } else {
                ShowCloseConfirmationDialog
            }
            _oneTimeEvents.emit(event)
        }
    }

    fun onNextClicked() {
        viewModelScope.launch {
            var nonValidItemIndex: Int? = null
            insertSteps()[currentStepIndex].components.forEachIndexed { index, group ->
                val isValid = group.validate()
                if (nonValidItemIndex == null && isValid.not()) {
                    nonValidItemIndex = index
                }
            }
            if (nonValidItemIndex != null) {
                _oneTimeEvents.emit(
                    ScrollCurrentStepToIndex(
                        stepIndex = currentStepIndex,
                        itemIndex = nonValidItemIndex
                    )
                )
            } else {
                if (currentStepIndex < insertSteps().lastIndex) {
                    currentStepIndex += 1
                    _oneTimeEvents.emit(ScrollToStep(currentStepIndex))
                    trackStepScreenView(currentStepIndex)
                } else {
                    trackStepScreenView(currentStepIndex + 1)
                    onFinish()
                }
            }
        }
    }

    override fun onValueChanged(
        component: OrionBaseComponent,
        values: List<OrionBaseComponentValue>
    ) {
        val category = values
            .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
            .firstOrNull()
        if (category != null) {
            onCategorySelected(category.categoryId, category.adTypeKey)
        } else {
            values.forEach { value ->
                when {
                    component is OrionTextField && value is OrionKeyStringValue -> {
                        if (component.notifyLapTitle) {
                            lapViewState.update { it.copy(title = value.value) }
                        }
                        if (component.notifyLapDescription) {
                            lapViewState.update { it.copy(description = value.value) }
                        }
                    }
                }
            }
        }
    }

    private fun onCategorySelected(categoryId: String, adType: AdTypeKey?) {
        if (selectedCategoryId == categoryId && selectedAdTypeKey == adType) return
        selectedCategoryId = categoryId
        selectedAdTypeKey = adType ?: DEFAULT_AD_TYPE_KEY
        viewModelScope.launch {
            getSteps(categoryId = categoryId, adType = adType ?: AdTypeKey.SELL)
            getAdLimitations()
        }
    }

    override fun onPrincipalImageChanged(image: OrionUiComponentMedia?) {
        lapViewState.update { it.copy(image = image) }
    }

    override fun onVasPackageSelected(vasPack: VasPack, vasPackage: VasPackage) {
        selectedVasPack = vasPack
        selectedVasPackage = vasPackage
    }

    override fun onExeSlotDaySelected(day: String) {
        selectedExeSlotsDay = day
    }

    override fun onExeSlotTimeSelected(timeId: String) {
        selectedExeSlotsTime = timeId
    }

    override fun onAddImageClicked() {
        onBackClicked()
    }

    override fun showSnackBar(uiText: UiText, type: SnackBarType) {
        viewModelScope.launch {
            SnackBarController.showSnackBar(
                event = SnackBarEvent(message = uiText, type = type)
            )
        }
    }

    override fun executeVasAction(action: VasAction) {
        when {
            action is VasAction.RefreshVasPacksByBrand
                    && action.brandId == selectedBrandId -> return

            action is VasAction.RefreshVasPacksByCityArea
                    && action.cityId == selectedCityId
                    && action.areaId == selectedAreaId -> return
        }
        selectedCityId = (action as? VasAction.RefreshVasPacksByCityArea)?.cityId
        selectedAreaId = (action as? VasAction.RefreshVasPacksByCityArea)?.areaId
        selectedBrandId = (action as? VasAction.RefreshVasPacksByBrand)?.brandId
        viewModelScope.launch {
            showHideLoading(true)
            val result = adRepository.getVasPacks(
                application = VasPacksApplication.AD_INSERT,
                adID = adId,
                adCategory = selectedCategoryId,
                adType = selectedAdTypeKey,
                cityId = (action as? VasAction.RefreshVasPacksByCityArea)?.cityId,
                areaId = (action as? VasAction.RefreshVasPacksByCityArea)?.areaId,
                brandId = (action as? VasAction.RefreshVasPacksByBrand)?.brandId,
            )
            when (result) {
                is Resource.Success -> {
                    lapViewState.update { state ->
                        state.copy(
                            vasPacks = result.data.packs.toPersistentList(),
                            vasExecutionSlots = result.data.executionSlots
                        )
                    }
                    showHideLoading(false)
                }

                is Resource.Failure -> _viewState.update {
                    AdInsertViewState(
                        steps = persistentListOf(),
                        isLoading = false,
                        error = result.error.asUiText()
                    )
                }
            }
            showHideLoading(false)
        }
    }

    private fun getInitialData() {
        viewModelScope.launch {
            showHideLoading(true)
            when (val result = getAdInsertInitialDataUseCase(adId)) {
                is Resource.Success -> {
                    adEditValues = result.data.adEditValues
                    categories = result.data.categories
                    selectedCategoryId = result.data.selectedCategoryId
                    selectedAdTypeKey = result.data.selectedAdTypeKey
                    selectedCityId = result.data.selectedCityId
                    selectedAreaId = result.data.selectedAreaId
                    selectedBrandId = result.data.selectedBrandId
                    getSteps(
                        categoryId = selectedCategoryId,
                        adType = selectedAdTypeKey,
                        cityId = selectedCityId,
                        areaId = selectedAreaId,
                        brandId = selectedBrandId
                    )
                    launch {
                        getAdLimitations()
                    }
                }

                is Resource.Failure -> _viewState.update {
                    it.copy(error = result.error.asUiText(), isLoading = false)
                }
            }
        }
    }

    private suspend fun getSteps(
        categoryId: String,
        adType: AdTypeKey,
        cityId: String? = null,
        areaId: String? = null,
        brandId: String? = null
    ) {
        showHideLoading(true)

        val result = executeInParallel(
            firstCall = { configRepository.getAdInsertSteps(categoryId, adType) },
            secondCall = {
                adRepository.getVasPacks(
                    application = VasPacksApplication.AD_INSERT,
                    adID = adId,
                    adCategory = categoryId,
                    adType = adType,
                    cityId = cityId,
                    areaId = areaId,
                    brandId = brandId
                )
            }
        )
        when (result) {
            is Resource.Success -> {
                _viewState.update {
                    AdInsertViewState(
                        steps = withContext(Dispatchers.Default) {
                            result.data.first.toUiSteps(
                                categoryId = categoryId,
                                adType = adType,
                                adEditValues = adEditValues,
                            )
                        },
                        isLoading = false,
                        error = null
                    )
                }
                lapViewState.update {
                    it.copy(
                        vasPacks = result.data.second.packs.toPersistentList(),
                        vasExecutionSlots = result.data.second.executionSlots
                    )
                }
                if (goToImageStep) {
                    goToImageStep = false
                    currentStepIndex = 2
                    // Add a slight delay to ensure the UI is ready, without it the pager won't scroll.
                    delay(200)
                    _oneTimeEvents.emit(ScrollToStep(currentStepIndex))
                }
            }

            is Resource.Failure -> {
                _viewState.update {
                    AdInsertViewState(
                        steps = persistentListOf(),
                        isLoading = false,
                        error = result.error.asUiText()
                    )
                }
            }
        }
    }

    private suspend fun getAdLimitations() {
        val limitation = getAccountAdLimitationsUseCase(
            categoryId = selectedCategoryId,
            adTypeKey = selectedAdTypeKey,
            isEdit = adId != null
        )
        isLimit = limitation?.isLimit ?: false
        lapViewState.update { it.copy(limitation = limitation) }
    }

    private suspend fun onFinish() {
        showHideLoading(true)
        val category = CategoryTree.findTree(
            trees = categories,
            categoryId = selectedCategoryId,
            adTypeKey = selectedAdTypeKey
        )?.category
        val values = insertSteps().map {
            it.components.map { group -> group.collectValue() }
        }.flatten().flatten()
        val result = adRepository.upsert(
            adID = adId,
            category = category,
            adType = selectedAdTypeKey,
            isLimit = isLimit,
            values = values,
            vasPack = selectedVasPack,
            vasPackage = selectedVasPackage
        )
        showHideLoading(false)
        when (result) {
            is Resource.Success -> {
                val event = if (selectedVasPackage?.price == 0) {
                    NavigateToInsertResultScreen(
                        from = if (adId != null) VasRoute.From.ACCOUNT else VasRoute.From.AD_INSERT
                    )
                } else {
                    NavigateToVas(
                        VasRoute(
                            from = if (adId != null) VasRoute.From.ACCOUNT else VasRoute.From.AD_INSERT,
                            adId = result.data,
                            adCategory = selectedCategoryId,
                            adTypeKey = selectedAdTypeKey.name,
                            application = VasPacksApplication.AD_INSERT.name,
                            chosenVasPackKey = selectedVasPack?.key,
                            chosenVasPackageId = selectedVasPackage?.id,
                            chosenExecutionSlotsDay = selectedExeSlotsDay,
                            chosenExecutionSlotsTimeId = selectedExeSlotsTime,
                        )
                    )
                }
                _oneTimeEvents.emit(event)
            }

            is Resource.Failure -> renderFailure(result.error)
        }
    }

    private fun showHideLoading(isLoading: Boolean) {
        _viewState.update { it.copy(isLoading = isLoading) }
    }

    private fun insertSteps() = viewState.value.steps

    private fun List<AdInsertStep>.toUiSteps(
        categoryId: String,
        adType: AdTypeKey,
        adEditValues: List<OrionBaseComponentValue>,
    ): PersistentList<Step> = fastMap { step ->
        Step(
            tipsTitle = step.tipsTitle,
            tipsContent = step.tipsContent.toPersistentList(),
            infoUrl = step.infoUrl,
            components = step.components.fastMapIndexed { index, component ->
                val hasNextTextFieldItem = if (index < step.components.lastIndex) {
                    step.components.slice(
                        IntRange(start = index + 1, endInclusive = step.components.lastIndex)
                    ).fastAny { it is OrionTextField && it.enabled }
                } else {
                    false
                }
                component.toOrionUiComponent(
                    categories = categories,
                    categoryId = categoryId,
                    adTypeKey = adType,
                    lapViewState = lapViewState,
                    hasNextTextFieldItem = hasNextTextFieldItem,
                    initialValues = adEditValues,
                    listener = this@AdInsertViewModel
                )
            }.filterNotNull().toPersistentList()
        )
    }.toPersistentList()

    private fun trackStepScreenView(step: Int) {
        val properties = mutableSetOf(
            Param(key = AnalyticsEvent.ParamKeys.LANG, value = LocaleManager.getCurrentLanguage()),
            Param(
                key = AnalyticsEvent.ParamKeys.SCREEN_NAME,
                value = if (adId != null) {
                    AnalyticsEvent.ScreensNames.AD_EDIT
                } else AnalyticsEvent.ScreensNames.AD_INSERT
            )
        )
        CategoryTree.findTree(
            trees = categories,
            categoryId = selectedCategoryId,
            adTypeKey = selectedAdTypeKey
        )?.category?.getAnalyticsParams()?.let { params ->
            properties.addAll(params)
        }
        properties.add(Param(AnalyticsEvent.ParamKeys.STEP, step.toString()))
        properties.add(Param(AnalyticsEvent.ParamKeys.SELLER_TYPE, account.analyticsAccountType()))
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SCREEN_VIEW,
                properties = properties
            ),
            where = setOf(AnalyticsAddons.FIREBASE)
        )
    }
}