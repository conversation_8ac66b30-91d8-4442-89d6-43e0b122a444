package se.scmv.morocco.ad.insert

import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.orion.components.AdInsertLapLimitation
import javax.inject.Inject

// case 1: Informing the user about the status of hir free insertion and the insertion fee to pay once reached the limit of the free insertion
// case 2: User reaches the limit of his free ads and now need to pay for his current ad.
// case 3: The case of a category where the user needs to pay from the first insertion like car rental.
// case 4: User reaches the limit to open a shop

class GetAccountAdLimitationsUseCase @Inject constructor(
    private val adRepository: AdRepository
) {
    suspend operator fun invoke(
        categoryId: String,
        adTypeKey: AdTypeKey,
        isEdit: Boolean
    ): AdInsertLapLimitation? {
        val result = adRepository.getAdLimitations(categoryId, adTypeKey)
        return when (result) {
            is Resource.Success -> {
                val userAdCount = result.data.userCategoryAdCount
                val configuredFreeLimit = result.data.configuredCategoryFreeLimit
                val configuredStoreLimit = result.data.configuredCategoryStoreLimit
                val categoryFree = result.data.categoryFree
                val categoryStore = result.data.categoryStore
                when {
                    // CASE 3:
                    configuredFreeLimit == 0 -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation3,
                        messageArgs = persistentListOf(),
                        alertType = AvAlertType.Warning,
                        isLimit = false
                    )

                    // CASE 1:
                    configuredFreeLimit != null && userAdCount < configuredFreeLimit -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation1,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}",
                            "$configuredFreeLimit",
                            categoryFree?.name.orEmpty()
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = false
                    )

                    // CASE 2:
                    configuredFreeLimit != null && userAdCount >= configuredFreeLimit
                            && (configuredStoreLimit == null || userAdCount < configuredStoreLimit)
                        -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation2,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}"
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = true
                    )

                    // CASE 4:
                    configuredStoreLimit != null && userAdCount >= configuredStoreLimit -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation4,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}",
                            categoryStore?.name.orEmpty()
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = true
                    )

                    else -> null
                }
            }

            is Resource.Failure -> null
        }
    }
}