package se.scmv.morocco.ad.listing

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun HorizontalCategoryItems(
    items: List<PopularCategory>,
    lazyListState: LazyListState,
    onCategoryItemClick: (PopularCategory) -> Unit
) {
    LazyRow(state = lazyListState) {
        items(items.filter { it.enabled }) { item ->
            CategoryItem(
                category = item,
            ) {
                onCategoryItemClick(item)
            }
        }
    }
}

@Composable
fun CategoryItem(
    category: PopularCategory,
    onCategoryItemClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .width(75.dp)
            .padding(horizontal = MaterialTheme.dimens.small),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(50.dp)
                .clip(shape = CircleShape)
                .background(MaterialTheme.colorScheme.tertiaryContainer)
                .clickable {
                    onCategoryItemClick()
                },
            contentAlignment = Alignment.Center
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(category.icon)
                    .decoderFactory(SvgDecoder.Factory())
                    .build(),
                contentDescription = null,
                modifier = Modifier
                    .size(40.dp)
                    .clip(shape = CircleShape),
                contentScale = ContentScale.Crop,
            )
        }

        Text(
            text = category.name,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 2,
            textAlign = TextAlign.Center,
            lineHeight = 13.sp,
            fontSize = 10.sp,
        )
    }
}