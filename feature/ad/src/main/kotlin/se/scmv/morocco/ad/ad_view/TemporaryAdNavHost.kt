package se.scmv.morocco.ad.ad_view

import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import se.scmv.morocco.ad.ad_view.state.AdViewUiEvents
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.CachedAdDetails
import se.scmv.morocco.domain.models.VasPacksApplication

@Composable
fun TemporaryAdNavHost(
    modifier: Modifier,
    adId: String,
    imageUrl: String? = null,
    title: String? = null,
    date: String?,
    imageCount: Int,
    videoCount: Int,
    videoUrl: String?,
    isStore: Boolean,
    price: AdPrice,
    location: String?,
    category: String?,
    categoryIcon: String?,
    isUrgent: Boolean,
    isHotDeal: Boolean,
    discount: Int?,
    navController: NavHostController,
    onNavigateBack: () -> Unit,
    navigateToAuthentication: () -> Unit,
    navigateToVasActivity: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    onShopPage: (Id: String) -> Unit,
    showMessageAfterAuth: Boolean,
    onBuyEcommerceProduct: (ad: AdDetails.Details, userId: String) -> Unit,
    authResult: AdViewUiEvents? = null
) {
    val viewModel: AdViewViewModel = hiltViewModel()
    var currentTitle by remember { mutableStateOf(title) }
    var currentImageUrl by remember { mutableStateOf(imageUrl) }

    LaunchedEffect(authResult) {
        authResult?.let {
            viewModel.onEvent(it)
        }
    }
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = "adView/$adId"
    ) {
        composable(
            route = "adView/{adId}",
            arguments = listOf(navArgument("adId") { type = NavType.StringType })
        ) { backStackEntry ->
            val currentAdId = backStackEntry.arguments?.getString("adId") ?: return@composable
            viewModel.saveCachedAdDetails(
                adId,
                CachedAdDetails(
                    imageUrl,
                    title,
                    adId,

                        try {
                            date?.let {
                                if (it.endsWith("Z")) {
                                    // Parse as Instant then convert to LocalDateTime
                                    Instant.parse(it).toLocalDateTime(TimeZone.UTC)
                                } else {
                                    // Parse directly as LocalDateTime
                                    LocalDateTime.parse(it)
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            null
                        }
                    ,
                    imageCount,
                    videoCount,
                    videoUrl,
                    isStore,
                    price,
                    location,
                    category,
                    categoryIcon,
                    isUrgent,
                    isHotDeal,
                    discount
                )
            )
            AdViewScreen(
                viewModel = viewModel,
                navigateBack = {
                    val canGoBack = navController.popBackStack()
                    if (!canGoBack) {
                        onNavigateBack()
                    }
                },
                navigateToSimilarAd = { newAdId, imageUrl, title, date, imageCount, videoCount, videoUrl, isStore, price, location, category, categoryIcon, isUrgent, isHotDeal, discount ->
                    viewModel.saveCachedAdDetails(
                        newAdId,
                        CachedAdDetails(
                            imageUrl,
                            title,
                            newAdId,
                            date,
                            imageCount,
                            videoCount,
                            videoUrl,
                            isStore,
                            price ?: AdPrice.Unavailable,
                            location,
                            category,
                            categoryIcon,
                            isUrgent,
                            isHotDeal,
                            discount
                        )
                    )

                    currentTitle = title
                    currentImageUrl = imageUrl

                    navController.navigate("adView/$newAdId") {
                        popUpTo("adView/$newAdId") {
                            inclusive = true
                        } // Clears previous instance of the same ad
                    }

                },
                onOpenPdf = { pdfUrl ->
                    val encodedPdfUrl = Uri.encode(pdfUrl)
                    navController.navigate("pdfViewer/$encodedPdfUrl")

                },
                navigateToAuthentication = navigateToAuthentication,
                showMessageAfterAuth = showMessageAfterAuth,
                onShopPage = onShopPage,
                onBuyEcommerceProduct = onBuyEcommerceProduct,
                onBoostClick = { adId, adCategoryKey, adType, application ->
                    navigateToVasActivity(adId, adCategoryKey, adType, application)
                },
                navigateToWebViewScreen = {_,_ ->}
            )

        }

        // PDF Viewer Screen
        composable(
            route = "pdfViewer/{pdfUrl}",
            arguments = listOf(navArgument("pdfUrl") { type = NavType.StringType })
        ) { backStackEntry ->
            val encodedPdfUrl = backStackEntry.arguments?.getString("pdfUrl") ?: return@composable
            val pdfUrl = Uri.decode(encodedPdfUrl) // Decode the PDF URL

            PDFViewerScreen(
                pdfUrl = pdfUrl,
                navigateBack = {}
            )

        }

    }
}