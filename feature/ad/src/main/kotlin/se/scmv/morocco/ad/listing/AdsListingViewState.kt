package se.scmv.morocco.ad.listing

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.domain.models.ExtendedSearchType
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

@Stable
data class AdsListingViewState(
    val popularCategories: PersistentList<PopularCategory> = persistentListOf(),
    val childrenCategories: PersistentList<PopularCategory> = persistentListOf(),
    val filters: PersistentList<ChipData> = persistentListOf(),
)

@Stable
data class PopularCategory(
    val id: String,
    val name: String,
    val icon: String,
    val adTypeKey: String?,
    val enabled: Boolean = true
)

sealed interface UiEvent {
    sealed interface ListingUiEvents : UiEvent {
        data class OnFilterChanged(val filtersValues: List<OrionBaseComponentValue>) : ListingUiEvents
        data object OnRefreshClicked : ListingUiEvents
        data object OnExtendedSearchKeepFiltersClicked : ListingUiEvents
    }

    data class OnAdFavoriteClicked(val ad: ListingAd.Published) : UiEvent

    data class OnCategoryClicked(val category: PopularCategory) : UiEvent
}

sealed interface AdsListingOneTimeEvents {
    data class NotifyCategoryChanged(
        val categoryId: String,
        val adTypeKey: String?
    ) : AdsListingOneTimeEvents
}

fun ExtendedSearchType.toMessage(): Int = when (this) {
    ExtendedSearchType.TO_WHOLE_CITY -> R.string.search_extended_to_whole_city
    ExtendedSearchType.TO_NEARBY_CITIES -> R.string.search_extended_to_nearby_cities
    ExtendedSearchType.TO_WHOLE_COUNTRY -> R.string.search_extended_to_whole_country
    ExtendedSearchType.TO_BIGGER_PRICE_RANGE -> R.string.search_extended_to_bigger_price
}