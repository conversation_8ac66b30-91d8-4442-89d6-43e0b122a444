package se.scmv.morocco.ad.vas.master

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import androidx.compose.ui.graphics.ImageBitmap
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.parcelize.Parcelize
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvStepperHeaderStep
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdToBoost
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.PriceUnit
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPacks
import se.scmv.morocco.domain.models.VasPacksApplication

@Parcelize
data class IntentParams(
    val from: From,
    val adId: String,
    val application: VasPacksApplication,
    val adCategory: String? = null,
    val adType: AdTypeKey? = null,
    val chosenVasPackage: String? = null,
    val chosenExecutionSlotsDay: String? = null,
    val chosenExecutionSlotsTimeId: String? = null,
) : Parcelable {
    @Parcelize
    sealed class From(val trackingName: String) : Parcelable {
        data object AdInsert : From("ad_insert")
        data object AdView : From("ad_view")
        data object Account : From("my_account")
        data class Notification(val source: String) : From("notification")
    }
}

@Stable
sealed interface VasMasterViewState {

    data object Loading : VasMasterViewState

    data class Success(
        val step1State: ChooseVasPackageViewState,
        val step2State: ChoosePaymentMethodViewState,
        val step3State: CashPaymentViewState?,
        val currentPageIndex: Int = PAGE_INDEX_CHOOSE_VAS_PACKAGE,
        val chosenVasPack: VasPack? = null,
        val chosenVasPackage: VasPackage? = null,
        val chosenExecutionSlotsDay: String? = null,
        val chosenExecutionSlotsTimeId: String? = null,
        val chosenPaymentMethod: PaymentMethodType? = null,
        val paymentCodeOrLink: String? = null,
        val isLoading: Boolean = false,
    ) : VasMasterViewState {
        fun footerBtnText(isShop: Boolean) = when (currentPageIndex) {
            PAGE_INDEX_CHOOSE_VAS_PACKAGE -> R.string.common_validate
            PAGE_INDEX_CHOOSE_PAYMENT_METHOD -> R.string.common_pay
            PAGE_INDEX_CHOOSE_SUMMARY -> if (isShop) {
                R.string.shop_payment_success_screen_btn_text
            } else R.string.common_save

            else -> R.string.common_validate
        }

        fun canSubmit() = when {
            currentPageIndex == PAGE_INDEX_CHOOSE_VAS_PACKAGE && chosenVasPackage == null -> false
            currentPageIndex == PAGE_INDEX_CHOOSE_PAYMENT_METHOD && chosenPaymentMethod == null -> false
            else -> true
        }
    }

    data class Error(val message: UiText) : VasMasterViewState
}

@Stable
data class ChooseVasPackageViewState(
    val adToBoost: AdToBoost,
    val vasPacks: VasPacks,
    val showPrivatePaymentProviders: Boolean,
)

@Stable
data class ChoosePaymentMethodViewState(
    val shopAccountInfo: ShopAccountInfo?,
    val paymentMethods: ImmutableList<PaymentMethod> = persistentListOf()
)

@Stable
data class ShopAccountInfo(
    val imageUrl: String?,
    val name: String,
    val isVerified: Boolean,
    val storePoints: Int,
    val storeSoldUnit: PriceUnit,
    val storeMembership: String,
    val dates: ImmutableList<Pair<Int, String>>,
)

@Stable
data class PaymentMethod(
    @StringRes val title: Int,
    @StringRes val description: Int,
    @DrawableRes val icon: Int,
    val type: PaymentMethodType
)

@Stable
data class CashPaymentViewState(
    val cashProviderTitle: Int,
    val step1Description: Int,
    val step1Icon: Int,
    val step1Action: Int,
    val isBankingAppVisible: Boolean,
)

val headerSteps = listOf(
    AvStepperHeaderStep(R.drawable.ic_ad_insert_step1),
    AvStepperHeaderStep(R.drawable.ic_payement_step2),
    AvStepperHeaderStep(R.drawable.ic_payement_step3),
).toImmutableList()

sealed interface VasMasterOneTimeEvents {
    data class GoToPage(val index: Int) : VasMasterOneTimeEvents
    data object RecordCashReceipt : VasMasterOneTimeEvents
    data class PrintCashReceipt(val bitmap: ImageBitmap) : VasMasterOneTimeEvents
    data object OpenAccountAdsScreen : VasMasterOneTimeEvents
    data class OpenUrl(val url: String, val isCreditCardPayment: Boolean = false) :
        VasMasterOneTimeEvents

    data object OpenSupportedBankAppsBtmSheet : VasMasterOneTimeEvents

    data object CloseVas : VasMasterOneTimeEvents
}