package se.scmv.morocco.ad.vas.master

import android.content.Context
import androidx.compose.material3.SnackbarDuration
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastFirstOrNull
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.CloseVas
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.GoToPage
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.common.utils.IOUtils
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.RemoteConfigRepository
import se.scmv.morocco.ui.SnackBarAction
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.renderFailure
import java.util.UUID
import javax.inject.Inject

const val PAGE_INDEX_CHOOSE_VAS_PACKAGE = 0
const val PAGE_INDEX_CHOOSE_PAYMENT_METHOD = 1

// Page that shows the cash or bank app code or the Avitokens success page for shops accounts.
const val PAGE_INDEX_CHOOSE_SUMMARY = 2

@HiltViewModel
class VasMasterViewModel @Inject constructor(
    private val accountRepository: AccountRepository,
    private val adRepository: AdRepository,
    private val remoteConfigRepository: RemoteConfigRepository,
    private val buildVasMasterViewStateUseCase: BuildVasMasterViewStateUseCase,
    private val analyticsHelper: AnalyticsHelper,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    // Intent params.
    private val route = savedStateHandle.toRoute<VasRoute>()
    private lateinit var account: Account.Connected

    private val _viewState = MutableStateFlow<VasMasterViewState>(VasMasterViewState.Loading)
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<VasMasterOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private var paymentProcessJob: Job? = null

    init {
        buildInitialState()
    }

    // This function must be called after the creation of an instance of this class.
    fun setAccount(account: Account.Connected) {
        this.account = account
    }

    fun onRefresh() {
        buildInitialState()
    }

    fun onVasPackageChanged(chosenVasPack: VasPack, chosenVasPackage: VasPackage) {
        _viewState.update {
            getViewStateAsSuccess().copy(
                chosenVasPack = chosenVasPack,
                chosenVasPackage = chosenVasPackage
            )
        }
    }

    fun onPaymentMethodChanged(paymentMethodType: PaymentMethodType) {
        _viewState.update {
            getViewStateAsSuccess().copy(
                step3State = getStep3ViewState(paymentMethodType),
                chosenPaymentMethod = paymentMethodType,
            )
        }
    }

    fun onExeSlotDaySelected(day: String) {
        if (getViewStateAsSuccess().chosenExecutionSlotsDay == day) return
        _viewState.update { getViewStateAsSuccess().copy(chosenExecutionSlotsDay = day) }
    }

    fun onExeSlotTimeSelected(time: String) {
        if (getViewStateAsSuccess().chosenExecutionSlotsTimeId == time) return
        _viewState.update { getViewStateAsSuccess().copy(chosenExecutionSlotsTimeId = time) }
    }

    fun onBackClicked() {
        if (getCurrentPageIndex() == PAGE_INDEX_CHOOSE_VAS_PACKAGE) {
            viewModelScope.launch { _oneTimeEvents.emit(CloseVas) }
            return
        }
        if (paymentProcessJob?.isActive == true) return
        viewModelScope.launch {
            _oneTimeEvents.emit(GoToPage(getCurrentPageIndex() - 1))
            _viewState.update {
                getViewStateAsSuccess().copy(currentPageIndex = getCurrentPageIndex() - 1)
            }
        }
    }

    fun onNextClicked() {
        viewModelScope.launch {
            val state = getViewStateAsSuccess()
            when (getCurrentPageIndex()) {
                PAGE_INDEX_CHOOSE_VAS_PACKAGE -> {
                    if (state.chosenVasPackage?.price == 0) {
                        _oneTimeEvents.emit(CloseVas)
                    } else {
                        _oneTimeEvents.emit(
                            GoToPage(index = PAGE_INDEX_CHOOSE_PAYMENT_METHOD)
                        )
                        _viewState.update {
                            state.copy(currentPageIndex = PAGE_INDEX_CHOOSE_PAYMENT_METHOD)
                        }
                    }
                    trackStepScreenView(
                        step = AnalyticsEvent.ParamValues.VAS_SELECTION,
                        value = state.chosenVasPack?.title
                    )
                }

                PAGE_INDEX_CHOOSE_PAYMENT_METHOD -> {
                    // If a payment is already in progress, cancel it and start a new one.
                    paymentProcessJob?.cancel()
                    trackStepScreenView(
                        step = AnalyticsEvent.ParamValues.METHOD_SELECTION,
                        value = state.chosenPaymentMethod?.trackingName
                    )
                    viewModelScope.launch {
                        proceedPayment()
                    }.also {
                        paymentProcessJob = it
                    }
                }

                PAGE_INDEX_CHOOSE_SUMMARY -> {
                    _oneTimeEvents.emit(
                        when (account) {
                            is Account.Connected.Shop -> VasMasterOneTimeEvents.OpenAccountAdsScreen
                            is Account.Connected.Private -> VasMasterOneTimeEvents.RecordCashReceipt
                        }
                    )
                }
            }
        }
    }

    fun onCashStep1BtnClicked() {
        // Should not be null because we can't reach this step if we don't had a success in the previous step .
        getViewStateAsSuccess().chosenPaymentMethod?.let { type ->
            when (type) {
                PaymentMethodType.TASSHILATE -> remoteConfigRepository.getString(
                    key = RemoteConfigRepository.TASSHILAT_AGENCIES_URL_KEY
                )?.let { url -> VasMasterOneTimeEvents.OpenUrl(url) }

                PaymentMethodType.CASH_PLUS -> remoteConfigRepository.getString(
                    key = RemoteConfigRepository.CASH_PLUS_AGENCIES_URL_KEY
                )?.let { url -> VasMasterOneTimeEvents.OpenUrl(url) }

                PaymentMethodType.BANK_APP -> VasMasterOneTimeEvents.OpenSupportedBankAppsBtmSheet
                else -> null
            }?.let { event ->
                viewModelScope.launch { _oneTimeEvents.emit(event) }
            }
        }
    }

    fun saveImageToDeviceStorage(context: Context, imageBitmap: ImageBitmap) {
        val file = IOUtils.saveBitmapInFile(
            context,
            bitmap = imageBitmap.asAndroidBitmap(),
            fileName = UUID.randomUUID().toString()
        )
        if (file != null) {
            viewModelScope.launch {
                SnackBarController.showSnackBar(
                    event = SnackBarEvent(
                        message = UiText.FromRes(R.string.common_screenshot_saved),
                        type = SnackBarType.SUCCESS,
                        duration = SnackbarDuration.Long,
                        action = SnackBarAction(
                            name = UiText.FromRes(R.string.common_ok),
                            onClicked = {
                                viewModelScope.launch { _oneTimeEvents.emit(CloseVas) }
                            }
                        )
                    )
                )
            }
        } else {
            renderFailure(UiText.FromRes(R.string.common_screenshot_not_saved_error))
        }
    }

    fun onCashReceiptRecorded(bitmap: ImageBitmap) {
        viewModelScope.launch {
            _oneTimeEvents.emit(VasMasterOneTimeEvents.PrintCashReceipt(bitmap))
        }
    }

    private fun buildInitialState() {
        viewModelScope.launch {
            val initialState = buildVasMasterViewStateUseCase(
                from = route.from,
                adId = route.adId,
                application = VasPacksApplication.valueOf(route.application),
                categoryId = route.adCategory,
                adTypeKey = AdTypeKey.safeValueOf(route.adTypeKey),
                packId = route.chosenVasPackageId
            )
            when (initialState) {
                is VasMasterViewState.Success -> {
                    val initialVasPackageId = route.chosenVasPackageId
                    if (initialVasPackageId != null) {
                        val initialVasPack = initialState.step1State.vasPacks.packs
                            .fastFirstOrNull { pack ->
                                pack.packages.fastAny { it.id == initialVasPackageId }
                            }
                        val initialVasPackage = initialVasPack?.packages?.fastFirstOrNull {
                            it.id == initialVasPackageId
                        }
                        _viewState.update {
                            initialState.copy(
                                chosenVasPack = initialVasPack,
                                chosenVasPackage = initialVasPackage,
                                chosenExecutionSlotsDay = route.chosenExecutionSlotsDay,
                                chosenExecutionSlotsTimeId = route.chosenExecutionSlotsTimeId
                            )
                        }
                        if (!route.chosenVasPackageId.isNullOrEmpty()) {
                            onNextClicked()
                        }
                    } else {
                        _viewState.update { initialState }
                    }
                }

                else -> _viewState.update { initialState }
            }
        }
    }

    private suspend fun proceedPayment() {
        val state = getViewStateAsSuccess()
        val packId = state.chosenVasPackage?.id ?: return
        val paymentMethodType = state.chosenPaymentMethod ?: return
        val executionDay = state.chosenExecutionSlotsDay
        val executionTime = state.chosenExecutionSlotsTimeId
        showHideLoading(true)
        val result = adRepository.proceedPayment(
            adId = route.adId,
            packId = packId,
            application = VasPacksApplication.valueOf(route.application),
            paymentMethodType = paymentMethodType,
            executionSlotsDay = executionDay,
            executionSlotsTime = executionTime
        )
        showHideLoading(false)
        when (result) {
            is Resource.Success -> {
                trackStepScreenView(step = AnalyticsEvent.ParamValues.PAYMENT, value = null)
                when (paymentMethodType) {
                    PaymentMethodType.CREDIT_CARD -> {
                        _oneTimeEvents.emit(
                            VasMasterOneTimeEvents.OpenUrl(
                                url = result.data.codeOrLink,
                                isCreditCardPayment = true
                            )
                        )
                        _viewState.update {
                            state.copy(currentPageIndex = PAGE_INDEX_CHOOSE_PAYMENT_METHOD)
                        }
                    }

                    else -> {
                        _viewState.update {
                            state.copy(
                                currentPageIndex = PAGE_INDEX_CHOOSE_SUMMARY,
                                paymentCodeOrLink = result.data.codeOrLink
                            )
                        }
                        _oneTimeEvents.emit(GoToPage(PAGE_INDEX_CHOOSE_SUMMARY))
                        // Once we pay using avitokens,
                        // we should refresh the account to get the new sold.
                        if (paymentMethodType == PaymentMethodType.AVITOKENS) {
                            accountRepository.refreshAccountFromRemote()
                        }
                    }
                }
            }

            is Resource.Failure -> renderFailure(result.error)
        }
    }

    private fun getViewStateAsSuccess(): VasMasterViewState.Success = requireNotNull(
        _viewState.value as? VasMasterViewState.Success
    ) {
        "You're trying to get the current state as VasMasterViewState.Success but value = ${_viewState.value}"
    }

    private fun getCurrentPageIndex() = getViewStateAsSuccess().currentPageIndex

    private fun showHideLoading(isLoading: Boolean) {
        _viewState.update { getViewStateAsSuccess().copy(isLoading = isLoading) }
    }

    private fun getStep3ViewState(paymentMethodType: PaymentMethodType): CashPaymentViewState? {
        return when (paymentMethodType) {
            PaymentMethodType.TASSHILATE -> CashPaymentViewState(
                cashProviderTitle = R.string.cash_checkout_screen_cash_provider_title,
                step1Description = R.string.cash_checkout_screen_cash_step_1,
                step1Icon = R.drawable.img_cash_payment_cash_provider_step1_icon,
                step1Action = R.string.cash_checkout_screen_cash_provider_step_1_btn,
                isBankingAppVisible = false
            )

            PaymentMethodType.CASH_PLUS -> CashPaymentViewState(
                cashProviderTitle = R.string.cash_checkout_screen_cash_plus_provider_title,
                step1Description = R.string.cash_checkout_screen_cash_plus_step_1,
                step1Icon = R.drawable.img_cash_payment_cash_provider_step1_icon,
                step1Action = R.string.cash_checkout_screen_cash_provider_step_1_btn,
                isBankingAppVisible = false
            )

            PaymentMethodType.BANK_APP -> CashPaymentViewState(
                cashProviderTitle = R.string.cash_checkout_screen_bank_app_provider_title,
                step1Description = R.string.cash_checkout_screen_bank_app_step_1,
                step1Icon = R.drawable.img_cash_payment_banking_app_provider_step1_icon,
                step1Action = R.string.cash_checkout_screen_bank_app_provider_step_1_btn,
                isBankingAppVisible = true
            )

            else -> null
        }
    }

    private fun trackStepScreenView(step: String, value: String?) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SCREEN_VIEW,
                properties = mutableSetOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.SCREEN_NAME,
                        value = AnalyticsEvent.ParamValues.PAYMENT
                    ),
                ).apply {
                    val fromWhere = route.from
                    add(
                        Param(
                            key = AnalyticsEvent.ParamKeys.SOURCE,
                            value = fromWhere.trackingName
                        )
                    )
                    if (fromWhere == VasRoute.From.NOTIFICATION) {
                        add(
                            Param(
                                key = AnalyticsEvent.ParamKeys.NOTIFICATION_SOURCE,
                                value = "firebase"
                            )
                        )
                    }
                    add(
                        Param(
                            key = AnalyticsEvent.ParamKeys.STEP,
                            value = step
                        )
                    )
                    if (value != null) {
                        add(Param(key = AnalyticsEvent.ParamKeys.VALUE, value = value))
                    }
                    add(
                        Param(
                            key = AnalyticsEvent.ParamKeys.SELLER_TYPE,
                            value = account.analyticsAccountType()
                        )
                    )
                }
            ),
            where = setOf(AnalyticsAddons.FIREBASE)
        )
    }
}