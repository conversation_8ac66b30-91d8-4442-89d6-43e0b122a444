plugins {
    id("avito.android.feature")
    // TODO Remove after migrating vas to compose
    alias(libs.plugins.kotlin.parcelize)
}

android {
    namespace = "se.scmv.morocco.ad"
}

dependencies {
    implementation(project(":core:orion"))

    implementation(project(":feature:info-center"))
    implementation(project(":feature:authentication"))

    implementation(libs.bouquet)
    implementation(libs.zoomable)
    implementation(libs.com.google.material)
    implementation(libs.com.google.android.gms.ads)
    implementation(libs.com.google.android.exoplayer)
    // Firebase
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.firebase.dynamic.links)
}