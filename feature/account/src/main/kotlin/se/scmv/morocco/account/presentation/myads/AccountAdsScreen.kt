package se.scmv.morocco.account.presentation.myads

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import coil.compose.AsyncImage
import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.bgColor
import se.scmv.morocco.designsystem.utils.calculateRemainingSecondsToDelete
import se.scmv.morocco.designsystem.utils.getBoostStatus
import se.scmv.morocco.designsystem.utils.localizedNameRes
import se.scmv.morocco.designsystem.utils.parseDateToReadableFormat
import se.scmv.morocco.designsystem.utils.relativeValue
import se.scmv.morocco.designsystem.utils.textColor
import se.scmv.morocco.designsystem.utils.toFormattedString
import se.scmv.morocco.designsystem.utils.toRelativeTimeRes
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.CountStatus
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.MyAdLabel
import se.scmv.morocco.domain.models.MyAdVasPack
import se.scmv.morocco.domain.models.MyEditAdStatus
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading


@Composable
fun AdsRoute(
    account: Account.Connected,
    navigateToNewInsert: (adId: String?, adCategoryKey: String?, adType: String?, toImageStep: Boolean) -> Unit,
    navigateToVasActivity: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    navigateToAdView: (
        adListId: String,
        imageUrl: String?,
        title: String?,
        date: String?,
        imageCount: Int?,
        videoCount: Int?,
        videoUrl: String?,
        isStore: Boolean?,
        price: String?,
        oldPrice: String?,
        location: String?,
        category: String?,
        categoryIcon: String?,
        isUrgent: Boolean?,
        isHotDeal: Boolean?,
        discount: Int?
    ) -> Unit,
    navigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: AccountAdsViewModel = hiltViewModel()
) {
    val allowedAccess =
        (account as? Account.Connected.Shop)?.store?.allowedAccess ?: AllowedAccess.default
    val adPagingItems: LazyPagingItems<AccountAd> = viewModel.ads.collectAsLazyPagingItems()

    val selectedFilterInput = viewModel.selectedFilterInput.collectAsStateWithLifecycle().value
    val adCounts = viewModel.adCounts.collectAsStateWithLifecycle().value

    // State for showing dialogs
    val isDeactivationDialogVisible = remember { mutableStateOf(false) }
    val isDeletionDialogVisible = remember { mutableStateOf(false) }
    val showremainingTimePopup = remember { mutableStateOf(false) }
    val isPatchBottomSheetVisible = remember { mutableStateOf(false) }

    // State for selected reason
    val selectedReason = remember { mutableStateOf<String?>(null) }
    var adId by remember { mutableStateOf<String?>(null) }

    // State for urgent and promo options in the bottom sheet
    var isUrgent by remember { mutableStateOf(false) }
    var isHotDeal by remember { mutableStateOf(false) }
    var selectedDiscount by remember { mutableStateOf(0) }


    // States for Bottom Sheet and Selected Ad
    val isAdMoreInfoBottomSheetVisible = remember { mutableStateOf(false) }
    var selectedAd by remember { mutableStateOf<AccountAd?>(null) }
    val context = LocalContext.current

    // Hoisted states for multi select mode
    var isMultiSelectMode by remember { mutableStateOf(false) }
    val bulkSelectedAdsIds = remember { mutableStateListOf<Pair<String, LocalDateTime?>>() }

    // Functions to handle multi-select mode actions
    val onSelectAll: (Boolean) -> Unit = { shouldSelectAll ->
        if (shouldSelectAll) {
            bulkSelectedAdsIds.clear()
            adPagingItems.itemSnapshotList.items.forEach { ad ->
                ad.adId.let { bulkSelectedAdsIds.add(Pair(it, ad.lastStateTime)) }
            }
            viewModel.trackSelectAll()
        } else {
            bulkSelectedAdsIds.clear()
            isMultiSelectMode = false
        }
    }

    val onDeactivateOrDelete: () -> Unit = {
        when (selectedFilterInput.myAccountAdStatus) {
            MyAccountAdStatus.ACTIVE -> isDeactivationDialogVisible.value = true
            MyAccountAdStatus.DEACTIVATED -> {
                // todo
                if (bulkSelectedAdsIds.any { it.second?.calculateRemainingSecondsToDelete()!! > 0 }) {
                    showremainingTimePopup.value = true
                } else {
                    isDeletionDialogVisible.value = true
                }
            }

            else -> {}
        }
    }

    Scaffold(
        modifier = modifier,
        topBar = {
            AccountAdsTopAppBar(
                isMultiSelectMode = isMultiSelectMode,
                onNavigationIconClicked = {
                    if (isMultiSelectMode) {
                        isMultiSelectMode = false
                        bulkSelectedAdsIds.clear()
                    } else {
                        navigateBack()
                    }
                },
                bulkSelectedAdsIds = bulkSelectedAdsIds,
                onSelectAll = onSelectAll,
                onDeactivateOrDelete = onDeactivateOrDelete
            )
        },
        snackbarHost = { SnackBarHostForSnackBarController() },
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .padding(
                        end = MaterialTheme.dimens.large,
                    )
                    .fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                // Ads Filter
                if (!isMultiSelectMode) {
                    AccountAdsStatusFilter(
                        modifier = Modifier.fillMaxWidth(),
                        selectedStatus = selectedFilterInput.myAccountAdStatus,
                        adCounts = adCounts,
                        adsBoostedFilterAllowed = allowedAccess.adsBoostedFilterAllowed,
                        onStatusChanged = viewModel::onStatusChanged
                    )
                }
                when {
                    adPagingItems.isEmpty() -> {
                        // Display Empty State if no ads are found
                        ScreenEmptyState(
                            modifier = Modifier
                                .fillMaxSize()
                                .verticalScroll(rememberScrollState()),
                            title = R.string.ads_screen_empty_state_title,
                            description = R.string.ads_screen_empty_state_description,
                            actionText = R.string.ads_screen_empty_state_action,
                            onActionClicked = {
                                navigateToNewInsert(
                                    null, null, null, false
                                )
                            }
                        )
                    }

                    adPagingItems.isError() -> ScreenErrorState(
                        title = stringResource(R.string.common_oups),
                        description = adPagingItems.getErrorAsUiText().getValue(context),
                        actionText = stringResource(R.string.common_refresh),
                        onActionClicked = adPagingItems::refresh
                    )

                    else -> {
                        // Display Ads List (Paging items)
                        AdsList(
                            modifier = Modifier.fillMaxSize(),
                            pagingItems = adPagingItems,
                            allowedAccess = allowedAccess,
                            isMultiSelectMode = isMultiSelectMode,
                            selectedAds = bulkSelectedAdsIds,
                            setMultiSelectMode = { enabled ->
                                isMultiSelectMode =
                                    enabled == true && allowedAccess.adsBulkDeleteAllowed && (selectedFilterInput.myAccountAdStatus == MyAccountAdStatus.ACTIVE || selectedFilterInput.myAccountAdStatus == MyAccountAdStatus.DEACTIVATED)
                                if (enabled != true) {
                                    bulkSelectedAdsIds.clear() // Clear the selected ads when multi-select is disabled
                                }
                            }, // Setter to update state
                            updateSelectedAds = { adId, date, isSelected ->
                                if (allowedAccess.adsBulkDeleteAllowed && (selectedFilterInput.myAccountAdStatus == MyAccountAdStatus.ACTIVE || selectedFilterInput.myAccountAdStatus == MyAccountAdStatus.DEACTIVATED)) {
                                    if (isSelected) {
                                        bulkSelectedAdsIds.add(Pair(adId, date))
                                    } else {
                                        bulkSelectedAdsIds.remove(Pair(adId, date))
                                    }
                                    if (bulkSelectedAdsIds.isEmpty()) {
                                        isMultiSelectMode =
                                            false // Disable multi-select if no ads are selected
                                    }
                                }
                            },
                            isRefreshing = adPagingItems.isLoading(),
                            onRefresh = { adPagingItems.refresh() },
                            onDeactivateClick = { id ->
                                adId = id
                                isDeactivationDialogVisible.value =
                                    true // Show dialog on button click
                            },
                            onActivateClick = { id -> viewModel.onActivateAd(id) },
                            onDeleteClick = { id ->
                                adId = id
                                isDeletionDialogVisible.value =
                                    true // Show dialog on button click
                            },
                            onBoostClick = { adId, adCategoryKey, adType, application ->
                                navigateToVasActivity(adId, adCategoryKey, adType, application)
                            },
                            onAdMoreInfoClick = { ad ->
                                selectedAd = ad
                                isAdMoreInfoBottomSheetVisible.value = true
                            },
                            onEditClick = { adId, adCategoryKey, adType, toImageStep ->
                                navigateToNewInsert(adId, adCategoryKey, adType, toImageStep)
                                viewModel.trackAdEditClicked()
                            },
                            onPatchClick = { id, urgent, hotDeal, discount ->
                                adId = id
                                isUrgent = urgent
                                isHotDeal = hotDeal
                                if (discount != null) {
                                    selectedDiscount = discount
                                }
                                isPatchBottomSheetVisible.value = true
                            },
                            onAdDetailClick = { adListId, imageUrl, title, date, imageCount, videoCount, videoUrl, isStore, price, oldPrice, location, category, categoryIcon, isUrgent, isHotDeal, discount ->
                                navigateToAdView(
                                    adListId,
                                    imageUrl,
                                    title,
                                    date,
                                    imageCount,
                                    videoCount,
                                    videoUrl,
                                    isStore,
                                    price,
                                    oldPrice,
                                    location,
                                    category,
                                    categoryIcon,
                                    isUrgent,
                                    isHotDeal,
                                    discount
                                )
                                viewModel.trackAdDetailClicked()
                            },
                        )
                    }
                }
            }

        }

        // Show the deactivation reason dialog
        if (isDeactivationDialogVisible.value) {
            DeactivationReasonDialog(
                isDeactivateDialogVisible = true,
                onDismiss = {
                    isDeactivationDialogVisible.value = false
                    if (isMultiSelectMode) {
                        isMultiSelectMode = false
                        bulkSelectedAdsIds.clear()
                    }
                },
                onDeactivate = { reason ->
                    isDeactivationDialogVisible.value = false
                    if (isMultiSelectMode && bulkSelectedAdsIds.isNotEmpty()) {
                        viewModel.onDeactivateAd(bulkSelectedAdsIds.map { it.first }, reason)
                        isMultiSelectMode = false
                        bulkSelectedAdsIds.clear()

                    } else {
                        adId?.let { id ->
                            viewModel.onDeactivateAd(listOf(id), reason)
                        }
                    }
                },
                selectedReason = selectedReason.value,
                onReasonSelected = { reason -> selectedReason.value = reason }
            )
        }

        // Show the deletion reason dialog
        if (isDeletionDialogVisible.value) {
            DeletionReasonDialog(
                isDeletionDialogVisible = true,
                onDismiss = {
                    isDeletionDialogVisible.value = false
                    if (isMultiSelectMode) {
                        isMultiSelectMode = false
                        bulkSelectedAdsIds.clear()
                    }
                },
                onDelete = { reason ->
                    isDeletionDialogVisible.value = false
                    if (isMultiSelectMode && bulkSelectedAdsIds.isNotEmpty()) {
                        viewModel.onDeleteAd(bulkSelectedAdsIds.map { it.first }, reason)
                        isMultiSelectMode = false
                        bulkSelectedAdsIds.clear()
                    } else {
                        adId?.let { id ->
                            viewModel.onDeleteAd(listOf(id), reason)
                        }
                    }
                },
                selectedReason = selectedReason.value,
                onReasonSelected = { reason -> selectedReason.value = reason }
            )
        }

        // Show delete popup if required
        if (showremainingTimePopup.value) {
            val itemWithLargestRemainingTime: Pair<String, LocalDateTime?>? =
                bulkSelectedAdsIds.maxByOrNull { it.second.calculateRemainingSecondsToDelete() }
            ShowDeletePopup(
                remainingSeconds = itemWithLargestRemainingTime?.second?.calculateRemainingSecondsToDelete()
                    ?: -1
            ) { showremainingTimePopup.value = false }
        }

        // Ad More Info Bottom Sheet
        if (isAdMoreInfoBottomSheetVisible.value && selectedAd != null) {
            AdMoreInfoBottomSheet(
                ad = selectedAd!!,
                onDeactivateClick = { id ->
                    adId = id
                    isDeactivationDialogVisible.value = true // Show dialog on button click
                },
                onActivateClick = { id ->
                    viewModel.onActivateAd(id)
                },
                onDeleteClick = { id ->
                    adId = id
                    isDeletionDialogVisible.value = true // Show dialog on button click
                },
                onBoostClick = { adId, adCategoryKey, adType, application ->
                    navigateToVasActivity(adId, adCategoryKey, adType, application)
                },
                onEditClick = { adId, adCategoryKey, adType, toImageStep ->
                    navigateToNewInsert(adId, adCategoryKey, adType, toImageStep)

                },
                onAdDetailClick = { adListId ->
                },
                onPatchClick = { id, urgent, hotDeal, discount ->
                    adId = id
                    isUrgent = urgent
                    isHotDeal = hotDeal
                    if (discount != null) {
                        selectedDiscount = discount
                    }
                    isPatchBottomSheetVisible.value = true
                },
                onDismiss = {
                    isAdMoreInfoBottomSheetVisible.value = false
                }
            )
            viewModel.trackShowMore()
        }


        // Bottom Sheet Scaffold
        if (isPatchBottomSheetVisible.value) {
            UrgentPromoBottomSheet(
                isUrgent = isUrgent,
                onUrgentChanged = { isUrgent = it },
                selectedPromo = selectedDiscount,
                onPromoChanged = { promo: Int ->
                    selectedDiscount = promo
                    isHotDeal = promo != 0
                },
                onApplyChanges = {
                    adId?.let {
                        viewModel.onPatchAd(
                            it,
                            isUrgent,
                            isHotDeal,
                            discount = selectedDiscount
                        )
                    }
                    isPatchBottomSheetVisible.value = false
                },
                onDismiss = { isPatchBottomSheetVisible.value = false }
            )
        }
    }

    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.ACCOUNT_ADS,
        properties = setOf(
            Param(
                AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                account.analyticsAccountType()
            )
        )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountAdsTopAppBar(
    isMultiSelectMode: Boolean,
    onNavigationIconClicked: () -> Unit,
    bulkSelectedAdsIds: MutableList<Pair<String, LocalDateTime?>>,
    onSelectAll: (Boolean) -> Unit,
    onDeactivateOrDelete: () -> Unit
) {
    if (isMultiSelectMode) {
        AvTopAppBar(
            modifier = Modifier.shadow(1.dp),
            onNavigationIconClicked = onNavigationIconClicked,
            titleRes = R.string.selected_ads_number,
            titleArgs = arrayOf(bulkSelectedAdsIds.size), // Passing the count //
            actions = {
                var isAllSelected by remember { mutableStateOf(false) }

                // Select All button
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val checkBlank =
                        ImageVector.vectorResource(id = R.drawable.ic_check_box_outline_blank_24)
                    val check = ImageVector.vectorResource(id = R.drawable.ic_check_box_24)

                    Icon(
                        imageVector = if (isAllSelected) check else checkBlank,
                        contentDescription = stringResource(R.string.common_oups),
                        modifier = Modifier
                            .size(24.dp) // Adjust size to match your desired dimensions
                            .clickable {
                                val shouldSelectAll = !isAllSelected
                                onSelectAll(shouldSelectAll)
                                isAllSelected = shouldSelectAll
                            }
                    )

                    // Add the "tout" text next to the icon with no padding
                    Text(
                        text = stringResource(R.string.common_all),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }


                // Deactivate / delete
                IconButton(onClick = onDeactivateOrDelete) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = stringResource(R.string.common_oups)
                    )
                }
            }
        )
    } else {
        AvTopAppBar(
            modifier = Modifier.shadow(1.dp),
            onNavigationIconClicked = onNavigationIconClicked,
            titleRes = R.string.account_master_screen_redirection_my_ads,
        )
    }
}


@Composable
fun AccountAdsStatusFilter(
    modifier: Modifier = Modifier,
    selectedStatus: MyAccountAdStatus,
    adCounts: List<CountStatus>,
    adsBoostedFilterAllowed: Boolean,
    onStatusChanged: (MyAccountAdStatus) -> Unit
) {
    LazyRow(
        modifier = modifier
            .padding(vertical = 0.dp) // Removes any vertical padding
            .fillMaxWidth(), // Ensures it takes up the available width if necessary
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
    ) {
        items(MyAccountAdStatus.entries) { status: MyAccountAdStatus ->
            val selected = status == selectedStatus
            var count: Int? = adCounts.firstOrNull { it.status == status }?.count

            if (status == MyAccountAdStatus.ACTIVE) {
                count = (count
                    ?: 0) + (adCounts.firstOrNull { it.status == MyAccountAdStatus.PENDING_REVIEW }?.count
                    ?: 0)
            }
            if (status != MyAccountAdStatus.BOOSTED_ACTIVE_ADS || adsBoostedFilterAllowed)
                FilterChip(
                    modifier = Modifier
                        .testTag(status.name)
                        .padding(top = MaterialTheme.dimens.betweenSmallMedium)
                        .heightIn(min = 0.dp, max = 36.dp), // Restrict height ,
                    selected = selected,
                    label = {
                        if (count != null)
                            Text(
                                text = stringResource(status.localizedNameRes()) + " ($count)", // Display status name with count
                                maxLines = 1
                            )
                        else
                            Text(
                                text = stringResource(status.localizedNameRes()), // Display status name with count
                                maxLines = 1
                            )
                    },
                    leadingIcon = if (selected) {
                        {
                            Icon(
                                imageVector = Icons.Filled.Done,
                                contentDescription = "Selected ${status.name}",
                                modifier = Modifier.size(FilterChipDefaults.IconSize)
                            )
                        }
                    } else {
                        null
                    },
                    shape = MaterialTheme.shapes.large,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = MaterialTheme.colorScheme.primary,
                        selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
                        selectedLeadingIconColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    onClick = { onStatusChanged(status) }
                )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewAccountAdsStatusFilter() {
    val mockAdCounts = listOf(
        CountStatus(MyAccountAdStatus.ACTIVE, 12),
        CountStatus(MyAccountAdStatus.BOOSTED_ACTIVE_ADS, 8),
        CountStatus(MyAccountAdStatus.REFUSED, 5)
    )
    var selectedStatus by remember { mutableStateOf(MyAccountAdStatus.ACTIVE) }

    MaterialTheme {
        AccountAdsStatusFilter(
            selectedStatus = selectedStatus,
            adCounts = mockAdCounts,
            adsBoostedFilterAllowed = false,
            onStatusChanged = { newStatus ->
                selectedStatus = newStatus
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdsList(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<AccountAd>, // Replace with your Ad model
    allowedAccess: AllowedAccess,
    onDeactivateClick: (String) -> Unit,
    onActivateClick: (String) -> Unit,
    onDeleteClick: (String) -> Unit,
    onEditClick: (adId: String, adCategoryKey: String, adType: String, toImageStep: Boolean) -> Unit,
    onAdDetailClick: (
        adListId: String,
        imageUrl: String?,
        title: String?,
        date: String?,
        imageCount: Int?,
        videoCount: Int?,
        videoUrl: String?,
        isStore: Boolean?,
        price: String?,
        oldPrice: String?,
        location: String?,
        category: String?,
        categoryIcon: String?,
        isUrgent: Boolean?,
        isHotDeal: Boolean?,
        discount: Int?
    ) -> Unit,
    onBoostClick: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    onAdMoreInfoClick: (ad: AccountAd?) -> Unit,
    onPatchClick: (adId: String, urgent: Boolean, hotDeal: Boolean, discount: Int?) -> Unit,
    isMultiSelectMode: Boolean,
    setMultiSelectMode: (Boolean?) -> Unit,
    selectedAds: SnapshotStateList<Pair<String, LocalDateTime?>>,
    updateSelectedAds: (String, LocalDateTime, Boolean) -> Unit,
    isRefreshing: Boolean,
    onRefresh: () -> Unit
) {
    var showremainingTimePopup by remember { mutableStateOf(false) }
    var remainingSeconds by remember { mutableStateOf(0) }

    PullToRefreshBox(
        modifier = modifier,
        isRefreshing = isRefreshing,
        state = rememberPullToRefreshState(),
        onRefresh = onRefresh
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.betweenSmallMedium),
            contentPadding = PaddingValues(bottom = MaterialTheme.dimens.extraExtraBig)
        ) {
            items(pagingItems.itemCount) { index ->
                val ad = pagingItems[index]
                ad?.let {
                    AdItem(
                        ad = it,
                        isSelected = selectedAds.any { item -> item.first == it.adId },
                        allowedAccess = allowedAccess,
                        isMultiSelectMode = isMultiSelectMode,
                        onDeactivateClick = { onDeactivateClick(it.adId) },
                        onActivateClick = { onActivateClick(it.adId) },
                        onMoreInfoClick = {
                            if (isMultiSelectMode) {
                                if (selectedAds.any { item -> item.first == it.adId }) {
                                    it.lastStateTime?.let { it1 ->
                                        updateSelectedAds(
                                            it.adId,
                                            it1,
                                            false
                                        )
                                    }
                                    if (selectedAds.isEmpty()) setMultiSelectMode(false)
                                } else {
                                    it.lastStateTime?.let { it1 ->
                                        updateSelectedAds(
                                            it.adId,
                                            it1,
                                            true
                                        )
                                    }
                                }
                            } else {
                                onAdMoreInfoClick(it)
                            }
                        },
                        onBoostClick = { application ->
                            onBoostClick(
                                it.adId,
                                it.category.id.toString(),
                                it.myAdType.key?.name.toString(),
                                application
                            )
                        },
                        onDeleteClick = {
                            val _remainingSeconds =
                                it.lastStateTime?.calculateRemainingSecondsToDelete()
                            if (_remainingSeconds != null) {
                                if (_remainingSeconds > 0) {
                                    remainingSeconds = _remainingSeconds
                                    showremainingTimePopup = true
                                } else {
                                    onDeleteClick(it.adId)
                                }
                            }
                        },
                        onEditClick = { toImageStep ->
                            onEditClick(
                                it.adId,
                                it.category.id.toString(),
                                it.myAdType.key?.name.toString(),
                                toImageStep
                            )
                        },
                        onPatchClick = { adId, urgent, hotDeal, discount ->
                            onPatchClick(adId, urgent, hotDeal, discount)
                        },
                        onLongPress = {
                            setMultiSelectMode(true)
                            it.lastStateTime?.let { it1 -> updateSelectedAds(it.adId, it1, true) }
                        },
                        onAdDetailClick = {
                            it.listId?.let { listId ->

                                onAdDetailClick(
                                    listId,
                                    it.imageUrl,
                                    it.title,
                                    it.lastStateTime?.toFormattedString(),
                                    it.mediaCount,
                                    null,
                                    null,
                                    null,
                                    it.priceWithCurrency,
                                    null,
                                    it.location.name,
                                    it.category.name,
                                    it.category.id,
                                    it.labels.contains(MyAdLabel.URGENT),
                                    it.labels.contains(MyAdLabel.HOTDEAL),
                                    it.discount
                                )


                            }


                        }
                    )
                }
            }
        }
    }

    // Show delete popup if required
    if (showremainingTimePopup) {
        ShowDeletePopup(remainingSeconds = remainingSeconds) { showremainingTimePopup = false }
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AdItem(
    ad: AccountAd,
    isSelected: Boolean,
    allowedAccess: AllowedAccess,
    onDeactivateClick: () -> Unit,
    onActivateClick: () -> Unit,
    onEditClick: (toImageStep: Boolean) -> Unit,
    onAdDetailClick: () -> Unit,
    onBoostClick: (application: VasPacksApplication) -> Unit,
    onMoreInfoClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onLongPress: () -> Unit,
    onPatchClick: (adId: String, urgent: Boolean, hotDeal: Boolean, discount: Int?) -> Unit,
    isMultiSelectMode: Boolean
) {

    val transparentBlue = Color(0xFF0000FF).copy(alpha = 0.01f) // Transparent blue
    val defaultBackground = MaterialTheme.colorScheme.surface

    val backgroundColor = if (isSelected) {
        transparentBlue
    } else {
        defaultBackground
    }


    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                top = 0.dp,
                end = MaterialTheme.dimens.small,
                start = MaterialTheme.dimens.small,
                bottom = MaterialTheme.dimens.small
            )
            .combinedClickable(
                onClick = {
                    onMoreInfoClick()
                },
                onLongClick = {
                    onLongPress()

                }
            ),
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline
        ),
        shape = MaterialTheme.shapes.small,
        elevation = CardDefaults.cardElevation(defaultElevation = MaterialTheme.dimens.none) // No shadow
    ) {
        Column(
            modifier = Modifier
                .background(backgroundColor)
                .padding(MaterialTheme.dimens.medium)
        ) {
            AdHeader(
                ad,
                allowedAccess,
                isMultiSelectMode,
                onDeactivateClick,
                onActivateClick,
                onEditClick = onEditClick,
                onAdDetailClick = onAdDetailClick,
                onBoostClick = { application -> onBoostClick(application) },
                onDeleteClick = onDeleteClick,
                onPatchClick = onPatchClick,
                onMoreInfoClick = onMoreInfoClick
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
            AdStatus(ad)
            ad.refusalReason?.let {
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                AdRefusalReason(it)
            }
            ad.lastAppliedVasPack?.let { lastAppliedVasPack: MyAdVasPack ->
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
                VASInfo(lastAppliedVasPack, ad.vasPackages?.count)
            }
            if (ad.myAdStatus != MyAccountAdStatus.PENDING_REVIEW && ad.myAdStatus != MyAccountAdStatus.DEACTIVATED)
                ad.performance?.let {
                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
                    AdPerformance(ad)
                }
            Spacer(modifier = Modifier.height(9.dp))
            if (!isMultiSelectMode)
                ActionButtons(
                    onEditClick = {
                        onEditClick(false)
                    },
                    onBoostClick = { application ->
                        onBoostClick(application)
                    },
                    onActivateClick = {
                        onActivateClick()
                    },
                    ad
                )
        }
    }
}

@Composable
fun AdHeader(
    ad: AccountAd,
    allowedAccess: AllowedAccess,
    isMultiSelectMode: Boolean,
    onDeactivateClick: () -> Unit,
    onActivateClick: () -> Unit,
    onEditClick: (toImageStep: Boolean) -> Unit,
    onAdDetailClick: () -> Unit,
    onBoostClick: (application: VasPacksApplication) -> Unit,
    onDeleteClick: () -> Unit,
    onPatchClick: (adId: String, urgent: Boolean, hotDeal: Boolean, discount: Int?) -> Unit,
    onMoreInfoClick: () -> Unit
) {
    // State for DropdownMenu
    val menuExpanded = remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.dimens.none)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.dimens.none),
            verticalAlignment = Alignment.Top
        ) {
            // Ad Image or Gray Background with Add Image Icon and Text
            Box(
                modifier = Modifier
                    .size(130.dp)
                    .clip(RoundedCornerShape(MaterialTheme.dimens.medium))
            ) {
                if (ad.imageUrl.isNullOrEmpty()) {
                    if (ad.myAdStatus == MyAccountAdStatus.ACTIVE || ad.myAdStatus == MyAccountAdStatus.REFUSED) {
                        // Use Column to stack the icon and text vertically
                        Column(
                            modifier = Modifier
                                .matchParentSize()
                                .background(Color(0xFFf6f6f6))
                                .clickable {
                                    onEditClick(true)
                                }
                                .align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_upload),
                                contentDescription = "Add Image",
                                modifier = Modifier.size(MaterialTheme.dimens.mediumBig),
                                tint = Color.Gray // Customize the tint color as needed
                            )
                            Text(
                                text = stringResource(R.string.add_image),
                                color = Color.Gray,
                                fontSize = 12.sp,
                                modifier = Modifier.padding(top = MaterialTheme.dimens.none) // Adjust spacing between icon and text
                            )
                        }
                    } else {
                        // Use Column to stack the icon and text vertically
                        Column(
                            modifier = Modifier
                                .matchParentSize()
                                .background(Color(0xFFf6f6f6))
                                .align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_upload),
                                contentDescription = "No Image",
                                modifier = Modifier.size(MaterialTheme.dimens.mediumBig),
                                tint = Color.Gray // Customize the tint color as needed
                            )
                        }
                    }

                } else {
                    // Display the loaded image
                    AsyncImage(
                        model = ad.imageUrl,
                        contentDescription = stringResource(R.string.add_image),
                        modifier = Modifier
                            .matchParentSize()
                            .clip(RoundedCornerShape(MaterialTheme.dimens.medium))
                            .clickable {
                                // When the card is clicked, show the bottom sheet
                                onMoreInfoClick()
                            },
                        contentScale = ContentScale.Crop
                    )
                }
            }

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

            // Ad Details
            AdDetailsColumn(ad)
        }

        if (!isMultiSelectMode)
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(MaterialTheme.dimens.none)
                    .size(MaterialTheme.dimens.bigger)
            ) {
                if (ad.myAdStatus != MyAccountAdStatus.PENDING_REVIEW && ad.myAdStatus != MyAccountAdStatus.DELETED) {
                    IconButton(
                        onClick = { menuExpanded.value = !menuExpanded.value },
                        modifier = Modifier
                            .align(Alignment.TopEnd) // Align to the top-end
                            .padding(MaterialTheme.dimens.none) // No padding
                    ) {
                        Icon(
                            imageVector = Icons.Filled.MoreVert,
                            contentDescription = "More Options"
                        )
                    }
                }

                // DropdownMenu for "Edit" and "Boost"
                DropdownMenu(
                    expanded = menuExpanded.value,
                    onDismissRequest = { menuExpanded.value = false },
                    offset = DpOffset(x = (-8).dp, y = MaterialTheme.dimens.none),
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.background) // Set background color to white

                ) {

                    val itemModifier = Modifier
                        .fillMaxWidth() // Ensure each item takes full width
                        .padding(vertical = MaterialTheme.dimens.none) // Reduce vertical padding between items

                    when (ad.myAdStatus) {
                        MyAccountAdStatus.REFUSED -> {
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onEditClick(false)
                                },
                                text = { Text(stringResource(R.string.edit_Ad)) },
                                modifier = itemModifier
                            )
                        }

                        MyAccountAdStatus.DEACTIVATED -> {
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onActivateClick()
                                },
                                text = { Text(stringResource(R.string.reactivate)) },
                                modifier = itemModifier
                            )

                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onDeleteClick()
                                },
                                text = { Text(stringResource(R.string.delete)) },
                                modifier = itemModifier
                            )
                        }

                        MyAccountAdStatus.DELETED, MyAccountAdStatus.PENDING_REVIEW -> {

                        }

                        MyAccountAdStatus.PENDING_PAYMENT -> {
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onBoostClick(VasPacksApplication.AD_INSERT)
                                },
                                text = { Text(stringResource(R.string.common_pay)) },
                                modifier = itemModifier
                            )
                        }

                        else -> {

                            //todo to implement allowedAccess handling logc
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onAdDetailClick()
                                },
                                text = { Text(stringResource(R.string.see_on_avito)) },
                                modifier = itemModifier
                            )

                            if (allowedAccess.adHotdealAllowed && allowedAccess.adUrgentAllowed) {
                                DropdownMenuItem(
                                    onClick = {
                                        menuExpanded.value = false
                                        onPatchClick(
                                            ad.adId,
                                            ad.labels.contains(MyAdLabel.URGENT),
                                            ad.labels.contains(MyAdLabel.HOTDEAL),
                                            ad.discount
                                        )
                                    },
                                    text = { Text(stringResource(R.string.hot_deal_urgent)) },
                                    modifier = itemModifier
                                )
                            }

                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onEditClick(false)
                                },
                                text = { Text(stringResource(R.string.edit_Ad)) },
                                modifier = itemModifier
                            )
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onBoostClick(VasPacksApplication.AD_LISTING)
                                },
                                text = { Text(stringResource(R.string.boost_Ad)) },
                                modifier = itemModifier
                            )
                            DropdownMenuItem(
                                onClick = {
                                    menuExpanded.value = false
                                    onDeactivateClick()
                                },
                                text = { Text(stringResource(R.string.deactivate)) },
                                modifier = itemModifier
                            )
                        }

                    }
                }
            }
    }
}


@Composable
fun AdDetailsColumn(ad: AccountAd) {
    Column(
        modifier = Modifier.padding(top = MaterialTheme.dimens.medium),
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = ad.priceWithCurrency ?: stringResource(R.string.price_not_specified),
            style = MaterialTheme.typography.titleMedium.copy(
                fontSize = 16.sp // Override only the text size
            ),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
        Text(
            text = ad.title,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.none))
        Text(
            text = ad.category.name.toString(),
            style = MaterialTheme.typography.bodySmall
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
        AdLocationAndTime(ad)
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
        AdLabels(ad)

    }
}


@Composable
fun VASInfo(lastAppliedVasPack: MyAdVasPack, count: Int?) {
    // Adding horizontal padding
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.none), // Horizontal padding
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Pack Information
        Column(modifier = Modifier.weight(1f)) {
            if (count is Int && count > 1)
                Text(
                    text = stringResource(id = R.string.pack_count, count - 1),
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.primary
                )
            else if (count != null)
                Text(
                    text = stringResource(id = R.string.current_pack),
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF2E6BFF)
                )
            else
                Text(
                    text = stringResource(id = R.string.pack),
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF2E6BFF)
                )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.none)) // Space between label and value
            Text(
                text = lastAppliedVasPack.name,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        Spacer(modifier = Modifier.width(MaterialTheme.dimens.default)) // Space between columns

        // Expiry Information
        Column(modifier = Modifier.weight(1f)) {
            if (lastAppliedVasPack.startDate?.getBoostStatus(lastAppliedVasPack.endDate) == 0) {
                Text(
                    text = stringResource(R.string.pack_starts),
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF2E6BFF)
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.none)) // Space between label and value
                Text(
                    text = lastAppliedVasPack.startDate?.parseDateToReadableFormat() ?: "N/A",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else if (lastAppliedVasPack.startDate?.getBoostStatus(lastAppliedVasPack.endDate) == 1) {
                Text(
                    text = stringResource(R.string.pack_expires),
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF2E6BFF)
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.none)) // Space between label and value
                Text(
                    text = lastAppliedVasPack.endDate?.parseDateToReadableFormat() ?: "N/A",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Text(
                    text = stringResource(R.string.pack_expired),
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF2E6BFF)
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.none)) // Space between label and value
                Text(
                    text = lastAppliedVasPack.endDate?.parseDateToReadableFormat() ?: "N/A",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}


@Composable
fun AdLabels(ad: AccountAd) {
    if (ad.labels.contains(MyAdLabel.URGENT) || ad.labels.contains(MyAdLabel.HOTDEAL)) {
        Row(horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)) {
            if (ad.labels.contains(MyAdLabel.URGENT)) {
                LabelChip(
                    text = stringResource(R.string.urgent),
                    backgroundColor = Color(0xFF3A6FF6),
                    icon = painterResource(id = R.drawable.ic_urgent)
                )
            }
            if (ad.labels.contains(MyAdLabel.HOTDEAL)) {
                LabelChip(
                    text = "-${ad.discount}% " + stringResource(R.string.hot_deal),
                    backgroundColor = Color(0xFFec5a5f),
                    icon = painterResource(id = R.drawable.ic_promotion)
                )
            }
        }
    }
}

@Composable
fun LabelChip(
    text: String,
    backgroundColor: Color,
    icon: Painter? = null, // Optional icon
    iconSize: Dp = MaterialTheme.dimens.default
) {
    Row(
        modifier = Modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(MaterialTheme.dimens.small)
            )
            .padding(
                horizontal = MaterialTheme.dimens.medium,
                vertical = MaterialTheme.dimens.small
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (icon != null) {
            Icon(
                painter = icon,
                contentDescription = null, // Or provide a description for accessibility
                modifier = Modifier.size(iconSize),
                tint = Color.White
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
        }
        Text(
            text = text,
            color = Color.White,
            style = MaterialTheme.typography.labelSmall
        )
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AdLocationAndTime(ad: AccountAd) {
    FlowRow(
    ) {
        FlowRow(
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_time),
                contentDescription = "Time Icon",
                modifier = Modifier.size(MaterialTheme.dimens.default),
                tint = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = (ad.publishedAt?.toRelativeTimeRes()
                    ?: ad.lastStateTime?.toRelativeTimeRes())?.let {
                    (ad.publishedAt?.relativeValue()
                        ?: ad.lastStateTime?.relativeValue())?.let { it1 ->
                        stringResource(
                            id = it,
                            it1

                        )
                    }
                }.orEmpty(),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
        // Location Row (handling long text gracefully)
        FlowRow(
        ) {
            Icon(
                painter = painterResource(id = R.drawable.outline_location_on_24),
                contentDescription = "Location Icon",
                modifier = Modifier.size(MaterialTheme.dimens.default),
                tint = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = ad.location.name,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1, // Keep it on one line
                overflow = TextOverflow.Ellipsis // Truncate with ellipsis if overflow
            )
        }
    }
}

@Composable
fun AdStatus(ad: AccountAd) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = MaterialTheme.dimens.none),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        ad.myAdStatus?.let { status: MyAccountAdStatus ->
            StatusChip(
                text = stringResource(status.localizedNameRes()),
                backgroundColor = status.bgColor(),
                textColor = status.textColor()
            )
        }
        ad.myEditAdStatus?.let { editStatus: MyEditAdStatus ->
            StatusChip(
                text = stringResource(editStatus.localizedNameRes()),
                backgroundColor = editStatus.bgColor(),
                textColor = editStatus.textColor()
            )
        }
    }
}

@Composable
fun AdRefusalReason(reason: String) {
    Text(
        modifier = Modifier
            .fillMaxWidth() // Make the refusal reason take the full width
            .background(color = MyAccountAdStatus.REFUSED.bgColor(), shape = RectangleShape)
            .padding(MaterialTheme.dimens.medium),
        text = reason,
        style = MaterialTheme.typography.labelLarge,
        color = MyAccountAdStatus.REFUSED.textColor()
    )
}


@Composable
fun StatusChip(text: String, backgroundColor: Color, textColor: Color) {
    Text(
        modifier = Modifier
            .background(color = backgroundColor, shape = CircleShape)
            .padding(
                horizontal = MaterialTheme.dimens.medium,
                vertical = MaterialTheme.dimens.small
            ),
        text = text,
        style = MaterialTheme.typography.labelSmall,
        color = textColor
    )
}

@Composable
fun AdPerformance(ad: AccountAd) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        if (ad.performance?.phoneViews != null)
            PerformanceItem(
                R.drawable.ic_calls_circle,
                stringResource(R.string.calls),
                ad.performance?.phoneViews.toString()
            )
        if (ad.performance?.views != null)
            PerformanceItem(
                R.drawable.ic_views_circle,
                stringResource(R.string.visits),
                ad.performance?.views.toString()
            )
        if (ad.performance?.conversations != null)
            PerformanceItem(
                R.drawable.ic_messages_circle,
                stringResource(R.string.messages),
                ad.performance?.conversations.toString()
            )
    }
}

@Composable
fun ActionButtons(
    onEditClick: () -> Unit,
    onBoostClick: (application: VasPacksApplication) -> Unit,
    onActivateClick: () -> Unit,
    ad: AccountAd
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.none)
    ) {
        when (ad.myAdStatus) {
            MyAccountAdStatus.REFUSED -> {
                Button(
                    onClick = onEditClick,
                    modifier = Modifier.fillMaxWidth(),  // Full width when only one button is visible
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3A6FF6)),
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular), // Rounded corners
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit, // Replace with your desired icon
                        contentDescription = "Edit",
                        modifier = Modifier.size(MaterialTheme.dimens.big) // Adjust size as needed
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium)) // Space between icon and text
                    Text(
                        text = stringResource(R.string.edit_Ad),
                        color = Color.White // Adjust text color as needed
                    )
                }
            }

            MyAccountAdStatus.DEACTIVATED -> {
                Button(
                    onClick = onActivateClick,
                    modifier = Modifier.fillMaxWidth(),  // Full width when only one button is visible
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3A6FF6)),
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular), // Rounded corners
                ) {
                    Text(
                        text = stringResource(R.string.reactivate),
                        color = Color.White
                    )
                }
            }

            MyAccountAdStatus.PENDING_PAYMENT -> {
                Button(
                    onClick = { onBoostClick(VasPacksApplication.AD_INSERT) },
                    modifier = Modifier.fillMaxWidth(),  // Full width when only one button is visible
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3A6FF6)),
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular), // Rounded corners

                ) {
                    Text(
                        text = stringResource(R.string.common_pay), color = Color.White
                    )
                }
            }

            MyAccountAdStatus.DELETED, MyAccountAdStatus.PENDING_REVIEW -> {
                // No buttons for DELETED status
            }

            else -> {

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.dimens.none), // Small padding around the Row
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium) // Spacing between buttons
                ) {
                    // Outlined Button (Left Button)
                    Button(
                        onClick = onEditClick,
                        modifier = Modifier
                            .weight(1f) // Equal width
                            .fillMaxWidth(), // Ensures button fills the weight allocation
                        colors = ButtonDefaults.outlinedButtonColors(containerColor = Color.Transparent), // No background
                        border = BorderStroke(
                            1.dp,
                            Color(0xFFFC942D)
                        ), // Border stroke directly
                        shape = RoundedCornerShape(MaterialTheme.dimens.regular), // Rounded corners
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.dimens.default,
                            vertical = MaterialTheme.dimens.regular
                        ) // Padding
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Edit, // Icon for Modifier
                            contentDescription = "Edit Icon",
                            tint = Color(0xFFFC942D),
                            modifier = Modifier.size(MaterialTheme.dimens.default) // Consistent icon size
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                        Text(
                            text = stringResource(R.string.edit_Ad),
                            color = Color(0xFFFC942D),
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1, // Restrict to one line
                            overflow = TextOverflow.Ellipsis // Add ellipsis for text overflow

                        )
                    }

                    // Filled Button (Right Button)
                    Button(
                        onClick = { onBoostClick(VasPacksApplication.AD_LISTING) },
                        modifier = Modifier
                            .weight(1f) // Equal width
                            .fillMaxWidth(), // Ensures button fills the weight allocation
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFC942D)), // Background color
                        shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.dimens.default,
                            vertical = MaterialTheme.dimens.regular
                        ) // Padding
                    ) {

                        Icon(
                            painter = painterResource(id = R.drawable.outline_rocket_launch_16),
                            contentDescription = "Time Icon",
                            modifier = Modifier.size(MaterialTheme.dimens.default),
                        )


                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                        Text(
                            text = stringResource(R.string.boost_Ad),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1, // Restrict to one line
                            overflow = TextOverflow.Ellipsis // Add ellipsis for text overflow
                        )
                    }
                }

            }
        }
    }
}

@Composable
fun PerformanceItem(iconId: Int, label: String, count: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(horizontal = MaterialTheme.dimens.none) // Add padding if needed
    ) {
        // Load the vector drawable as a Painter
        val iconPainter = painterResource(id = iconId)

        // Display the icon
        Image(
            painter = iconPainter,
            contentDescription = null, // Optional: Add a description for accessibility
            modifier = Modifier.size(MaterialTheme.dimens.bigger) // Adjust size as needed
        )

        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium)) // Spacer to separate the icon and text

        // Add a Column to display the label and count vertically
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Add the label text
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall, // Style for the label
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f) // Adjust color if needed
            )

            // Add the count text
            Text(
                text = count,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}




