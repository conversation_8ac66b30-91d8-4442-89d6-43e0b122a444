package se.scmv.morocco.account.presentation.bookmarks.ads

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemKey
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsOneTimeEvents.DeleteAdSuccess
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsOneTimeEvents.ShowHideProgress
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.createMockListingAds
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading

@Composable
fun AccountBookmarkedAdsRoute(
    modifier: Modifier = Modifier,
    viewModel: AccountBookmarkedAdsViewModel = hiltViewModel(),
    // TODO We should move this to the app state once all the app in compose !
    navigateToHome: () -> Unit,
    onAdRemovedFromFavorites: (String) -> Unit,
    onAdClicked: (String) -> Unit,
) {
    val adPagingItems = viewModel.ads.collectAsLazyPagingItems()
    var adToUnBookmark by remember { mutableStateOf<ListingAd.Published?>(null) }

    AccountBookmarkedAdsScreen(
        modifier = modifier.fillMaxSize(),
        pagingItems = adPagingItems,
        onEmptyStateActionClicked = navigateToHome,
        onUnBookmarkAd = viewModel::onUnBookmarkAd,
        onAdClicked = onAdClicked
    )
    if (adToUnBookmark != null) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.bookmarked_ads_screen_delete_ad),
            description = stringResource(R.string.bookmarked_ads_screen_delete_ad_description),
            onConfirm = {
                adToUnBookmark?.let { ad ->
                    viewModel.onUnBookmarkAd(ad)
                    adToUnBookmark = null
                }
            },
            onCancel = { adToUnBookmark = null },
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(R.string.bookmarked_ads_screen_delete_ad_processing))
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                is ShowHideProgress -> showProgressBar = it.isLoading
                is DeleteAdSuccess -> onAdRemovedFromFavorites(it.adId)
            }
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.BOOKMARKED_ADS)
}

@Composable
private fun AccountBookmarkedAdsScreen(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<ListingAd.Published>,
    onEmptyStateActionClicked: () -> Unit,
    onUnBookmarkAd: (ListingAd.Published) -> Unit,
    onAdClicked: (String) -> Unit,
) {
    val context = LocalContext.current
    when {
        pagingItems.isEmpty() -> ScreenEmptyState(
            modifier = modifier,
            image = R.drawable.img_empty_bookmarked_ads,
            title = R.string.bookmarked_ads_screen_empty_state_title,
            description = R.string.bookmarked_ads_screen_empty_state_description,
            actionText = R.string.common_search,
            onActionClicked = onEmptyStateActionClicked
        )

        pagingItems.isError() -> ScreenErrorState(
            modifier = modifier,
            title = stringResource(R.string.common_oups),
            description = pagingItems.getErrorAsUiText().getValue(context),
            actionText = stringResource(R.string.common_refresh),
            onActionClicked = pagingItems::refresh
        )

        else -> AccountBookmarkedAdsList(
            modifier = modifier,
            pagingItems = pagingItems,
            onUnBookmarkAd = onUnBookmarkAd,
            onAdClicked = onAdClicked
        )
    }
}

@Preview
@Composable
private fun AccountBookmarkedSearchesScreenPreview() {
    AvitoTheme {
        Surface {
            // Uncomment this code to preview empty state
            // val ads = PagingData.emptyLazyPagingItems<ListingAd.Published>()
            val ads = flowOf(
                PagingData.from(createMockListingAds(20))
            ).collectAsLazyPagingItems()
            // Uncomment this code to preview error state
            //val ads = PagingData.errorLazyPagingItems<ListingAd.Published>()
            AccountBookmarkedAdsScreen(
                pagingItems = ads,
                onEmptyStateActionClicked = {},
                onUnBookmarkAd = {},
                onAdClicked = {}
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AccountBookmarkedAdsList(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<ListingAd.Published>,
    onUnBookmarkAd: (ListingAd.Published) -> Unit,
    onAdClicked: (String) -> Unit,
) {
    PullToRefreshBox(
        modifier = modifier,
        isRefreshing = pagingItems.isLoading(),
        state = rememberPullToRefreshState(),
        onRefresh = { pagingItems.refresh() }
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
            contentPadding = PaddingValues(bottom = MaterialTheme.dimens.extraExtraBig)
        ) {
            items(
                count = pagingItems.itemCount,
                key = pagingItems.itemKey { it.listId },
            ) {
                pagingItems[it]?.let { ad ->
                    ListingCard(
                        listingAd = ad,
                        isInFavorites = true,
                        onFavoriteClick = { _, _ -> onUnBookmarkAd(ad) },
                        onAdClick = onAdClicked
                    )
                }
            }
        }
    }
}