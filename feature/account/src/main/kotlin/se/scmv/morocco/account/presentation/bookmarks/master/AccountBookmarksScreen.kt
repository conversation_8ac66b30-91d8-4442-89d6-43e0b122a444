package se.scmv.morocco.account.presentation.bookmarks.master

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import kotlinx.coroutines.launch
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsRoute
import se.scmv.morocco.account.presentation.bookmarks.searches.AccountBookmarkedSearchesRoute
import se.scmv.morocco.designsystem.components.AvTabs
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.BookmarkedSearch

@Composable
fun AccountBookmarksRoute(
    navigateToHome: () -> Unit,
    applySearch: (BookmarkedSearch) -> Unit,
    notifyAdRemovedFromFavorites: (String) -> Unit,
    navigateToAdDetails: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val pagerState = rememberPagerState(
        pageCount = { AccountBookmarksPages.entries.size }
    )
    val coroutineScope = rememberCoroutineScope()
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(top = MaterialTheme.dimens.medium)
    ) {
        AvTabs(
            modifier = Modifier.fillMaxWidth(),
            tabs = accountBookmarksTabs,
            selectedIndex = pagerState.currentPage,
        ) { tabIndex ->
            coroutineScope.launch { pagerState.animateScrollToPage(tabIndex) }
        }
        HorizontalPager(
            modifier = Modifier
                .padding(top = MaterialTheme.dimens.medium)
                .fillMaxSize(),
            state = pagerState
        ) {
            when (it) {
                AccountBookmarksPages.ADS.ordinal -> AccountBookmarkedAdsRoute(
                    navigateToHome = navigateToHome,
                    onAdRemovedFromFavorites = notifyAdRemovedFromFavorites,
                    onAdClicked = navigateToAdDetails
                )

                AccountBookmarksPages.SEARCHES.ordinal -> AccountBookmarkedSearchesRoute(
                    navigateToHome = navigateToHome,
                    applySearch = applySearch
                )
            }
        }
    }
}