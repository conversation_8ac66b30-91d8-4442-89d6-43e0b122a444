package se.scmv.morocco.account.presentation.myads


import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.AccountAdDeactivationReason
import se.scmv.morocco.domain.models.AccountDeactivationReasonInput
import se.scmv.morocco.domain.models.CountStatus
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.MyAccountAdsFilterInput
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountAdsRepository
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import java.util.Locale
import javax.inject.Inject


@HiltViewModel
class AccountAdsViewModel @Inject constructor(
    private val adsListingRepository: AccountAdsRepository,
    private val analyticsHelper: AnalyticsHelper
) : ViewModel() {

    private val _oneTimeEvents = MutableSharedFlow<AccountAdsDeactivateStatus>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private val _selectedFilterInput = MutableStateFlow(
        MyAccountAdsFilterInput(myAccountAdStatus = MyAccountAdStatus.ACTIVE, boosted = null)
    )
    val selectedFilterInput = _selectedFilterInput.asStateFlow()

    // No replay needed for one-time events.
    private val refreshTrigger = MutableSharedFlow<Unit>()

    @OptIn(ExperimentalCoroutinesApi::class)
    val ads: Flow<PagingData<AccountAd>> = refreshTrigger
        // Emit initially to load data on first launch
        .onStart { emit(Unit) }
        .flatMapLatest {
            _selectedFilterInput.flatMapLatest {
                adsListingRepository.getMyAds(it).cachedIn(viewModelScope)
            }
        }

    val adCounts: StateFlow<List<CountStatus>> = refreshTrigger
        .onStart { emit(Unit) }
        .map {
            when (val result = adsListingRepository.getAdsCount()) {
                is Resource.Success -> result.data

                is Resource.Failure -> emptyList()
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    fun onStatusChanged(myAccountAdStatus: MyAccountAdStatus) {
        viewModelScope.launch {
            _selectedFilterInput.update { MyAccountAdsFilterInput(myAccountAdStatus) }
            trackSelectStatus(myAccountAdStatus)
        }
    }

    fun onDeactivateAd(
        ids: List<String>, // Accepting a list of IDs for bulk deactivation
        reason: AccountDeactivationReasonInput
    ) {
        viewModelScope.launch {
            val result = adsListingRepository.bulkDeactivateAds(
                ids,
                reason
            )
            when (result) {
                is Resource.Success -> {
                    refreshTrigger.emit(Unit)
                    renderSuccess(UiText.FromRes(R.string.ad_deactivate_success))
                    trackDeactivateAds(reason.reason)
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun onActivateAd(
        id: String
    ) {
        viewModelScope.launch {
            val result = adsListingRepository.activateAd(id)
            when (result) {
                is Resource.Success -> {
                    refreshTrigger.emit(Unit)
                    renderSuccess(UiText.FromRes(R.string.ad_activate_success))
                    trackReactivateAds()
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }


    fun onDeleteAd(
        ids: List<String>,
        reason: AccountDeactivationReasonInput
    ) {
        viewModelScope.launch {
            val result = adsListingRepository.bulkDeleteAds(
                ids,
                reason
            )
            when (result) {
                is Resource.Success -> {
                    refreshTrigger.emit(Unit)
                    renderSuccess(UiText.FromRes(R.string.ad_delete_success))
                    trackDeleteAds(reason.reason)
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun onPatchAd(
        adId: String,
        isUrgent: Boolean,
        isHotDeal: Boolean,
        discount: Int
    ) {
        viewModelScope.launch {
            val result = adsListingRepository.patchAd(
                adId = adId,
                isUrgent = isUrgent,
                isHotDeal = isHotDeal,
                discount = discount
            )
            when (result) {
                is Resource.Success -> {
                    refreshTrigger.emit(Unit)
                    renderSuccess(UiText.FromRes(R.string.ad_edit_success))
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    // Refresh logic
    fun refresh() {
        viewModelScope.launch {
            refreshTrigger.emit(Unit)
        }
    }

    private fun trackDeactivateAds(accountAdDeactivationReason: AccountAdDeactivationReason) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.DELETE_SELECTED
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VALUE,
                        value = accountAdDeactivationReason.name.lowercase(Locale.ROOT)
                    )
                )
            )
        )
    }


    private fun trackDeleteAds(accountAdDeactivationReason: AccountAdDeactivationReason) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.FINAL_DELETE
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VALUE,
                        value = accountAdDeactivationReason.name.lowercase(Locale.ROOT)
                    )
                )
            )
        )
    }

    private fun trackReactivateAds() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.REACTIVATE
                    )
                )
            )
        )
    }

    fun trackShowMore() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.SHOW_MORE
                    )
                )
            )
        )
    }
    fun trackSelectAll() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.SELECT_ALL
                    )
                )
            )
        )
    }

    fun trackAdDetailClicked() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.AD_DETAIL
                    )
                )
            )
        )
    }

    fun trackAdEditClicked() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.AD_EDIT
                    )
                )
            )
        )
    }


    private fun trackSelectStatus(myAccountAdStatus: MyAccountAdStatus) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ACCOUNT_ADS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.SELECT_STATUS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VALUE,
                        value = myAccountAdStatus.getAnalyticsName()
                    )
                )
            )
        )
    }

}

