package se.scmv.morocco.account.presentation.bookmarks.searches

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.collectLatest
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.format
import kotlinx.datetime.format.MonthNames
import kotlinx.datetime.format.char
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.ui.localizedMonthNames
import kotlin.random.Random

@Composable
fun AccountBookmarkedSearchesRoute(
    modifier: Modifier = Modifier,
    viewModel: AccountBookmarkedSearchesViewModel = hiltViewModel(),
    // TODO We should move this to the app state once all the app in compose !
    navigateToHome: () -> Unit,
    applySearch: (BookmarkedSearch) -> Unit
) {
    val state = viewModel.viewState.collectAsStateWithLifecycle().value
    var searchToUnBookmark by remember { mutableStateOf<BookmarkedSearch?>(null) }

    AccountBookmarkedSearchesScreen(
        modifier = modifier.fillMaxSize(),
        state = state,
        onEmptyStateActionClicked = {},
        onRefresh = viewModel::onRefresh,
        onSearchClicked = applySearch,
        onDeleteSearch = { search -> searchToUnBookmark = search }
    )
    if (searchToUnBookmark != null) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.bookmarked_searches_screen_delete_search),
            description = stringResource(R.string.bookmarked_searches_screen_cancel_search_description),
            onConfirm = {
                searchToUnBookmark?.let { search ->
                    viewModel.onUnBookmarkSearch(search)
                    searchToUnBookmark = null
                }
            },
            onCancel = { searchToUnBookmark = null },
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(R.string.bookmarked_searches_screen_cancel_search_processing))
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            showProgressBar = it.isLoading
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.BOOKMARKED_SEARCH)
}

@Composable
private fun AccountBookmarkedSearchesScreen(
    modifier: Modifier = Modifier,
    state: AccountBookmarkedSearchesViewState,
    onEmptyStateActionClicked: () -> Unit,
    onRefresh: () -> Unit,
    onSearchClicked: (BookmarkedSearch) -> Unit,
    onDeleteSearch: (BookmarkedSearch) -> Unit,
) {
    val context = LocalContext.current
    with(state) {
        when {
            // Empty state
            loading.not() && searches.isEmpty() && errorMessage == null -> ScreenEmptyState(
                modifier = modifier,
                image = R.drawable.img_empty_bookmarked_searches,
                title = R.string.bookmarked_searches_screen_empty_state_title,
                description = R.string.bookmarked_searches_screen_empty_state_description,
                actionText = R.string.common_search,
                onActionClicked = onEmptyStateActionClicked
            )

            // Error state
            loading.not() && searches.isEmpty() && errorMessage != null -> ScreenErrorState(
                modifier = modifier,
                title = stringResource(R.string.common_oups),
                description = errorMessage.getValue(context),
                actionText = stringResource(R.string.common_refresh),
                onActionClicked = onRefresh
            )

            // Success state
            else -> AccountBookmarkedSearchList(
                modifier = modifier,
                searches = searches,
                loading = loading,
                onRefresh = onRefresh,
                onSearchClicked = onSearchClicked,
                onDeleteSearch = onDeleteSearch
            )
        }
    }
}

@Preview
@Composable
private fun AccountBookmarkedSearchesScreenPreview() {
    AvitoTheme {
        Surface {
            AccountBookmarkedSearchesScreen(
                state = AccountBookmarkedSearchesViewState(
                    searches = List(30) {
                        BookmarkedSearch(
                            id = it,
                            label = "Saved search $it",
                            imageUrL = null,
                            date = LocalDateTime(
                                year = 2000 + it,
                                monthNumber = Random.nextInt(1, 12),
                                dayOfMonth = Random.nextInt(1, 30),
                                hour = Random.nextInt(1, 24),
                                minute = Random.nextInt(0, 60)
                            ),
                            searchQuery = "an"
                        )
                    }.toImmutableList(),
                    loading = false,
                    errorMessage = null
                ),
                onEmptyStateActionClicked = {},
                onRefresh = {},
                onSearchClicked = {},
                onDeleteSearch = {}
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AccountBookmarkedSearchList(
    modifier: Modifier = Modifier,
    searches: ImmutableList<BookmarkedSearch>,
    loading: Boolean,
    onRefresh: () -> Unit,
    onSearchClicked: (BookmarkedSearch) -> Unit,
    onDeleteSearch: (BookmarkedSearch) -> Unit,
) {
    PullToRefreshBox(
        modifier = modifier
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal),
        isRefreshing = loading,
        onRefresh = onRefresh
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
            contentPadding = PaddingValues(bottom = MaterialTheme.dimens.extraExtraBig)
        ) {
            items(searches) { search ->
                BookmarkedSearch(
                    modifier = Modifier.fillMaxWidth(),
                    search = search,
                    onClick = { onSearchClicked(search) },
                    onDeleteSearch = { onDeleteSearch(search) }
                )
            }
        }
    }
}

@Composable
private fun BookmarkedSearch(
    modifier: Modifier = Modifier,
    search: BookmarkedSearch,
    onClick: () -> Unit,
    onDeleteSearch: () -> Unit
) {
    val context = LocalContext.current
    Card(
        modifier = Modifier.clickable(onClick = onClick)
    ) {
        Row(
            modifier = modifier.padding(MaterialTheme.dimens.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AsyncImage(
                modifier = Modifier
                    .width(MaterialTheme.dimens.extraBig)
                    .aspectRatio(1f),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(search.imageUrL)
                    .decoderFactory(SvgDecoder.Factory())
                    .build(),
                contentDescription = null,
                placeholder = painterResource(R.drawable.img_no_image_placeholder),
                error = painterResource(R.drawable.ic_arrow_right),
                onError = {
                    it.result.throwable.printStackTrace()
                },
                contentScale = ContentScale.Crop
            )
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = search.label,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = search.date.format(
                        LocalDateTime.Format {
                            dayOfMonth()
                            char(' ')
                            monthName(names = MonthNames.localizedMonthNames(context))
                            char(' ')
                            year()
                            char(' ')
                            chars(context.getString(R.string.common_at))
                            char(' ')
                            hour()
                            char(':')
                            minute()
                        }
                    ),
                    style = MaterialTheme.typography.bodySmall
                )
            }
            IconButton(onClick = onDeleteSearch) {
                Icon(
                    imageVector = Icons.Filled.Delete,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

