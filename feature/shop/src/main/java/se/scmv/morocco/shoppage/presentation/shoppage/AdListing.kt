package se.scmv.morocco.shoppage.presentation.shoppage

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.itemContentType
import androidx.paging.compose.itemKey
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.EmptyNoResultsScreen
import se.scmv.morocco.designsystem.components.IndeterminateLoading
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.shop.R
import se.scmv.morocco.shoppage.presentation.primaryFilter.PrimaryFilterRoute

@OptIn(ExperimentalFoundationApi::class)
@SuppressLint("SuspiciousIndentation")
@Composable
fun AdListing(
    modifier: Modifier = Modifier,
    storeProfileInfo: StoreProfileInfo,
    storeListingAds: LazyPagingItems<ListingAd.Published>,
    lazyListState: LazyListState,
    isFavorites: (ListingAd.Published) -> Boolean,
    showSnackBar: (Int, Color) -> Unit,
    onFavoriteClick : (ListingAd.Published, Boolean) -> Unit,
    navigateToAdView: (String) -> Unit,
    onCategoryChange: (String) -> Unit,
    updateFilters: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?) -> Unit,
    onItemSelected: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?, String, String) -> Unit,
    onEmptyStateActionClicked: () -> Unit
) {

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val indexState = remember { mutableStateOf(0) }
    LazyColumn(
        state = lazyListState,
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            StoreInformationItem(
                isVerifiedSeller = storeProfileInfo.isVerifiedSeller,
                storeProfileInfo = storeProfileInfo,
                onShareClick = {
                    shareUrl(context = context, storeId = storeProfileInfo.id)
                }
            )
        }
        stickyHeader {
                storeProfileInfo.let {
                    if (it.adFiltersAllowed){
                        PrimaryFilterRoute(
                            categoryId = it.categoryId,
                            adType = it.adType,
                            onCategoryChange = {primaryFilters, allFilters, id ->
                                onCategoryChange(id)
                                updateFilters(primaryFilters, allFilters)
                            }
                        ) { filterItems, allFilters, itemId ->
                            onItemSelected(filterItems, allFilters,it.categoryId, itemId)
                            scope.launch {
                                indexState.value = lazyListState.firstVisibleItemIndex
                                lazyListState.animateScrollToItem(indexState.value)
                            }
                        }
                    }
                }
        }
        when {
            storeListingAds.loadState.refresh is LoadState.Loading -> item { IndeterminateLoading() }
            storeListingAds.loadState.append.endOfPaginationReached && storeListingAds.itemCount == 0 -> item {
                EmptyNoResultsScreen(onSearchClick = onEmptyStateActionClicked)
            }
            else -> items(
                count = storeListingAds.itemCount,
                key = storeListingAds.itemKey { it.id },
            contentType = storeListingAds.itemContentType()
        ) { index ->
            val item = storeListingAds[index]
            item?.let { it ->
                ListingCard(
                        listingAd = it,
                        isInFavorites = isFavorites(it),
                        onFavoriteClick = { listingAd, isFavorite ->
                            onFavoriteClick(listingAd, isFavorite)
                        }
                    ) {
                        navigateToAdView(it)
                    }
                }
            }
        }

        storeListingAds.apply {
            when{
                loadState.append is LoadState.Loading -> {
                    item { IndeterminateLoading() }
                }
                loadState.refresh is LoadState.Error -> {
                    showSnackBar(R.string.common_network_error_verify_and_try_later, Color.Red)
                }
                loadState.append is LoadState.Error -> {
                    showSnackBar(R.string.common_network_error_verify_and_try_later, Color.Red)
                }
            }
        }
    }
}

fun shareUrl(context: Context, storeId: String) {
    val sendIntent = Intent().apply {
        action = Intent.ACTION_SEND
        putExtra(Intent.EXTRA_TEXT, "https://www.avito.ma/fr/boutique?id=$storeId")
        type = "text/plain"
    }
    val shareIntent = Intent.createChooser(sendIntent,
        context.getString(R.string.share_choser_title))
    context.startActivity(shareIntent)
}