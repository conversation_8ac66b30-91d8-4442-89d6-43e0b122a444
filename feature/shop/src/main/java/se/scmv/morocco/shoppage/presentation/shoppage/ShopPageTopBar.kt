package se.scmv.morocco.shoppage.presentation.shoppage

import androidx.compose.foundation.Image
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import se.scmv.morocco.shop.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShopPageTopBar(
    onBackClick: () -> Unit
) {

    TopAppBar(
        modifier = Modifier.shadow(
            elevation = 5.dp
        ),
        colors = TopAppBarDefaults.largeTopAppBarColors(
            containerColor = MaterialTheme.colorScheme.background
        ),
        title = {
            Image(
                painter = painterResource(R.drawable.avito_logo),
                contentDescription = null
            )
        },
        navigationIcon = {
            IconButton(
                onClick = {
                    onBackClick()
                }) {
                Icon(
                    imageVector = Icons.AutoMirrored.Default.ArrowBack,
                    contentDescription = null
                )
            }
        }
    )
}