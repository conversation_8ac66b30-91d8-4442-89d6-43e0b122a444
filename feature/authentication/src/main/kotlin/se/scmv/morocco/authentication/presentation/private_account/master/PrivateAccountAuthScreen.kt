package se.scmv.morocco.authentication.presentation.private_account.master

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.presentation.common.AuthScreenHeader
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.authentication.presentation.private_account.signin.PrivateAccountSignInRoute
import se.scmv.morocco.authentication.presentation.private_account.signup.phone.PrivateAccountSignUpRoute
import se.scmv.morocco.designsystem.components.AvTabs
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun PrivateAccountAuthRoute(
    modifier: Modifier = Modifier,
    navigateBack: () -> Unit,
    navigateToStoreSignIn: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateToHome: (LoginType, String) -> Unit,
    navigateToOtpValidation: (String) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    val pagerState = rememberPagerState(
        pageCount = { PrivateAccountAuthPages.entries.size }
    )
    val coroutineScope = rememberCoroutineScope()
    Scaffold(
        modifier = modifier,
        topBar = {
            AuthScreenHeader(onBackClicked = navigateBack)
        }
    ) { padding ->
        Column(
            modifier = Modifier.padding(padding)
        ) {
            AvTabs(
                tabs = privateAccountAuthTabs,
                selectedIndex = pagerState.currentPage,
                onTabClicked = { tabIndex ->
                    coroutineScope.launch { pagerState.animateScrollToPage(tabIndex) }
                }
            )
            HorizontalPager(
                modifier = Modifier.fillMaxSize(),
                state = pagerState,
                contentPadding = PaddingValues(top = MaterialTheme.dimens.extraBig)
            ) {
                when (it) {
                    PrivateAccountAuthPages.SIGN_IN.ordinal -> PrivateAccountSignInRoute(
                        navigateToStoreSignIn = navigateToStoreSignIn,
                        navigateToResetPassword = navigateToResetPassword,
                        navigateToHome = navigateToHome
                    )

                    PrivateAccountAuthPages.SIGN_UP.ordinal -> PrivateAccountSignUpRoute(
                        navigateToOtpValidation = navigateToOtpValidation,
                        navigateToWebViewScreen = navigateToWebViewScreen
                    )
                }
            }
        }
    }
}


