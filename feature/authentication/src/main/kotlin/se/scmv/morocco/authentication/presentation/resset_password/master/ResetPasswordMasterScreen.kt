package se.scmv.morocco.authentication.presentation.resset_password.master

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.AuthScreenHeader
import se.scmv.morocco.authentication.presentation.private_account.master.PrivateAccountAuthPages
import se.scmv.morocco.authentication.presentation.resset_password.private_account.phone.ResetPasswordPhoneNumberRoute
import se.scmv.morocco.authentication.presentation.resset_password.shop_account.ResetPasswordEmailRoute
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ResetPasswordMasterRoute(
    modifier: Modifier = Modifier,
    navigateBack: () -> Unit,
    navigateToOtpValidation: (String) -> Unit,
    navigateToEmailSuccess: (String) -> Unit
) {
    val pagerState = rememberPagerState(
        pageCount = { PrivateAccountAuthPages.entries.size }
    )
    val coroutineScope = rememberCoroutineScope()
    Scaffold(
        modifier = modifier,
        topBar = {
            AuthScreenHeader(onBackClicked = navigateBack)
        }
    ) { padding ->
        Column(
            modifier = Modifier.padding(padding),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
                    .padding(top = MaterialTheme.dimens.extraBig),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
            ) {
                AvScreenTitle(title = R.string.reset_password_master_screen_title)
                ResetPasswordTabs(
                    selectedTabIndex = pagerState.currentPage,
                    onTabClicked = { tabIndex ->
                        coroutineScope.launch { pagerState.animateScrollToPage(tabIndex) }
                    }
                )
            }
            HorizontalPager(
                modifier = Modifier.fillMaxSize(),
                state = pagerState,
                verticalAlignment = Alignment.Top
            ) {
                when (it) {
                    ResetPasswordPages.PRIVATE.ordinal -> ResetPasswordPhoneNumberRoute(
                        navigateToOtpValidation = navigateToOtpValidation
                    )

                    ResetPasswordPages.SHOP.ordinal -> ResetPasswordEmailRoute(
                        navigateToSuccess = navigateToEmailSuccess
                    )
                }
            }
        }
    }
}

@Composable
private fun ResetPasswordTabs(
    selectedTabIndex: Int,
    onTabClicked: (Int) -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTabIndex,
        divider = {}
    ) {
        ResetPasswordTab(
            text = R.string.reset_password_master_screen_private,
            selected = selectedTabIndex == PrivateAccountAuthPages.SIGN_IN.ordinal,
            onClick = {
                onTabClicked(PrivateAccountAuthPages.SIGN_IN.ordinal)
            }
        )
        ResetPasswordTab(
            text = R.string.reset_password_master_screen_shop,
            selected = selectedTabIndex == PrivateAccountAuthPages.SIGN_UP.ordinal,
            onClick = {
                onTabClicked(PrivateAccountAuthPages.SIGN_UP.ordinal)
            }
        )
    }
}

@Composable
private fun ResetPasswordTab(
    @StringRes text: Int,
    selected: Boolean,
    onClick: () -> Unit
) {
    Tab(
        text = {
            Text(
                text = stringResource(id = text),
                style = MaterialTheme.typography.bodyLarge,
            )
        },
        selected = selected,
        selectedContentColor = MaterialTheme.colorScheme.onBackground,
        unselectedContentColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f),
        onClick = onClick
    )
}