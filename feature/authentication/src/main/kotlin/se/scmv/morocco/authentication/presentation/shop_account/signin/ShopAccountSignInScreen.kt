package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.AuthScreenHeader
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvSecondaryButton
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ShopAccountSignInRoute(
    modifier: Modifier = Modifier,
    viewModel: ShopAccountSignInViewModel = hiltViewModel(),
    navigateToStoreSignUp: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateBack: () -> Unit,
    navigateToHome: (String) -> Unit,
) {
    Scaffold(
        modifier = modifier,
        topBar = { AuthScreenHeader(onBackClicked = navigateBack) },
    ) {
        val state = viewModel.viewState.collectAsState().value
        ShopAccountSignInScreen(
            modifier = Modifier.padding(it),
            state = state,
            onEmailChanged = viewModel::onEmailChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordForgottenClicked = navigateToResetPassword,
            onSubmit = viewModel::onSubmit,
            onGoToStoreSignInBtnClicked = navigateToStoreSignUp
        )
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collect { navigateToHome(it.token) }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.LOGIN,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP
            )
        )
    )
}

@Composable
fun ShopAccountSignInScreen(
    modifier: Modifier = Modifier,
    state: ShopAccountSignInViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmit: () -> Unit,
    onGoToStoreSignInBtnClicked: () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(vertical = MaterialTheme.dimens.screenPaddingVertical)
    ) {
        ShopAccountSignInForm(
            modifier = modifier,
            state = state,
            onEmailChanged = onEmailChanged,
            onPasswordChanged = onPasswordChanged,
            onPasswordForgottenClicked = onPasswordForgottenClicked,
            onSubmit = onSubmit,
        )
        AvSecondaryButton(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            text = stringResource(R.string.shop_account_sign_in_screen_go_to_shop_sign_up),
            onClick = onGoToStoreSignInBtnClicked
        )
    }
}

@Preview
@Composable
private fun ShopAccountSignInScreenPreview() {
    var state by remember { mutableStateOf(ShopAccountSignInViewState()) }
    AvitoTheme {
        Surface {
            ShopAccountSignInScreen(
                state = state,
                onEmailChanged = {
                    state = state.copy(email = it)
                },
                onPasswordChanged = {
                    state = state.copy(password = it)
                },
                onPasswordForgottenClicked = {},
                onSubmit = {
                    state = state.copy(loading = true)
                },
                onGoToStoreSignInBtnClicked = {}
            )
        }
    }
}

@Composable
fun ShopAccountSignInForm(
    modifier: Modifier = Modifier,
    state: ShopAccountSignInViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.dimens.big),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.shop_account_sign_in_screen_title)
            AvScreenSubTitle(title = R.string.shop_account_sign_in_screen_subtitle)
        }
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_EMAIL),
            value = state.email,
            onValueChanged = onEmailChanged,
            title = R.string.common_email_field_label,
            placeholder = R.string.common_email_field_placeholder,
            error = state.emailError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            )
        )
        AvPasswordField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PASSWORD),
            value = state.password,
            onValueChanged = onPasswordChanged,
            title = R.string.common_password_field_label,
            placeholder = R.string.common_password_field_placeholder,
            error = state.passwordError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.dimens.medium),
            horizontalAlignment = Alignment.End
        ) {
            Text(
                modifier = Modifier.clickable(onClick = onPasswordForgottenClicked),
                text = stringResource(id = R.string.shop_account_sign_in_screen_password_forgotten),
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Medium
            )
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.shop_account_sign_in_screen_submit_button),
            loading = state.loading,
            onClick = {
                focusManager.clearFocus()
                onSubmit()
            }
        )
    }
}