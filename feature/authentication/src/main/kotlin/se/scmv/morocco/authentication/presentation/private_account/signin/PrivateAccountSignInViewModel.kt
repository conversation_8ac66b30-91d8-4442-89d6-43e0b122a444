package se.scmv.morocco.authentication.presentation.private_account.signin

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class PrivateAccountSignInViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(PrivateAccountSignInViewState())
    val viewState = _viewState.asStateFlow()
    private var loginType: LoginType = LoginType.EMAIL

    private val _oneTimeEvents = MutableSharedFlow<PrivateAccountSignInSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailPhoneChanged(emailOrPhone: String) {
        _viewState.update { it.copy(emailOrPhone = emailOrPhone, emailOrPhoneError = null) }
    }

    fun onPasswordChanged(password: String) {
        _viewState.update { it.copy(password = password, passwordError = null) }
    }

    fun onSubmit() {
        clearErrors()
        if (validateForm().not()) return

        showLoading()
        val (emailOrPhone, _, password) = _viewState.value
        loginType = if (isEmail(emailOrPhone)) LoginType.EMAIL else LoginType.PHONE
        viewModelScope.launch {
            val result = authenticationRepository.signIn(emailOrPhone, password)
            hideLoading()
            updateForResult(result)
        }
    }

    fun onSignInWithGoogle(googleIdToken: String) {
        viewModelScope.launch {
            loginType = LoginType.GMAIL
            showLoading()
            val result = authenticationRepository.signInWithGoogle(googleIdToken)
            hideLoading()
            updateForResult(result)
        }
    }

    // ------------- Email/Phone Password login -------------------
    private fun validateForm(): Boolean {
        val (emailOrPhone, _, password) = _viewState.value

        when {
            emailOrPhone.isBlank() -> R.string.private_account_sign_in_screen_email_or_phone_field_required

            isPhone(emailOrPhone) && credentialsValidator.validatePhone(emailOrPhone).not() -> {
                R.string.common_phone_number_field_format_invalid
            }

            isEmail(emailOrPhone) && credentialsValidator.validateEmail(emailOrPhone).not() -> {
                R.string.common_email_field_format_invalid
            }

            else -> null
        }?.let { error ->
            _viewState.update { it.copy(emailOrPhoneError = UiText.FromRes(error)) }
            return false
        }

        if (credentialsValidator.validatePassword(password).not()) {
            _viewState.update {
                it.copy(passwordError = UiText.FromRes(R.string.common_password_field_required))
            }
            return false
        }

        return true
    }

    private fun isEmail(emailOrPhone: String): Boolean {
        return emailOrPhone.any { it.isDigit().not() } || emailOrPhone.contains("@")
    }

    private fun isPhone(emailOrPhone: String): Boolean {
        return emailOrPhone.all { it.isDigit() }
    }

    private fun clearErrors() {
        _viewState.update { it.copy(emailOrPhoneError = null, passwordError = null) }
    }

    // ------------- Google login -------------------
    /*private fun getIdToken(result: GetCredentialResponse): String? {
        return when (val credential = result.credential) {
            is CustomCredential -> {
                if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                    try {
                        // Use googleIdTokenCredential and extract id to validate and
                        // authenticate on your server.
                        GoogleIdTokenCredential.createFrom(credential.data).idToken
                    } catch (e: GoogleIdTokenParsingException) {
                        e.printStackTrace()
                        null
                    }
                } else {
                    Log.e("TAG---", "Unexpected type of credential")
                    null
                }
            }

            else -> {
                Log.e("TAG---", "Unexpected type of credential")
                null
            }
        }
    }*/

    // ------------- Common -------------------
    private suspend fun updateForResult(result: Resource<String, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(
                PrivateAccountSignInSuccessEvent(loginType = loginType, token = result.data)
            )

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}