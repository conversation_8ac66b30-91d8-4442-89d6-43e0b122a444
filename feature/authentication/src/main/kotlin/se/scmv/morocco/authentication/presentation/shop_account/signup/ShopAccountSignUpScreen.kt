package se.scmv.morocco.authentication.presentation.shop_account.signup

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.AuthScreenHeader
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvChipGroup
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTermOfServicesAndPrivacyPolicy
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ShopAccountSignUpRoute(
    modifier: Modifier = Modifier,
    viewModel: ShopAccountSignUpViewModel = hiltViewModel(),
    navigateToSuccess: () -> Unit,
    navigateBack: () -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    Scaffold(
        modifier = modifier,
        topBar = { AuthScreenHeader(onBackClicked = navigateBack) }
    ) {

        val state = viewModel.viewState.collectAsState().value
        ShopAccountSignUpScreen(
            modifier = Modifier.padding(it),
            state = state,
            onShopCategoryClicked = viewModel::onShopCategoryClicked,
            onShopSubscriptionClicked = viewModel::onShopSubscriptionClicked,
            onFullNameChanged = viewModel::onFullNameChanged,
            onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
            onEmailChanged = viewModel::onEmailChanged,
            onTosAndPpConfirmChanged = viewModel::onTosAndPpConfirmChanged,
            navigateToWebViewScreen = navigateToWebViewScreen,
            onSubmit = viewModel::onSubmit
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collect { navigateToSuccess() }
    }
}

@Composable
private fun ShopAccountSignUpScreen(
    modifier: Modifier = Modifier,
    state: ShopAccountSignUpViewState,
    onShopCategoryClicked: (ChipData) -> Unit,
    onShopSubscriptionClicked: (ChipData) -> Unit,
    onFullNameChanged: (String) -> Unit,
    onPhoneNumberChanged: (String) -> Unit,
    onEmailChanged: (String) -> Unit,
    onTosAndPpConfirmChanged: (Boolean) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = MaterialTheme.dimens.screenPaddingHorizontal,
                vertical = MaterialTheme.dimens.screenPaddingVertical
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        AvScreenTitle(title = R.string.shop_account_sign_up_screen_title)
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        AvChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = state.shopCategories,
            title = R.string.shop_account_sign_up_screen_shop_category_label,
            explanation = R.string.shop_category_explanation,
            required = true,
            error = state.shopCategoryError?.getValue(context),
            onChipClicked = onShopCategoryClicked
        )
        AvChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = state.shopSubscriptions,
            title = R.string.shop_account_sign_up_screen_shop_subscription_label,
            explanation = R.string.shop_subscription_explanation,
            required = true,
            error = state.shopSubscriptionError?.getValue(context),
            onChipClicked = onShopSubscriptionClicked
        )
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_FULL_NAME),
            value = state.fullName,
            onValueChanged = onFullNameChanged,
            required = true,
            title = R.string.common_full_name_field_label,
            placeholder = R.string.common_full_name_field_placeholder,
            error = state.fullNameError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Next,
                keyboardType = KeyboardType.Text
            )
        )
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PHONE_NUMBER),
            value = state.phoneNumber,
            onValueChanged = onPhoneNumberChanged,
            required = true,
            title = R.string.common_phone_number_field_label,
            placeholder = R.string.common_phone_number_field_placeholder,
            error = state.phoneNumberError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Next,
                keyboardType = KeyboardType.Phone
            )
        )
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_EMAIL),
            value = state.email,
            onValueChanged = onEmailChanged,
            required = true,
            title = R.string.common_email_field_label,
            placeholder = R.string.common_email_field_placeholder,
            error = state.emailError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done,
                keyboardType = KeyboardType.Email
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        )
        AvTermOfServicesAndPrivacyPolicy(
            checked = state.confirmTosAndPp,
            error = state.confirmTosAndPpError,
            onCheckedChange = onTosAndPpConfirmChanged,
            navigateToWebViewScreen = navigateToWebViewScreen
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.shop_account_sign_up_screen_submit_button),
            loading = state.loading,
            onClick = onSubmit
        )
    }
}

@Preview
@Composable
private fun ShopAccountSignUpScreenPreview() {
    AvitoTheme {
        Surface {
            ShopAccountSignUpScreen(
                state = ShopAccountSignUpViewState(
                    shopCategories = List(3) { ChipData("$it", "Chip$it", false) }.toPersistentList(),
                    shopSubscriptions = List(3) { ChipData("$it", "Chip$it", false) }.toPersistentList(),
                ),
                onShopCategoryClicked = {},
                onShopSubscriptionClicked = {},
                onFullNameChanged = {},
                onPhoneNumberChanged = {},
                onEmailChanged = {},
                onTosAndPpConfirmChanged = {},
                navigateToWebViewScreen = {_,_ ->},
                onSubmit = {}
            )
        }
    }
}