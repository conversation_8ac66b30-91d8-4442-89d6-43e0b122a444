package se.scmv.morocco.authentication.presentation.resset_password.private_account.update

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.AuthScreenHeader
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun PasswordResetRoute(
    modifier: Modifier = Modifier,
    viewModel: PasswordResetViewModel = hiltViewModel(),
    navigateBack: () -> Unit,
    navigateToPasswordResetSuccess: () -> Unit,
) {
    Scaffold(
        modifier = modifier,
        topBar = { AuthScreenHeader(onBackClicked = navigateBack) }
    ) {
        val state = viewModel.viewState.collectAsState().value
        PasswordResetScreen(
            modifier = Modifier.padding(it),
            state = state,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordConfirmChanged = viewModel::onPasswordConfirmChanged,
            onSubmit = viewModel::onSubmit,
        )
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collect { navigateToPasswordResetSuccess() }
    }
}

@Composable
private fun PasswordResetScreen(
    modifier: Modifier = Modifier,
    state: PasswordResetViewState,
    onPasswordChanged: (String) -> Unit,
    onPasswordConfirmChanged: (String) -> Unit,
    onSubmit: () -> Unit,
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(
                vertical = MaterialTheme.dimens.screenPaddingVertical,
                horizontal = MaterialTheme.dimens.screenPaddingHorizontal
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.password_reset_screen_title)
            AvScreenSubTitle(title = R.string.password_reset_screen_subtitle)
        }
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            AvPasswordField(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_PASSWORD),
                value = state.password,
                onValueChanged = onPasswordChanged,
                title = R.string.password_reset_screen_new_password_label,
                placeholder = R.string.common_password_field_placeholder,
                error = state.passwordError?.getValue(context),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
            )
            AvPasswordField(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_PASSWORD_CONFIRM),
                value = state.passwordConfirm,
                onValueChanged = onPasswordConfirmChanged,
                title = R.string.common_password_confirm_field_label,
                placeholder = R.string.common_password_confirm_field_placeholder,
                error = state.passwordConfirmError?.getValue(context),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        onSubmit()
                    }
                )
            )
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.common_continue),
            loading = state.loading,
            onClick = onSubmit
        )
    }
}

@Preview
@Composable
private fun PasswordResetScreenPreview() {
    AvitoTheme {
        Surface {
            PasswordResetScreen(
                state = PasswordResetViewState(),
                onPasswordChanged = {},
                onPasswordConfirmChanged = {},
                onSubmit = {},
            )
        }
    }
}