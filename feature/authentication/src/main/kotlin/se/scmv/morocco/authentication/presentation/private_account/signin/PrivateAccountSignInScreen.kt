package se.scmv.morocco.authentication.presentation.private_account.signin

import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.facebook.CallbackManager
import com.facebook.login.LoginManager
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.GoogleAuthUi
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.dimens
import java.util.Random

@Composable
fun PrivateAccountSignInRoute(
    modifier: Modifier = Modifier,
    viewModel: PrivateAccountSignInViewModel = hiltViewModel(),
    navigateToStoreSignIn: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateToHome: (LoginType, String) -> Unit,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val googleAuthUi = remember { GoogleAuthUi(context, Identity.getSignInClient(context)) }
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { result ->
        result.data?.let {
            val token = googleAuthUi.getGoogleIdToken(it) ?: return@let
            viewModel.onSignInWithGoogle(token)
        }
    }

    val facebookLoginManager = remember { LoginManager.getInstance() }
    val facebookCallbackManager = remember { CallbackManager.Factory.create() }
    val facebookLoginLauncher = rememberLauncherForActivityResult(
        contract = facebookLoginManager.createLogInActivityResultContract(
            callbackManager = facebookCallbackManager,
            loggerID = null
        ),
        onResult = {}
    )

    val state = viewModel.viewState.collectAsState().value
    var googleLoading by remember { mutableStateOf(false) }
    Column {
        DebugTopBar {
            viewModel.onEmailPhoneChanged(it.first)
            viewModel.onPasswordChanged(it.second)
        }
        PrivateAccountSignInScreen(
            modifier = modifier.fillMaxSize()
                .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
                .padding(top = MaterialTheme.dimens.medium)
                .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
            state = state,
            onEmailPhoneChanged = viewModel::onEmailPhoneChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordForgottenClicked = navigateToResetPassword,
            onSubmitBtnClicked = viewModel::onSubmit,
            onGoogleBtnClicked = {
                scope.launch {
                    googleLoading = true
                    val signInIntent = googleAuthUi.signIn()
                    googleLoading = false
                    if (signInIntent == null) {
                        Toast.makeText(
                            context,
                            R.string.common_unexpected_error_verify_and_try_later,
                            Toast.LENGTH_SHORT
                        ).show()
                        return@launch
                    }
                    googleSignInLauncher.launch(IntentSenderRequest.Builder(signInIntent).build())
                }
            },
            onFacebookBtnClicked = {
                facebookLoginLauncher.launch(listOf("email", "public_profile"))
            },
            onGoToStoreSignInBtnClicked = navigateToStoreSignIn
        )
    }
    if (googleLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            navigateToHome(it.loginType, it.token)
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.LOGIN,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun PrivateAccountSignInScreen(
    modifier: Modifier = Modifier,
    state: PrivateAccountSignInViewState,
    onEmailPhoneChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmitBtnClicked: () -> Unit,
    onGoogleBtnClicked: () -> Unit,
    onFacebookBtnClicked: () -> Unit,
    onGoToStoreSignInBtnClicked: () -> Unit
) {
    CompositionLocalProvider(
        LocalOverscrollConfiguration provides null
    ) {
        Column(
            modifier = modifier.verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            PrivateAccountSignInFrom(
                viewState = state,
                onEmailPhoneChanged = onEmailPhoneChanged,
                onPasswordChanged = onPasswordChanged,
                onPasswordForgottenClicked = onPasswordForgottenClicked,
                onSubmit = onSubmitBtnClicked
            )
            PrivateAccountSignInSocialMediaProviders(
                onGoogleBtnClicked = onGoogleBtnClicked
            )
            // TODO Remove all the shop signup/signin screen once we're sure that we wont need them.
            /*AvSecondaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.private_account_sign_in_screen_go_to_store_auth),
                onClick = onGoToStoreSignInBtnClicked
            )*/
        }
    }
}

@Preview
@Composable
private fun PrivateAccountSignInScreenPreview() {
    AvitoTheme {
        Surface {
            var state by remember {
                mutableStateOf(PrivateAccountSignInViewState())
            }
            PrivateAccountSignInScreen(
                state = state,
                onEmailPhoneChanged = {
                    state = state.copy(emailOrPhone = it, loading = false)
                },
                onPasswordChanged = {
                    state = state.copy(password = it, loading = false)
                },
                onPasswordForgottenClicked = {},
                onSubmitBtnClicked = {
                    state = state.copy(loading = true)
                },
                onGoogleBtnClicked = {},
                onFacebookBtnClicked = {},
                onGoToStoreSignInBtnClicked = {}
            )
        }
    }
}

@Composable
private fun PrivateAccountSignInFrom(
    viewState: PrivateAccountSignInViewState,
    onEmailPhoneChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.private_account_sign_in_screen_title)
            AvScreenSubTitle(title = R.string.private_account_sign_in_screen_subtitle)
        }
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_EMAIL_OR_PHONE),
                value = viewState.emailOrPhone,
                onValueChanged = onEmailPhoneChanged,
                title = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                placeholder = R.string.private_account_sign_in_screen_email_or_phone_field_placeholder,
                error = viewState.emailOrPhoneError?.getValue(context),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Next
                )
            )
            AvPasswordField(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_PASSWORD),
                value = viewState.password,
                onValueChanged = onPasswordChanged,
                title = R.string.common_password_field_label,
                placeholder = R.string.common_password_field_placeholder,
                error = viewState.passwordError?.getValue(context),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        onSubmit()
                    }
                )
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(MaterialTheme.dimens.extraBig),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    modifier = Modifier.clickable(onClick = onPasswordForgottenClicked),
                    text = stringResource(id = R.string.private_account_sign_in_screen_password_forgotten),
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium
                )
            }
            AvPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.private_account_sign_in_screen_submit_button),
                loading = viewState.loading,
                onClick = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        }
    }
}

@Composable
private fun PrivateAccountSignInSocialMediaProviders(
    onGoogleBtnClicked: () -> Unit,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.large),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            HorizontalDivider(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                color = Gray500,
                style = MaterialTheme.typography.bodySmall
            )
            HorizontalDivider(modifier = Modifier.weight(1f))
        }

            OutlinedButton(
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline),
                onClick = {
                    onGoogleBtnClicked()
                },
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_google),
                    contentDescription = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                    modifier = Modifier.size(MaterialTheme.dimens.big)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                Text(text = stringResource(R.string.private_account_sign_in_screen_login_with_google))
            }

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DebugTopBar(
    onCredentialsSelected: (Pair<String, String>) -> Unit
) {
    val logins = listOf(
        "<EMAIL>" to "123456",
        "<EMAIL>" to "123456",
        "<EMAIL>" to "**********",
        "<EMAIL>" to "Avito1234",
        "<EMAIL>" to "<EMAIL>",
        "<EMAIL>" to "kainosisnew",
        "<EMAIL>" to "**********",
        "<EMAIL>" to "*********",
    )

    TopAppBar(
        title = { },
        actions = {
            IconButton(
                onClick = {
                    val generator = Random()
                    val nextInt = generator.nextInt(logins.size)
                    onCredentialsSelected(logins[nextInt])
                }
            ) {
                Icon(imageVector = Icons.Default.Add, contentDescription = null)
            }
        }
    )
}