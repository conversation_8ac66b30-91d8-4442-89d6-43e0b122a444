package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class ShopAccountSignInViewState(
    val email: String = "",
    val emailError: UiText? = null,
    val password: String = "",
    val passwordError: UiText? = null,
    val loading: Boolean = false
)

@JvmInline
value class ShopAccountSignInSuccessEvent(val token: String)