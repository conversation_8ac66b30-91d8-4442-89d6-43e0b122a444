plugins {
    id("avito.android.feature")
}

android {
    namespace = "se.scmv.morocco.authentication"

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation(project(":feature:info-center"))

    implementation(libs.androidx.activity.compose)

    /*// Credential
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services.auth)
    implementation(libs.com.google.googleid)*/

    implementation(libs.com.google.android.gms.auth)
    implementation(libs.com.google.firebase.auth.ktx)

    // Facebook
    implementation(libs.com.facebook.sdk)
}